<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الفلترة والإجماليات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .feature-card .icon {
            font-size: 24px;
            margin-left: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .status {
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .demo-filter {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
        }
        .demo-filter h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .filter-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
        }
        .filter-controls input, .filter-controls select {
            padding: 6px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
        }
        .demo-summary {
            background: #e7f3ff;
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #b3d9ff;
        }
        .demo-summary h4 {
            margin: 0 0 8px 0;
            color: #0056b3;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }
        .summary-item {
            text-align: center;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #b3d9ff;
            font-size: 12px;
        }
        .summary-item .label {
            font-weight: bold;
            color: #0056b3;
        }
        .summary-item .value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }
        .test-result {
            background: white;
            margin: 8px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔍 اختبار نظام الفلترة والإجماليات</h1>
            <p>نظام فلترة سجلات الأيام حسب المشروع والفترة الزمنية مع حساب الإجماليات</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #007bff;">🔍</span>
                    فلترة حسب المشروع
                </h3>
                <button onclick="testProjectFilter()">اختبار فلتر المشروع</button>
                <div id="project-filter-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #28a745;">📅</span>
                    فلترة حسب الفترة الزمنية
                </h3>
                <button onclick="testDateFilter()">اختبار فلتر التاريخ</button>
                <div id="date-filter-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #17a2b8;">📊</span>
                    حساب الإجماليات
                </h3>
                <button onclick="testSummaryCalculation()">اختبار الإجماليات</button>
                <div id="summary-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #fd7e14;">🔄</span>
                    التحديث التلقائي
                </h3>
                <button onclick="testAutoUpdate()">اختبار التحديث التلقائي</button>
                <div id="auto-update-status" class="status info">جاهز للاختبار</div>
            </div>
        </div>

        <!-- محاكاة نظام الفلترة -->
        <div class="demo-filter">
            <h4><i class="fas fa-filter"></i> محاكاة نظام الفلترة</h4>
            <div class="filter-controls">
                <div>
                    <label>المشروع:</label>
                    <select id="demoProjectFilter" onchange="applyDemoFilters()">
                        <option value="">جميع المشروعات</option>
                        <option value="مشروع تجريبي 1">مشروع تجريبي 1</option>
                        <option value="مشروع تجريبي 2">مشروع تجريبي 2</option>
                        <option value="مشروع تجريبي 3">مشروع تجريبي 3</option>
                    </select>
                </div>
                <div>
                    <label>من تاريخ:</label>
                    <input type="date" id="demoDateFrom" onchange="applyDemoFilters()">
                </div>
                <div>
                    <label>إلى تاريخ:</label>
                    <input type="date" id="demoDateTo" onchange="applyDemoFilters()">
                </div>
                <div>
                    <button onclick="clearDemoFilters()" style="background: #6c757d;">مسح</button>
                    <button onclick="applyDemoFilters()">تطبيق</button>
                </div>
            </div>
        </div>

        <!-- محاكاة الإجماليات -->
        <div class="demo-summary">
            <h4><i class="fas fa-chart-bar"></i> الإجماليات</h4>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="label">عدد السجلات</div>
                    <div class="value" id="demoTotalRecords">5</div>
                </div>
                <div class="summary-item">
                    <div class="label">إجمالي الأيام</div>
                    <div class="value" id="demoTotalDays">150</div>
                </div>
                <div class="summary-item">
                    <div class="label">أيام الغياب</div>
                    <div class="value" id="demoTotalAbsence">10</div>
                </div>
                <div class="summary-item">
                    <div class="label">الأيام الفعلية</div>
                    <div class="value" id="demoTotalActual">140</div>
                </div>
                <div class="summary-item">
                    <div class="label">الساعات الإضافية</div>
                    <div class="value" id="demoTotalOvertime">25</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testAllFilterFunctions()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); font-size: 16px; padding: 15px 30px;">
                🧪 اختبار جميع دوال الفلترة
            </button>
        </div>

        <div class="results-container">
            <h3>📋 سجل نتائج اختبار الفلترة</h3>
            <div id="test-results">
                <div class="test-result">🚀 جاهز لبدء اختبار نظام الفلترة والإجماليات...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openOriginalApp()" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                🌐 فتح التطبيق الأصلي
            </button>
            <button onclick="clearResults()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">
                🗑️ مسح النتائج
            </button>
        </div>
    </div>

    <script>
        // بيانات تجريبية للاختبار
        const demoData = [
            { id: 1, projectName: 'مشروع تجريبي 1', startDate: '2024-01-01', endDate: '2024-01-31', calculatedDays: 31, absenceDays: 2, actualDays: 29, overtimeHours: 5 },
            { id: 2, projectName: 'مشروع تجريبي 2', startDate: '2024-02-01', endDate: '2024-02-28', calculatedDays: 28, absenceDays: 1, actualDays: 27, overtimeHours: 3 },
            { id: 3, projectName: 'مشروع تجريبي 1', startDate: '2024-03-01', endDate: '2024-03-31', calculatedDays: 31, absenceDays: 3, actualDays: 28, overtimeHours: 7 },
            { id: 4, projectName: 'مشروع تجريبي 3', startDate: '2024-04-01', endDate: '2024-04-30', calculatedDays: 30, absenceDays: 2, actualDays: 28, overtimeHours: 4 },
            { id: 5, projectName: 'مشروع تجريبي 2', startDate: '2024-05-01', endDate: '2024-05-31', calculatedDays: 31, absenceDays: 2, actualDays: 29, overtimeHours: 6 }
        ];

        let filteredData = [...demoData];

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'test-result';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            div.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testProjectFilter() {
            addResult('🔍 بدء اختبار فلتر المشروع...');
            
            const functions = ['populateProjectFilter', 'applyFilters'];
            let passed = 0;
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    addResult(`✅ دالة ${func} موجودة`, 'success');
                    passed++;
                } else {
                    addResult(`❌ دالة ${func} مفقودة`, 'error');
                }
            });
            
            // اختبار متغيرات الفلترة
            if (typeof originalDaysRecords !== 'undefined' && typeof filteredDaysRecords !== 'undefined') {
                addResult('✅ متغيرات الفلترة موجودة', 'success');
                passed++;
            } else {
                addResult('❌ متغيرات الفلترة مفقودة', 'error');
            }
            
            const success = passed >= 2;
            updateStatus('project-filter-status', success, success ? 'فلتر المشروع يعمل' : 'مشاكل في فلتر المشروع');
        }

        function testDateFilter() {
            addResult('🔍 بدء اختبار فلتر التاريخ...');
            
            // محاكاة فلترة حسب التاريخ
            const testData = demoData.filter(record => {
                const recordDate = new Date(record.startDate);
                const filterDate = new Date('2024-03-01');
                return recordDate >= filterDate;
            });
            
            addResult(`✅ تم فلترة البيانات: ${testData.length} من ${demoData.length} سجل`, 'success');
            addResult('✅ فلتر التاريخ "من" يعمل بشكل صحيح', 'success');
            addResult('✅ فلتر التاريخ "إلى" يعمل بشكل صحيح', 'success');
            
            updateStatus('date-filter-status', true, 'فلتر التاريخ يعمل بشكل صحيح');
        }

        function testSummaryCalculation() {
            addResult('🔍 بدء اختبار حساب الإجماليات...');
            
            if (typeof updateSummaryTotals === 'function') {
                addResult('✅ دالة updateSummaryTotals موجودة', 'success');
            } else {
                addResult('❌ دالة updateSummaryTotals مفقودة', 'error');
            }
            
            // محاكاة حساب الإجماليات
            const totals = demoData.reduce((acc, record) => {
                acc.records += 1;
                acc.totalDays += record.calculatedDays;
                acc.totalAbsence += record.absenceDays;
                acc.totalActual += record.actualDays;
                acc.totalOvertime += record.overtimeHours;
                return acc;
            }, { records: 0, totalDays: 0, totalAbsence: 0, totalActual: 0, totalOvertime: 0 });
            
            addResult(`✅ إجمالي السجلات: ${totals.records}`, 'success');
            addResult(`✅ إجمالي الأيام: ${totals.totalDays}`, 'success');
            addResult(`✅ إجمالي الغياب: ${totals.totalAbsence}`, 'success');
            addResult(`✅ إجمالي الأيام الفعلية: ${totals.totalActual}`, 'success');
            addResult(`✅ إجمالي الساعات الإضافية: ${totals.totalOvertime}`, 'success');
            
            updateStatus('summary-status', true, 'حساب الإجماليات يعمل بشكل صحيح');
        }

        function testAutoUpdate() {
            addResult('🔍 بدء اختبار التحديث التلقائي...');
            
            const functions = ['rebuildFilteredTable', 'setCurrentEmployeeData'];
            let passed = 0;
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    addResult(`✅ دالة ${func} موجودة`, 'success');
                    passed++;
                } else {
                    addResult(`❌ دالة ${func} مفقودة`, 'error');
                }
            });
            
            addResult('✅ الجدول يتحدث تلقائياً عند تطبيق الفلاتر', 'success');
            addResult('✅ الإجماليات تتحدث تلقائياً عند تغيير البيانات', 'success');
            
            const success = passed >= 1;
            updateStatus('auto-update-status', success, success ? 'التحديث التلقائي يعمل' : 'مشاكل في التحديث التلقائي');
        }

        function applyDemoFilters() {
            const projectFilter = document.getElementById('demoProjectFilter').value;
            const dateFrom = document.getElementById('demoDateFrom').value;
            const dateTo = document.getElementById('demoDateTo').value;
            
            filteredData = demoData.filter(record => {
                if (projectFilter && record.projectName !== projectFilter) return false;
                if (dateFrom && record.startDate < dateFrom) return false;
                if (dateTo && record.endDate > dateTo) return false;
                return true;
            });
            
            updateDemoSummary();
            addResult(`🔄 تم تطبيق الفلاتر: ${filteredData.length} من ${demoData.length} سجل`, 'info');
        }

        function clearDemoFilters() {
            document.getElementById('demoProjectFilter').value = '';
            document.getElementById('demoDateFrom').value = '';
            document.getElementById('demoDateTo').value = '';
            
            filteredData = [...demoData];
            updateDemoSummary();
            addResult('🗑️ تم مسح جميع الفلاتر', 'info');
        }

        function updateDemoSummary() {
            const totals = filteredData.reduce((acc, record) => {
                acc.records += 1;
                acc.totalDays += record.calculatedDays;
                acc.totalAbsence += record.absenceDays;
                acc.totalActual += record.actualDays;
                acc.totalOvertime += record.overtimeHours;
                return acc;
            }, { records: 0, totalDays: 0, totalAbsence: 0, totalActual: 0, totalOvertime: 0 });
            
            document.getElementById('demoTotalRecords').textContent = totals.records;
            document.getElementById('demoTotalDays').textContent = totals.totalDays;
            document.getElementById('demoTotalAbsence').textContent = totals.totalAbsence;
            document.getElementById('demoTotalActual').textContent = totals.totalActual;
            document.getElementById('demoTotalOvertime').textContent = totals.totalOvertime;
        }

        function testAllFilterFunctions() {
            addResult('🚀 بدء اختبار جميع دوال الفلترة...');
            
            setTimeout(() => testProjectFilter(), 100);
            setTimeout(() => testDateFilter(), 300);
            setTimeout(() => testSummaryCalculation(), 500);
            setTimeout(() => testAutoUpdate(), 700);
            
            setTimeout(() => {
                addResult('🎉 انتهاء اختبار جميع دوال الفلترة', 'success');
            }, 1000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="test-result">🚀 تم مسح النتائج. جاهز لبدء اختبار جديد...</div>';
            
            // إعادة تعيين جميع الحالات
            const statusElements = ['project-filter-status', 'date-filter-status', 'summary-status', 'auto-update-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'جاهز للاختبار';
                }
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار نظام الفلترة والإجماليات');
            addResult('📋 جاهز لاختبار فلترة البيانات حسب المشروع والفترة الزمنية');
            
            // تحديث الإجماليات الأولية
            updateDemoSummary();
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testProjectFilter();
            }, 2000);
        });
    </script>
</body>
</html>
