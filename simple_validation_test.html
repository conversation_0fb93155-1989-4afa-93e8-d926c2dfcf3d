<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط لمشكلة التحقق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button:hover {
            background: #218838;
        }
        
        .button.primary {
            background: #007bff;
        }
        
        .button.primary:hover {
            background: #0056b3;
        }
        
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .button.warning:hover {
            background: #e0a800;
        }
        
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .step {
            background: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        
        .step.success {
            border-left-color: #28a745;
        }
        
        .step.error {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار بسيط لمشكلة التحقق</h1>
            <p>اختبار سريع ومباشر لحل مشكلة "يرجى ملء باقي الحقول"</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="button primary" onclick="runQuickTest()">🚀 اختبار سريع</button>
            <button class="button" onclick="createEmployee()">👤 إنشاء موظف</button>
            <button class="button warning" onclick="openWindow()">📝 فتح النافذة</button>
            <button class="button" onclick="testSave()">💾 اختبار الحفظ</button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div id="steps"></div>

        <div class="console" id="console">
            [SIMPLE_TEST] اختبار بسيط لمشكلة التحقق جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function addStep(message, type = 'info') {
            const stepsDiv = document.getElementById('steps');
            const stepDiv = document.createElement('div');
            stepDiv.className = `step ${type}`;
            stepDiv.innerHTML = message;
            stepsDiv.appendChild(stepDiv);
        }

        function createEmployee() {
            addLog('إنشاء موظف تجريبي...');
            
            try {
                if (!employees.find(emp => emp.id === 123)) {
                    employees.push({
                        id: 123,
                        name: 'علي محمد - اختبار بسيط',
                        position: 'مطور',
                        employeeCode: 'SIMPLE123',
                        employmentType: 'monthly',
                        basicSalary: 5000
                    });
                    addStep('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    addLog('✓ تم إنشاء موظف تجريبي');
                    updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                } else {
                    addStep('⚠️ الموظف التجريبي موجود مسبقاً', 'info');
                    updateStatus('الموظف التجريبي موجود مسبقاً', 'info');
                }
            } catch (error) {
                addStep('❌ فشل في إنشاء الموظف: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في إنشاء الموظف: ' + error.message, 'error');
            }
        }

        function openWindow() {
            addLog('فتح نافذة حساب الأيام...');
            
            try {
                if (!employees.find(emp => emp.id === 123)) {
                    addStep('❌ الموظف التجريبي غير موجود - يرجى إنشاؤه أولاً', 'error');
                    updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                createEnhancedDaysCalculator(123);
                
                setTimeout(() => {
                    const modal = document.getElementById('enhancedDaysModal');
                    if (modal) {
                        addStep('✅ تم فتح نافذة حساب الأيام بنجاح', 'success');
                        addLog('✓ تم فتح النافذة بنجاح');
                        updateStatus('تم فتح النافذة - الآن املأ البيانات واختبر الحفظ', 'success');
                        
                        // ملء البيانات تلقائياً
                        fillTestData();
                    } else {
                        addStep('❌ فشل في فتح النافذة', 'error');
                        addLog('✗ فشل في فتح النافذة');
                        updateStatus('فشل في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addStep('❌ خطأ في فتح النافذة: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function fillTestData() {
            addLog('ملء البيانات التجريبية...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) return;
            
            setTimeout(() => {
                const projectSelect = modal.querySelector('#projectSelect');
                const monthSelect = modal.querySelector('#monthSelect');
                const yearSelect = modal.querySelector('#yearSelect');
                const calculatedDays = modal.querySelector('#calculatedDays');
                const absenceDays = modal.querySelector('#absenceDays');
                const overtimeHours = modal.querySelector('#overtimeHours');
                
                if (projectSelect) {
                    projectSelect.value = 'مشروع اختبار بسيط';
                    addLog('✓ تم ملء المشروع');
                }
                
                if (monthSelect) {
                    monthSelect.value = '1';
                    addLog('✓ تم ملء الشهر');
                }
                
                if (yearSelect) {
                    yearSelect.value = '2024';
                    addLog('✓ تم ملء السنة');
                }
                
                if (calculatedDays) {
                    calculatedDays.value = '30';
                    addLog('✓ تم ملء الأيام المحسوبة: 30');
                }
                
                if (absenceDays) {
                    absenceDays.value = '2';
                    addLog('✓ تم ملء أيام الغياب');
                }
                
                if (overtimeHours) {
                    overtimeHours.value = '5';
                    addLog('✓ تم ملء الساعات الإضافية');
                }
                
                addStep('✅ تم ملء جميع البيانات التجريبية', 'success');
                updateStatus('تم ملء البيانات - الآن يمكنك اختبار الحفظ', 'success');
            }, 200);
        }

        function testSave() {
            addLog('اختبار دالة الحفظ...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addStep('❌ النافذة غير مفتوحة - يرجى فتحها أولاً', 'error');
                updateStatus('يرجى فتح النافذة أولاً', 'error');
                return;
            }
            
            try {
                addLog('🔄 استدعاء saveDaysCalculationFast...');
                const result = saveDaysCalculationFast();
                
                if (result === true) {
                    addStep('🎉 نجح الحفظ! المشكلة تم حلها بنجاح', 'success');
                    addLog('✅ نجح الحفظ!');
                    updateStatus('🎉 ممتاز! تم حل المشكلة - الحفظ يعمل بشكل صحيح', 'success');
                } else if (result === false) {
                    addStep('❌ فشل الحفظ - تحقق من رسائل الخطأ في console', 'error');
                    addLog('❌ فشل الحفظ');
                    updateStatus('❌ فشل الحفظ - راجع console للتفاصيل', 'error');
                } else {
                    addStep('⚠️ نتيجة غير متوقعة من دالة الحفظ', 'error');
                    addLog('⚠️ نتيجة غير متوقعة: ' + result);
                    updateStatus('نتيجة غير متوقعة من دالة الحفظ', 'error');
                }
            } catch (error) {
                addStep('❌ خطأ في دالة الحفظ: ' + error.message, 'error');
                addLog('❌ خطأ: ' + error.message);
                updateStatus('خطأ في دالة الحفظ: ' + error.message, 'error');
            }
        }

        function runQuickTest() {
            updateStatus('🚀 بدء الاختبار السريع...', 'info');
            addLog('=== بدء الاختبار السريع ===');
            
            // مسح الخطوات السابقة
            document.getElementById('steps').innerHTML = '';
            
            // خطوة 1: إنشاء الموظف
            setTimeout(() => {
                createEmployee();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openWindow();
            }, 1500);
            
            // خطوة 3: اختبار الحفظ
            setTimeout(() => {
                testSave();
            }, 3500);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء الاختبار السريع ===');
                addStep('📋 انتهاء الاختبار - راجع النتائج أعلاه', 'info');
            }, 5000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل الاختبار البسيط');
            updateStatus('جاهز لبدء الاختبار البسيط', 'info');
        });
    </script>
</body>
</html>
