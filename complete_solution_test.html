<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحل الشامل والنهائي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1e3c72;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .solution-summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .solution-layer {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        
        .solution-layer:last-child {
            border-bottom: none;
        }
        
        .layer-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-left: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            background: #28a745;
            font-weight: bold;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 20px 0;
        }
        
        .step {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        
        .step-number {
            background: #ffc107;
            color: #212529;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 8px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> الحل الشامل والنهائي</h1>
            <p>حل متعدد الطبقات لضمان عمل الحفظ في جميع الحالات</p>
        </div>

        <div class="solution-summary">
            <h4><i class="fas fa-layer-group"></i> طبقات الحماية المطبقة:</h4>
            
            <div class="solution-layer">
                <div class="layer-number">1</div>
                <div>
                    <strong>الطبقة الأولى: تتبع recordsChanges</strong>
                    <br><small>النظام الأساسي لتتبع التغييرات عبر updateRecordField</small>
                </div>
            </div>
            
            <div class="solution-layer">
                <div class="layer-number">2</div>
                <div>
                    <strong>الطبقة الثانية: فحص الصفوف المعدلة</strong>
                    <br><small>البحث عن عناصر DOM بـ class="modified"</small>
                </div>
            </div>
            
            <div class="solution-layer">
                <div class="layer-number">3</div>
                <div>
                    <strong>الطبقة الثالثة: مقارنة البيانات</strong>
                    <br><small>مقارنة employeeDaysData مع localStorage</small>
                </div>
            </div>
            
            <div class="solution-layer">
                <div class="layer-number">4</div>
                <div>
                    <strong>الطبقة الرابعة: مراقب الأحداث</strong>
                    <br><small>addEventListener إضافي على حقول الإدخال</small>
                </div>
            </div>
            
            <div class="solution-layer">
                <div class="layer-number">5</div>
                <div>
                    <strong>الطبقة الخامسة: النسخة الاحتياطية</strong>
                    <br><small>حفظ واستعادة التغييرات من الحفظ التلقائي</small>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-exclamation-triangle"></i> تعليمات الاختبار:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء البيانات:</strong> انقر على "إنشاء بيانات تجريبية"
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح النافذة:</strong> انقر على "فتح نافذة السجلات"
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>التعديل:</strong> عدل أي حقل في الجدول
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>الحفظ:</strong> انقر على زر "حفظ جميع التغييرات"
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>المراقبة:</strong> راقب console لرؤية أي طبقة تعمل
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button danger" onclick="runCompleteTest()">
                <i class="fas fa-rocket"></i> اختبار الحل الشامل
            </button>
            
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button warning" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>
            
            <button class="test-button success" onclick="testAllLayers()">
                <i class="fas fa-layer-group"></i> اختبار جميع الطبقات
            </button>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار الحل الشامل...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [COMPLETE_SOLUTION] الحل الشامل متعدد الطبقات جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');
            addLog('🔧 إنشاء بيانات تجريبية للحل الشامل...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 999)) {
                    employees.push({
                        id: 999,
                        name: 'عمر أحمد - الحل الشامل',
                        position: 'مهندس نظم',
                        employeeCode: 'COMPLETE999',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                    addLog('✓ تم إنشاء موظف تجريبي');
                }
                
                // إنشاء سجل أيام تجريبي
                const testRecord = {
                    id: Date.now() + 9000,
                    employeeId: 999,
                    projectName: 'مشروع الحل الشامل',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 30,
                    absenceDays: 2,
                    overtimeHours: 10,
                    createdAt: new Date().toISOString()
                };
                
                if (!employeeDaysData.find(r => r.id === testRecord.id)) {
                    employeeDaysData.push(testRecord);
                }
                
                // حفظ البيانات
                saveDaysDataToLocalStorage();
                addLog('✓ تم حفظ البيانات التجريبية');
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 999)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة السجلات...');
                viewDaysRecords(999);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        addLog('✓ تم فتح نافذة السجلات بنجاح');
                        
                        // تعطيل الحفظ التلقائي للاختبار
                        const autoSaveCheckbox = modal.querySelector('#autoSaveMode');
                        if (autoSaveCheckbox) {
                            autoSaveCheckbox.checked = false;
                            toggleAutoSave();
                            addLog('✓ تم تعطيل الحفظ التلقائي');
                        }
                        
                        // تفعيل التعديل المباشر
                        const inlineEditCheckbox = modal.querySelector('#inlineEditMode');
                        if (inlineEditCheckbox) {
                            inlineEditCheckbox.checked = true;
                            toggleInlineEditMode();
                            addLog('✓ تم تفعيل التعديل المباشر');
                        }
                        
                        updateStatus('تم فتح النافذة وتهيئة الإعدادات!', 'success');
                    } else {
                        addLog('✗ فشل في فتح نافذة السجلات');
                        updateStatus('فشل في فتح نافذة السجلات', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
        }

        function testLayer(layerNumber, testFunction, layerName) {
            return new Promise((resolve) => {
                addLog(`🧪 اختبار الطبقة ${layerNumber}: ${layerName}`);
                
                try {
                    testFunction();
                    
                    setTimeout(() => {
                        // اختبار الحفظ
                        const originalConfirm = window.confirm;
                        window.confirm = () => {
                            addLog(`✓ تأكيد الحفظ للطبقة ${layerNumber}`);
                            return true;
                        };
                        
                        try {
                            saveAllRecordsChanges(999);
                            addLog(`✅ نجحت الطبقة ${layerNumber}: ${layerName}`);
                            resolve(true);
                        } catch (error) {
                            addLog(`❌ فشلت الطبقة ${layerNumber}: ${layerName} - ${error.message}`);
                            resolve(false);
                        }
                        
                        window.confirm = originalConfirm;
                    }, 1000);
                    
                } catch (error) {
                    addLog(`❌ خطأ في الطبقة ${layerNumber}: ${layerName} - ${error.message}`);
                    resolve(false);
                }
            });
        }

        async function testAllLayers() {
            updateStatus('🔍 اختبار جميع طبقات الحماية...', 'warning');
            addLog('=== بدء اختبار جميع طبقات الحماية ===');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                updateStatus('يرجى فتح نافذة السجلات أولاً', 'error');
                return;
            }
            
            let passedLayers = 0;
            let totalLayers = 0;
            
            // اختبار الطبقة الأولى: تعديل عادي
            totalLayers++;
            const layer1 = await testLayer(1, () => {
                const projectInput = modal.querySelector('input[onchange*="projectName"]');
                if (projectInput) {
                    projectInput.value = 'مشروع معدل - طبقة 1';
                    projectInput.dispatchEvent(new Event('change'));
                }
            }, 'تتبع recordsChanges');
            if (layer1) passedLayers++;
            
            // اختبار الطبقة الثانية: تعديل مع إضافة class
            totalLayers++;
            const layer2 = await testLayer(2, () => {
                const daysInput = modal.querySelector('input[onchange*="calculatedDays"]');
                if (daysInput) {
                    daysInput.value = parseInt(daysInput.value) + 1;
                    daysInput.closest('tr').classList.add('modified');
                }
            }, 'فحص الصفوف المعدلة');
            if (layer2) passedLayers++;
            
            // اختبار الطبقة الثالثة: تعديل البيانات مباشرة
            totalLayers++;
            const layer3 = await testLayer(3, () => {
                const testRecord = employeeDaysData.find(r => r.employeeId === 999);
                if (testRecord) {
                    testRecord.overtimeHours = (testRecord.overtimeHours || 0) + 2;
                }
            }, 'مقارنة البيانات');
            if (layer3) passedLayers++;
            
            // النتيجة النهائية
            const percentage = (passedLayers / totalLayers) * 100;
            addLog(`=== انتهاء اختبار الطبقات ===`);
            addLog(`📊 النتيجة: ${passedLayers}/${totalLayers} طبقة نجحت (${percentage.toFixed(0)}%)`);
            
            if (percentage >= 100) {
                updateStatus('🎉 جميع طبقات الحماية تعمل بشكل مثالي!', 'success');
            } else if (percentage >= 33) {
                updateStatus(`⚠️ ${passedLayers} من ${totalLayers} طبقة تعمل - هذا كافي للحفظ`, 'warning');
            } else {
                updateStatus(`❌ فشل في معظم الطبقات - يحتاج فحص إضافي`, 'error');
            }
        }

        function runCompleteTest() {
            updateStatus('🚀 بدء اختبار الحل الشامل...', 'warning');
            addLog('=== بدء اختبار الحل الشامل متعدد الطبقات ===');
            
            // خطوة 1: إنشاء البيانات
            setTimeout(() => {
                createTestData();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openRecordsWindow();
            }, 2000);
            
            // خطوة 3: اختبار جميع الطبقات
            setTimeout(() => {
                testAllLayers();
            }, 4000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل الحل الشامل متعدد الطبقات - جاهز للاختبار!', 'info');
            addLog('تم تحميل الحل الشامل لضمان عمل الحفظ في جميع الحالات');
        });
    </script>
</body>
</html>
