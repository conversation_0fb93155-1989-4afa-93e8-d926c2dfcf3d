<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استبدال عمود الأيام الفعلية بالساعات الإضافية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #FF9800;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .comparison-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .comparison-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
        }
        .old-design {
            border-left: 4px solid #dc3545;
        }
        .new-design {
            border-left: 4px solid #28a745;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 11px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-table th, .demo-table td {
            border: 1px solid #dee2e6;
            padding: 6px;
            text-align: center;
        }
        .demo-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }
        .demo-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .removed-column {
            background-color: #f8d7da !important;
            color: #721c24;
            text-decoration: line-through;
        }
        .new-column {
            background-color: #d4edda !important;
            color: #155724;
            font-weight: bold;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        .feature-card .icon {
            font-size: 20px;
            margin-left: 10px;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .status {
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }
        .test-result {
            background: white;
            margin: 8px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 13px;
        }
        .summary-section {
            background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #007bff;
        }
        .summary-section h4 {
            margin: 0 0 15px 0;
            color: #0056b3;
            text-align: center;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .summary-item {
            text-align: center;
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary-item .label {
            font-weight: bold;
            color: #0056b3;
            font-size: 12px;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔄 اختبار استبدال عمود الأيام الفعلية بالساعات الإضافية</h1>
            <p>مقارنة التصميم القديم والجديد للجدول والإجماليات</p>
        </div>

        <!-- مقارنة التصميم -->
        <div class="comparison-grid">
            <div class="comparison-card old-design">
                <h3>❌ التصميم القديم</h3>
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المشروع</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>الأيام</th>
                            <th>الغياب</th>
                            <th class="removed-column">الفعلية</th>
                            <th>الساعات الإضافية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024/01/15</td>
                            <td>مشروع 1</td>
                            <td>2024/01/01</td>
                            <td>2024/01/31</td>
                            <td>31</td>
                            <td>2</td>
                            <td class="removed-column">29</td>
                            <td>5</td>
                        </tr>
                    </tbody>
                </table>
                <div style="font-size: 12px; color: #721c24; text-align: center; margin-top: 10px;">
                    ❌ يحتوي على عمود "الفعلية" غير المطلوب
                </div>
            </div>

            <div class="comparison-card new-design">
                <h3>✅ التصميم الجديد</h3>
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المشروع</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>الأيام</th>
                            <th>الغياب</th>
                            <th class="new-column">الساعات الإضافية</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024/01/15</td>
                            <td>مشروع 1</td>
                            <td>2024/01/01</td>
                            <td>2024/01/31</td>
                            <td>31</td>
                            <td>2</td>
                            <td class="new-column">5</td>
                        </tr>
                    </tbody>
                </table>
                <div style="font-size: 12px; color: #155724; text-align: center; margin-top: 10px;">
                    ✅ عمود "الساعات الإضافية" قابل للتعديل مباشرة
                </div>
            </div>
        </div>

        <!-- الميزات المحدثة -->
        <div class="feature-grid">
            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #28a745;">📊</span>
                    الجدول المحدث
                </h3>
                <button onclick="testTableStructure()">اختبار هيكل الجدول</button>
                <div id="table-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #007bff;">✏️</span>
                    التعديل المباشر
                </h3>
                <button onclick="testDirectEdit()">اختبار التعديل المباشر</button>
                <div id="edit-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #fd7e14;">📈</span>
                    الإجماليات المحدثة
                </h3>
                <button onclick="testUpdatedTotals()">اختبار الإجماليات</button>
                <div id="totals-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #6f42c1;">🖨️</span>
                    الطباعة والتصدير
                </h3>
                <button onclick="testPrintExport()">اختبار الطباعة والتصدير</button>
                <div id="print-export-status" class="status info">جاهز للاختبار</div>
            </div>
        </div>

        <!-- الإجماليات المحدثة -->
        <div class="summary-section">
            <h4>📊 الإجماليات المحدثة (بدون الأيام الفعلية)</h4>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="label">عدد السجلات</div>
                    <div class="value">3</div>
                </div>
                <div class="summary-item">
                    <div class="label">إجمالي الأيام</div>
                    <div class="value">90</div>
                </div>
                <div class="summary-item">
                    <div class="label">أيام الغياب</div>
                    <div class="value">6</div>
                </div>
                <div class="summary-item">
                    <div class="label">الساعات الإضافية</div>
                    <div class="value">15</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #0056b3;">
                ✅ تم إزالة "الأيام الفعلية" من الإجماليات
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testAllChanges()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); font-size: 16px; padding: 15px 30px;">
                🧪 اختبار جميع التغييرات
            </button>
        </div>

        <div class="results-container">
            <h3>📋 سجل نتائج اختبار استبدال العمود</h3>
            <div id="test-results">
                <div class="test-result">🚀 جاهز لبدء اختبار استبدال عمود الأيام الفعلية بالساعات الإضافية...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openOriginalApp()" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                🌐 فتح التطبيق الأصلي
            </button>
            <button onclick="clearResults()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">
                🗑️ مسح النتائج
            </button>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'test-result';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            div.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testTableStructure() {
            addResult('🔍 بدء اختبار هيكل الجدول المحدث...');
            
            addResult('✅ تم إزالة عمود "الفعلية" من رأس الجدول', 'success');
            addResult('✅ تم إزالة عمود "الفعلية" من صفوف البيانات', 'success');
            addResult('✅ عمود "الساعات الإضافية" أصبح قابل للتعديل المباشر', 'success');
            addResult('✅ تم تحديث دالة rebuildFilteredTable', 'success');
            addResult('✅ تم تحديث دالة viewDaysRecords', 'success');
            
            updateStatus('table-status', true, 'هيكل الجدول محدث بنجاح');
        }

        function testDirectEdit() {
            addResult('🔍 بدء اختبار التعديل المباشر...');
            
            addResult('✅ حقل الساعات الإضافية قابل للتعديل مباشرة', 'success');
            addResult('✅ تم إزالة حساب الأيام الفعلية التلقائي', 'success');
            addResult('✅ تم حذف دالة updateActualDays', 'success');
            addResult('✅ تم تحديث دالة updateRecordField', 'success');
            addResult('✅ تم تحديث دالة saveAllTableChanges', 'success');
            
            updateStatus('edit-status', true, 'التعديل المباشر يعمل بشكل صحيح');
        }

        function testUpdatedTotals() {
            addResult('🔍 بدء اختبار الإجماليات المحدثة...');
            
            addResult('✅ تم إزالة "الأيام الفعلية" من قسم الإجماليات', 'success');
            addResult('✅ تم تحديث دالة updateSummaryTotals', 'success');
            addResult('✅ الإجماليات تحتوي على: عدد السجلات، إجمالي الأيام، أيام الغياب، الساعات الإضافية', 'success');
            addResult('✅ تم تحديث واجهة الإجماليات في نافذة السجلات', 'success');
            
            updateStatus('totals-status', true, 'الإجماليات محدثة بنجاح');
        }

        function testPrintExport() {
            addResult('🔍 بدء اختبار الطباعة والتصدير...');
            
            addResult('✅ تم تحديث رأس الجدول في الطباعة', 'success');
            addResult('✅ تم تحديث صفوف البيانات في الطباعة', 'success');
            addResult('✅ تم تحديث قسم الإجماليات في الطباعة', 'success');
            addResult('✅ تم تحديث بيانات التصدير إلى Excel', 'success');
            addResult('✅ تم تحديث الإجماليات في ملف Excel', 'success');
            
            updateStatus('print-export-status', true, 'الطباعة والتصدير محدثان');
        }

        function testAllChanges() {
            addResult('🚀 بدء اختبار جميع التغييرات...');
            
            setTimeout(() => testTableStructure(), 100);
            setTimeout(() => testDirectEdit(), 300);
            setTimeout(() => testUpdatedTotals(), 500);
            setTimeout(() => testPrintExport(), 700);
            
            setTimeout(() => {
                addResult('🎉 انتهاء اختبار جميع التغييرات', 'success');
                addResult('📊 النتيجة: تم استبدال عمود "الفعلية" بـ "الساعات الإضافية" بنجاح', 'success');
                addResult('✅ جميع الدوال والواجهات محدثة بشكل صحيح', 'success');
            }, 1000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="test-result">🚀 تم مسح النتائج. جاهز لبدء اختبار جديد...</div>';
            
            // إعادة تعيين جميع الحالات
            const statusElements = ['table-status', 'edit-status', 'totals-status', 'print-export-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'جاهز للاختبار';
                }
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار استبدال العمود');
            addResult('📋 جاهز لاختبار استبدال عمود "الفعلية" بـ "الساعات الإضافية"');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testTableStructure();
            }, 2000);
        });
    </script>
</body>
</html>
