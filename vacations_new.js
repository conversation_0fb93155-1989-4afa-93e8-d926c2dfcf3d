// نظام إدارة الإجازات - الإصدار الجديد

// المتغيرات العامة
let employees = [];
let vacationRecords = [];
let customHolidays = [];
let selectedEmployee = null;
let selectedVacationType = null;

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    loadEmployeesList();

    // إضافة بيانات تجريبية إذا لم تكن موجودة
    if (employees.length === 0) {
        addSampleData();
    }
});

// تحميل البيانات من localStorage
function loadData() {
    try {
        const savedEmployees = localStorage.getItem('vacations_employees');
        const savedRecords = localStorage.getItem('vacations_records');
        const savedHolidays = localStorage.getItem('vacations_holidays');

        employees = savedEmployees ? JSON.parse(savedEmployees) : [];
        vacationRecords = savedRecords ? JSON.parse(savedRecords) : [];
        customHolidays = savedHolidays ? JSON.parse(savedHolidays) : [];

        console.log(`تم تحميل ${employees.length} موظف و ${vacationRecords.length} سجل إجازة`);
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        employees = [];
        vacationRecords = [];
        customHolidays = [];
    }
}

// حفظ البيانات في localStorage
function saveData() {
    try {
        localStorage.setItem('vacations_employees', JSON.stringify(employees));
        localStorage.setItem('vacations_records', JSON.stringify(vacationRecords));
        localStorage.setItem('vacations_holidays', JSON.stringify(customHolidays));
        console.log('تم حفظ البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
    }
}

// إضافة بيانات تجريبية
function addSampleData() {
    const sampleEmployees = [
        { id: 1, name: 'أحمد محمد', position: 'مطور', type: 'monthly' },
        { id: 2, name: 'فاطمة علي', position: 'محاسبة', type: 'monthly' },
        { id: 3, name: 'محمد حسن', position: 'عامل', type: 'daily' }
    ];

    employees = sampleEmployees;
    saveData();
    loadEmployeesList();
    console.log('تم إضافة بيانات تجريبية');
}

// تحميل قائمة الموظفين
function loadEmployeesList() {
    const select = document.getElementById('employeeSelect');
    select.innerHTML = '<option value="">اختر الموظف</option>';

    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;
        option.textContent = `${employee.name} - ${employee.position}`;
        select.appendChild(option);
    });
}

// إضافة موظف جديد
function addEmployee() {
    const name = prompt('اسم الموظف:');
    if (!name || name.trim() === '') return;

    const position = prompt('المنصب:') || '';
    const type = confirm('هل الموظف شهري؟ (اضغط إلغاء للموظف اليومي)') ? 'monthly' : 'daily';

    const newEmployee = {
        id: Date.now(),
        name: name.trim(),
        position: position.trim(),
        type: type,
        createdAt: new Date().toISOString()
    };

    employees.push(newEmployee);
    saveData();
    loadEmployeesList();

    // تحديد الموظف الجديد
    document.getElementById('employeeSelect').value = newEmployee.id;
    selectEmployee();

    alert('تم إضافة الموظف بنجاح');
}

// اختيار موظف
function selectEmployee() {
    const employeeId = document.getElementById('employeeSelect').value;

    if (!employeeId) {
        selectedEmployee = null;
        hideAllSections();
        return;
    }

    selectedEmployee = employees.find(emp => emp.id == employeeId);
    if (!selectedEmployee) return;

    showEmployeeInfo();
    showVacationTypes();
    showVacationRecords();
    resetVacationForm();
}

// إخفاء جميع الأقسام
function hideAllSections() {
    document.getElementById('employeeInfo').classList.add('hidden');
    document.getElementById('vacationTypes').classList.add('hidden');
    document.getElementById('vacationForm').classList.add('hidden');
    document.getElementById('vacationRecords').classList.add('hidden');
}

// عرض معلومات الموظف
function showEmployeeInfo() {
    const infoDiv = document.getElementById('employeeInfo');
    const detailsDiv = document.getElementById('employeeDetails');

    detailsDiv.innerHTML = `
        <p><strong>الاسم:</strong> ${selectedEmployee.name}</p>
        <p><strong>المنصب:</strong> ${selectedEmployee.position || 'غير محدد'}</p>
        <p><strong>نوع التوظيف:</strong> ${selectedEmployee.type === 'monthly' ? 'شهري' : 'يومي'}</p>
    `;

    infoDiv.classList.remove('hidden');
}

// عرض أنواع الإجازات
function showVacationTypes() {
    document.getElementById('vacationTypes').classList.remove('hidden');
}

// اختيار نوع الإجازة
function selectVacationType(type) {
    selectedVacationType = type;

    // تحديث المظهر
    document.querySelectorAll('.vacation-type-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    event.target.closest('.vacation-type-btn').classList.add('selected');

    showVacationForm();
}

// عرض نموذج الإجازة
function showVacationForm() {
    if (!selectedEmployee || !selectedVacationType) return;

    const formDiv = document.getElementById('vacationForm');
    const contentDiv = document.getElementById('formContent');

    let formHTML = `
        <div class="form-row">
            <div class="form-group">
                <label>السنة:</label>
                <select id="vacationYear" class="form-control">
                    ${generateYearOptions()}
                </select>
            </div>
            <div class="form-group">
                <label>الأيام المستحقة:</label>
                <input type="number" id="vacationDays" class="form-control" min="1" max="365" placeholder="عدد الأيام">
            </div>
        </div>
    `;

    // إضافة حقول خاصة حسب نوع الإجازة
    if (selectedVacationType === 'annual') {
        formHTML += `
            <div class="form-group">
                <label>نوع الإجازة السنوية:</label>
                <select id="annualType" class="form-control">
                    <option value="">اختر النوع</option>
                    <option value="regular">اعتيادي</option>
                    <option value="emergency">عارضة</option>
                </select>
            </div>
        `;
    } else if (selectedVacationType === 'official') {
        formHTML += `
            <div class="form-group">
                <label>العطلة الرسمية:</label>
                <select id="officialHoliday" class="form-control" onchange="handleHolidayChange()">
                    <option value="">اختر العطلة</option>
                    <option value="new_year">رأس السنة الميلادية</option>
                    <option value="revolution">عيد الثورة</option>
                    <option value="labor_day">عيد العمال</option>
                    <option value="eid_fitr">عيد الفطر</option>
                    <option value="eid_adha">عيد الأضحى</option>
                    <option value="add_custom">إضافة عطلة مخصصة</option>
                </select>
            </div>

            <div id="customHolidayDiv" class="form-group hidden">
                <label>اسم العطلة المخصصة:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="customHolidayName" class="form-control" placeholder="أدخل اسم العطلة">
                    <button type="button" class="btn btn-success" onclick="addCustomHoliday()">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
        `;
    }

    formHTML += `
        <div class="form-group">
            <label>ملاحظات:</label>
            <textarea id="vacationNotes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."></textarea>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-success" onclick="saveVacationRecord()">
                <i class="fas fa-save"></i> حفظ الإجازة
            </button>
            <button class="btn btn-primary" onclick="resetVacationForm()">
                <i class="fas fa-times"></i> إلغاء
            </button>
        </div>
    `;

    contentDiv.innerHTML = formHTML;
    formDiv.classList.remove('hidden');

    // تحديث قائمة العطلات المخصصة
    updateCustomHolidaysDropdown();
}

// إنشاء خيارات السنوات
function generateYearOptions() {
    let options = '';
    const currentYear = new Date().getFullYear();

    for (let year = 2020; year <= 2035; year++) {
        const selected = year === currentYear ? 'selected' : '';
        options += `<option value="${year}" ${selected}>${year}</option>`;
    }

    return options;
}

// معالجة تغيير العطلة الرسمية
function handleHolidayChange() {
    const select = document.getElementById('officialHoliday');
    const customDiv = document.getElementById('customHolidayDiv');

    if (select.value === 'add_custom') {
        customDiv.classList.remove('hidden');
    } else {
        customDiv.classList.add('hidden');
    }
}

// إضافة عطلة مخصصة
function addCustomHoliday() {
    const nameInput = document.getElementById('customHolidayName');
    const name = nameInput.value.trim();

    if (!name) {
        alert('يرجى إدخال اسم العطلة');
        return;
    }

    // التحقق من عدم وجود العطلة مسبقاً
    if (customHolidays.find(h => h.name.toLowerCase() === name.toLowerCase())) {
        alert('هذه العطلة موجودة بالفعل');
        return;
    }

    const newHoliday = {
        id: Date.now(),
        name: name,
        value: `custom_${Date.now()}`,
        createdAt: new Date().toISOString()
    };

    customHolidays.push(newHoliday);
    saveData();

    // تحديث القائمة المنسدلة
    updateCustomHolidaysDropdown();

    // تحديد العطلة الجديدة
    document.getElementById('officialHoliday').value = newHoliday.value;

    // إخفاء حقل الإدخال ومسح النص
    document.getElementById('customHolidayDiv').classList.add('hidden');
    nameInput.value = '';

    alert('تم إضافة العطلة بنجاح');
}

// تحديث قائمة العطلات المخصصة
function updateCustomHolidaysDropdown() {
    const select = document.getElementById('officialHoliday');
    if (!select) return;

    // إزالة العطلات المخصصة الموجودة
    const customOptions = select.querySelectorAll('option[value^="custom_"]');
    customOptions.forEach(option => option.remove());

    // إضافة العطلات المخصصة الجديدة
    const addCustomOption = select.querySelector('option[value="add_custom"]');
    customHolidays.forEach(holiday => {
        const option = document.createElement('option');
        option.value = holiday.value;
        option.textContent = holiday.name;
        select.insertBefore(option, addCustomOption);
    });
}

// حفظ سجل الإجازة
function saveVacationRecord() {
    if (!selectedEmployee || !selectedVacationType) {
        alert('يرجى اختيار الموظف ونوع الإجازة');
        return;
    }

    const year = document.getElementById('vacationYear').value;
    const days = parseInt(document.getElementById('vacationDays').value);
    const notes = document.getElementById('vacationNotes').value;

    if (!year || !days || days <= 0) {
        alert('يرجى إدخال السنة وعدد الأيام');
        return;
    }

    // جمع البيانات الإضافية
    let additionalData = {};

    if (selectedVacationType === 'annual') {
        const annualType = document.getElementById('annualType').value;
        if (!annualType) {
            alert('يرجى اختيار نوع الإجازة السنوية');
            return;
        }
        additionalData.annualType = annualType;
    } else if (selectedVacationType === 'official') {
        const holiday = document.getElementById('officialHoliday').value;
        if (!holiday || holiday === 'add_custom') {
            alert('يرجى اختيار العطلة الرسمية');
            return;
        }
        additionalData.holidayType = holiday;
    }

    // إنشاء السجل الجديد
    const newRecord = {
        id: Date.now(),
        employeeId: selectedEmployee.id,
        employeeName: selectedEmployee.name,
        vacationType: selectedVacationType,
        year: parseInt(year),
        days: days,
        notes: notes,
        additionalData: additionalData,
        createdAt: new Date().toISOString()
    };

    vacationRecords.push(newRecord);
    saveData();
    showVacationRecords();
    resetVacationForm();

    alert('تم حفظ سجل الإجازة بنجاح');
}

// إعادة تعيين نموذج الإجازة
function resetVacationForm() {
    selectedVacationType = null;

    // إزالة التحديد من أزرار نوع الإجازة
    document.querySelectorAll('.vacation-type-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    // إخفاء النموذج
    document.getElementById('vacationForm').classList.add('hidden');
}

// عرض سجلات الإجازات
function showVacationRecords() {
    if (!selectedEmployee) return;

    const recordsDiv = document.getElementById('vacationRecords');
    const tableDiv = document.getElementById('recordsTable');

    // تصفية السجلات للموظف المحدد
    const employeeRecords = vacationRecords.filter(record =>
        record.employeeId === selectedEmployee.id
    );

    if (employeeRecords.length === 0) {
        tableDiv.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                لا توجد سجلات إجازات لهذا الموظف
            </div>
        `;
    } else {
        let tableHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>السنة</th>
                        <th>نوع الإجازة</th>
                        <th>التفاصيل</th>
                        <th>الأيام</th>
                        <th>ملاحظات</th>
                        <th>تاريخ التسجيل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        employeeRecords.forEach(record => {
            const vacationTypeText = getVacationTypeText(record.vacationType);
            const detailsText = getVacationDetailsText(record);
            const notesText = record.notes || '-';
            const dateText = new Date(record.createdAt).toLocaleDateString('ar-EG');

            tableHTML += `
                <tr>
                    <td><strong>${record.year}</strong></td>
                    <td>${vacationTypeText}</td>
                    <td>${detailsText}</td>
                    <td><span style="color: #28a745; font-weight: 600;">${record.days} يوم</span></td>
                    <td>${notesText}</td>
                    <td>${dateText}</td>
                    <td>
                        <button class="btn btn-primary btn-small" onclick="editRecord(${record.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteRecord(${record.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        tableDiv.innerHTML = tableHTML;
    }

    recordsDiv.classList.remove('hidden');
}

// الحصول على نص نوع الإجازة
function getVacationTypeText(type) {
    const types = {
        'annual': 'إجازة سنوية',
        'official': 'عطلة رسمية'
    };
    return types[type] || type;
}

// الحصول على تفاصيل الإجازة
function getVacationDetailsText(record) {
    if (!record.additionalData) return '-';

    if (record.additionalData.annualType) {
        return record.additionalData.annualType === 'regular' ? 'اعتيادي' : 'عارضة';
    }

    if (record.additionalData.holidayType) {
        const holidayNames = {
            'new_year': 'رأس السنة الميلادية',
            'revolution': 'عيد الثورة',
            'labor_day': 'عيد العمال',
            'eid_fitr': 'عيد الفطر',
            'eid_adha': 'عيد الأضحى'
        };

        if (record.additionalData.holidayType.startsWith('custom_')) {
            const customHoliday = customHolidays.find(h => h.value === record.additionalData.holidayType);
            return customHoliday ? customHoliday.name : 'عطلة مخصصة';
        }

        return holidayNames[record.additionalData.holidayType] || record.additionalData.holidayType;
    }

    return '-';
}

// تعديل السجل
function editRecord(recordId) {
    const record = vacationRecords.find(r => r.id === recordId);
    if (!record) {
        alert('السجل غير موجود');
        return;
    }

    // تحديد نوع الإجازة وإظهار النموذج
    selectedVacationType = record.vacationType;

    // تحديث المظهر
    document.querySelectorAll('.vacation-type-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    const targetBtn = document.querySelector(`[onclick="selectVacationType('${record.vacationType}')"]`);
    if (targetBtn) {
        targetBtn.classList.add('selected');
    }

    // إظهار النموذج
    showVacationForm();

    // ملء الحقول بالبيانات الموجودة
    setTimeout(() => {
        document.getElementById('vacationYear').value = record.year;
        document.getElementById('vacationDays').value = record.days;
        document.getElementById('vacationNotes').value = record.notes || '';

        if (record.additionalData) {
            if (record.additionalData.annualType) {
                document.getElementById('annualType').value = record.additionalData.annualType;
            }
            if (record.additionalData.holidayType) {
                document.getElementById('officialHoliday').value = record.additionalData.holidayType;
            }
        }

        // تغيير زر الحفظ للتحديث
        const saveBtn = document.querySelector('[onclick="saveVacationRecord()"]');
        if (saveBtn) {
            saveBtn.onclick = () => updateRecord(recordId);
            saveBtn.innerHTML = '<i class="fas fa-save"></i> تحديث الإجازة';
        }
    }, 100);

    alert('تم تحميل البيانات للتعديل');
}

// تحديث السجل
function updateRecord(recordId) {
    const recordIndex = vacationRecords.findIndex(r => r.id === recordId);
    if (recordIndex === -1) {
        alert('السجل غير موجود');
        return;
    }

    const year = document.getElementById('vacationYear').value;
    const days = parseInt(document.getElementById('vacationDays').value);
    const notes = document.getElementById('vacationNotes').value;

    if (!year || !days || days <= 0) {
        alert('يرجى إدخال السنة وعدد الأيام');
        return;
    }

    // جمع البيانات الإضافية
    let additionalData = {};

    if (selectedVacationType === 'annual') {
        const annualType = document.getElementById('annualType').value;
        if (!annualType) {
            alert('يرجى اختيار نوع الإجازة السنوية');
            return;
        }
        additionalData.annualType = annualType;
    } else if (selectedVacationType === 'official') {
        const holiday = document.getElementById('officialHoliday').value;
        if (!holiday || holiday === 'add_custom') {
            alert('يرجى اختيار العطلة الرسمية');
            return;
        }
        additionalData.holidayType = holiday;
    }

    // تحديث السجل
    vacationRecords[recordIndex] = {
        ...vacationRecords[recordIndex],
        year: parseInt(year),
        days: days,
        notes: notes,
        additionalData: additionalData,
        updatedAt: new Date().toISOString()
    };

    saveData();
    showVacationRecords();
    resetVacationForm();

    alert('تم تحديث سجل الإجازة بنجاح');
}

// حذف السجل
function deleteRecord(recordId) {
    const record = vacationRecords.find(r => r.id === recordId);
    if (!record) {
        alert('السجل غير موجود');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف سجل إجازة ${getVacationTypeText(record.vacationType)} لسنة ${record.year}؟`)) {
        vacationRecords = vacationRecords.filter(r => r.id !== recordId);
        saveData();
        showVacationRecords();
        alert('تم حذف السجل بنجاح');
    }
}
