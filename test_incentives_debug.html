<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الحوافز والمكافآت - تشخيص المشاكل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .results {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .result-item {
            padding: 5px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            color: #28a745;
        }

        .result-item.error {
            color: #dc3545;
        }

        .result-item.warning {
            color: #ffc107;
        }

        .result-item.info {
            color: #17a2b8;
        }

        .status-bar {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }

        .status-bar.success {
            background: #28a745;
        }

        .status-bar.error {
            background: #dc3545;
        }

        .status-bar.warning {
            background: #ffc107;
            color: #333;
        }

        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> اختبار نظام الحوافز والمكافآت</h1>
            <p>تشخيص وإصلاح مشاكل فتح نافذة الحوافز والمكافآت</p>
        </div>

        <div class="status-bar" id="statusBar">
            جاهز للاختبار...
        </div>

        <div class="content">
            <!-- اختبار أساسي -->
            <div class="test-section">
                <h3><i class="fas fa-check-circle"></i> الاختبارات الأساسية</h3>
                <button class="test-button" onclick="testBasicSystem()">
                    <i class="fas fa-play"></i> اختبار النظام الأساسي
                </button>
                <button class="test-button" onclick="testFunctions()">
                    <i class="fas fa-cogs"></i> اختبار الدوال
                </button>
                <button class="test-button" onclick="testVariables()">
                    <i class="fas fa-database"></i> اختبار المتغيرات
                </button>
                <div class="results" id="basicResults"></div>
            </div>

            <!-- اختبار فتح النافذة -->
            <div class="test-section">
                <h3><i class="fas fa-window-maximize"></i> اختبار فتح النافذة</h3>
                <button class="test-button" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                <button class="test-button" onclick="testOpenWindow()">
                    <i class="fas fa-external-link-alt"></i> اختبار فتح النافذة
                </button>
                <button class="test-button danger" onclick="forceOpenWindow()">
                    <i class="fas fa-hammer"></i> فتح النافذة بالقوة
                </button>
                <div class="results" id="windowResults"></div>
            </div>

            <!-- اختبار متقدم -->
            <div class="test-section">
                <h3><i class="fas fa-tools"></i> اختبارات متقدمة</h3>
                <button class="test-button warning" onclick="debugConsole()">
                    <i class="fas fa-terminal"></i> تشخيص Console
                </button>
                <button class="test-button" onclick="testWithAlert()">
                    <i class="fas fa-exclamation-triangle"></i> اختبار مع Alert
                </button>
                <button class="test-button" onclick="clearAllTests()">
                    <i class="fas fa-broom"></i> مسح جميع النتائج
                </button>
                <div class="results" id="advancedResults"></div>
            </div>

            <!-- مخرجات Console -->
            <div class="test-section">
                <h3><i class="fas fa-terminal"></i> مخرجات Console</h3>
                <button class="test-button" onclick="clearConsole()">
                    <i class="fas fa-trash"></i> مسح Console
                </button>
                <div class="console-output" id="consoleOutput"></div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `<span style="color: #666;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            container.appendChild(resultItem);
            container.scrollTop = container.scrollHeight;
        }

        // دالة لتحديث شريط الحالة
        function updateStatus(message, type = 'info') {
            const statusBar = document.getElementById('statusBar');
            statusBar.textContent = message;
            statusBar.className = `status-bar ${type}`;
        }

        // دالة لإضافة مخرجات console
        function addConsoleLog(message, type = 'info') {
            const consoleOutput = document.getElementById('consoleOutput');
            const logItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();

            let color = '#00ff00';
            if (type === 'error') color = '#ff4444';
            else if (type === 'warning') color = '#ffaa00';
            else if (type === 'success') color = '#44ff44';

            logItem.innerHTML = `<span style="color: #888;">[${timestamp}]</span> <span style="color: ${color};">${message}</span>`;
            consoleOutput.appendChild(logItem);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // اختبار النظام الأساسي
        function testBasicSystem() {
            updateStatus('جاري اختبار النظام الأساسي...', 'warning');
            addResult('basicResults', '🔍 بدء اختبار النظام الأساسي', 'info');

            // فحص تحميل app.js
            if (typeof employees !== 'undefined') {
                addResult('basicResults', '✅ تم تحميل app.js بنجاح', 'success');
                addResult('basicResults', `📊 عدد الموظفين: ${employees.length}`, 'info');
            } else {
                addResult('basicResults', '❌ لم يتم تحميل app.js', 'error');
                updateStatus('❌ فشل في تحميل النظام الأساسي', 'error');
                return;
            }

            // فحص بيانات الحوافز
            if (typeof incentivesRewardsData !== 'undefined') {
                addResult('basicResults', '✅ متغير incentivesRewardsData موجود', 'success');
                addResult('basicResults', `📊 عدد سجلات الحوافز: ${incentivesRewardsData.length}`, 'info');
            } else {
                addResult('basicResults', '❌ متغير incentivesRewardsData مفقود', 'error');
            }

            updateStatus('✅ تم اختبار النظام الأساسي', 'success');
        }

        // اختبار الدوال
        function testFunctions() {
            updateStatus('جاري اختبار الدوال...', 'warning');
            addResult('basicResults', '🔧 بدء اختبار الدوال', 'info');

            const requiredFunctions = [
                'viewIncentivesRewards',
                'saveIncentiveReward',
                'deleteIncentiveReward',
                'closeIncentivesRewardsModal',
                'loadIncentivesRewardsFromLocalStorage',
                'saveIncentivesRewardsToLocalStorage'
            ];

            let functionsFound = 0;
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult('basicResults', `✅ دالة ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult('basicResults', `❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            if (functionsFound === requiredFunctions.length) {
                addResult('basicResults', '✅ جميع الدوال المطلوبة موجودة', 'success');
                updateStatus('✅ جميع الدوال تعمل بشكل صحيح', 'success');
            } else {
                addResult('basicResults', `❌ ${requiredFunctions.length - functionsFound} دالة مفقودة`, 'error');
                updateStatus('❌ بعض الدوال مفقودة', 'error');
            }
        }

        // اختبار المتغيرات
        function testVariables() {
            updateStatus('جاري اختبار المتغيرات...', 'warning');
            addResult('basicResults', '📊 بدء اختبار المتغيرات', 'info');

            const requiredVariables = [
                { name: 'employees', type: 'object' },
                { name: 'incentivesRewardsData', type: 'object' },
                { name: 'activeEmployees', type: 'object' }
            ];

            requiredVariables.forEach(variable => {
                if (typeof window[variable.name] !== 'undefined') {
                    addResult('basicResults', `✅ متغير ${variable.name}: موجود`, 'success');
                    if (Array.isArray(window[variable.name])) {
                        addResult('basicResults', `📊 ${variable.name}: ${window[variable.name].length} عنصر`, 'info');
                    }
                } else {
                    addResult('basicResults', `❌ متغير ${variable.name}: مفقود`, 'error');
                }
            });

            updateStatus('✅ تم اختبار المتغيرات', 'success');
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('windowResults', '👤 إنشاء موظف تجريبي للاختبار', 'info');

            const testEmployee = {
                id: 99999,
                name: "موظف تجريبي للحوافز",
                position: "مختبر النظام",
                employeeCode: "TEST001",
                nationalId: "12345678901234",
                phone: "01000000000",
                photo: "https://via.placeholder.com/150/28a745/ffffff?text=TEST",
                employmentType: "monthly",
                salary: {
                    basic: 5000,
                    total: 5000
                },
                dateAdded: new Date().toISOString()
            };

            // التحقق من وجود الموظف
            const existingEmployee = employees.find(emp => emp.id === 99999);
            if (existingEmployee) {
                addResult('windowResults', '⚠️ الموظف التجريبي موجود بالفعل', 'warning');
            } else {
                employees.push(testEmployee);
                saveEmployeesToLocalStorage();
                addResult('windowResults', '✅ تم إنشاء الموظف التجريبي بنجاح', 'success');

                // إعادة عرض الموظفين
                if (typeof populateEmployees === 'function') {
                    populateEmployees();
                    addResult('windowResults', '🔄 تم تحديث قائمة الموظفين', 'info');
                }
            }

            updateStatus('✅ تم إنشاء الموظف التجريبي', 'success');
        }

        // اختبار فتح النافذة
        function testOpenWindow() {
            updateStatus('جاري اختبار فتح النافذة...', 'warning');
            addResult('windowResults', '🪟 بدء اختبار فتح نافذة الحوافز', 'info');

            // التحقق من وجود الموظف التجريبي
            const testEmployee = employees.find(emp => emp.id === 99999);
            if (!testEmployee) {
                addResult('windowResults', '❌ الموظف التجريبي غير موجود', 'error');
                addResult('windowResults', '💡 يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                updateStatus('❌ فشل الاختبار - الموظف غير موجود', 'error');
                return;
            }

            addResult('windowResults', '✅ تم العثور على الموظف التجريبي', 'success');

            // اختبار فتح النافذة
            try {
                addResult('windowResults', '🔄 محاولة فتح النافذة...', 'info');
                addConsoleLog('محاولة فتح نافذة الحوافز للموظف: ' + testEmployee.name);

                if (typeof viewIncentivesRewards === 'function') {
                    viewIncentivesRewards(99999);
                    addResult('windowResults', '✅ تم استدعاء دالة فتح النافذة', 'success');
                    addConsoleLog('تم استدعاء viewIncentivesRewards بنجاح', 'success');

                    // التحقق من وجود النافذة بعد فترة قصيرة
                    setTimeout(() => {
                        const modal = document.getElementById('incentivesRewardsModal');
                        if (modal) {
                            addResult('windowResults', '🎉 النافذة مفتوحة ومرئية!', 'success');
                            addConsoleLog('النافذة موجودة في DOM', 'success');
                            updateStatus('✅ نجح فتح النافذة!', 'success');
                        } else {
                            addResult('windowResults', '❌ النافذة لم تظهر في DOM', 'error');
                            addConsoleLog('النافذة غير موجودة في DOM', 'error');
                            updateStatus('❌ فشل في فتح النافذة', 'error');
                        }
                    }, 500);

                } else {
                    addResult('windowResults', '❌ دالة viewIncentivesRewards غير موجودة', 'error');
                    addConsoleLog('دالة viewIncentivesRewards مفقودة', 'error');
                    updateStatus('❌ الدالة المطلوبة مفقودة', 'error');
                }

            } catch (error) {
                addResult('windowResults', `❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                addConsoleLog('خطأ في فتح النافذة: ' + error.message, 'error');
                updateStatus('❌ حدث خطأ في الاختبار', 'error');
            }
        }

        // فتح النافذة بالقوة
        function forceOpenWindow() {
            updateStatus('جاري فتح النافذة بالقوة...', 'warning');
            addResult('windowResults', '💪 محاولة فتح النافذة بالقوة', 'info');

            try {
                // إنشاء موظف تجريبي إذا لم يكن موجوداً
                let testEmployee = employees.find(emp => emp.id === 99999);
                if (!testEmployee) {
                    createTestEmployee();
                    testEmployee = employees.find(emp => emp.id === 99999);
                }

                // محاولة فتح النافذة مع تسجيل مفصل
                addConsoleLog('=== بدء فتح النافذة بالقوة ===');
                addConsoleLog('الموظف: ' + testEmployee.name);
                addConsoleLog('معرف الموظف: ' + testEmployee.id);

                // استدعاء الدالة مباشرة
                window.viewIncentivesRewards(99999);

                addResult('windowResults', '✅ تم استدعاء الدالة بالقوة', 'success');
                addConsoleLog('تم استدعاء الدالة بالقوة', 'success');

                // فحص النافذة
                setTimeout(() => {
                    const modal = document.getElementById('incentivesRewardsModal');
                    if (modal) {
                        addResult('windowResults', '🎉 النافذة مفتوحة!', 'success');
                        updateStatus('✅ نجح فتح النافذة بالقوة!', 'success');
                    } else {
                        addResult('windowResults', '❌ النافذة لم تفتح حتى بالقوة', 'error');
                        updateStatus('❌ فشل فتح النافذة بالقوة', 'error');
                    }
                }, 1000);

            } catch (error) {
                addResult('windowResults', `❌ خطأ في الفتح بالقوة: ${error.message}`, 'error');
                addConsoleLog('خطأ في الفتح بالقوة: ' + error.message, 'error');
                updateStatus('❌ فشل الفتح بالقوة', 'error');
            }
        }

        // تشخيص Console
        function debugConsole() {
            updateStatus('جاري تشخيص Console...', 'warning');
            addResult('advancedResults', '🔍 بدء تشخيص Console', 'info');

            // تشغيل دالة الاختبار المدمجة
            if (typeof testIncentivesRewardsSystem === 'function') {
                addResult('advancedResults', '✅ دالة الاختبار المدمجة موجودة', 'success');
                addConsoleLog('تشغيل دالة الاختبار المدمجة...');
                testIncentivesRewardsSystem();
                addResult('advancedResults', '✅ تم تشغيل دالة الاختبار المدمجة', 'success');
            } else {
                addResult('advancedResults', '❌ دالة الاختبار المدمجة مفقودة', 'error');
            }

            updateStatus('✅ تم تشخيص Console', 'success');
        }

        // اختبار مع Alert
        function testWithAlert() {
            updateStatus('جاري الاختبار مع Alert...', 'warning');
            addResult('advancedResults', '⚠️ اختبار مع رسائل Alert', 'info');

            try {
                const testEmployee = employees.find(emp => emp.id === 99999);
                if (!testEmployee) {
                    alert('❌ الموظف التجريبي غير موجود. يرجى إنشاؤه أولاً.');
                    return;
                }

                alert('✅ سيتم فتح نافذة الحوافز الآن. راقب الشاشة...');
                viewIncentivesRewards(99999);

                setTimeout(() => {
                    const modal = document.getElementById('incentivesRewardsModal');
                    if (modal) {
                        alert('🎉 نجح! النافذة مفتوحة الآن.');
                        addResult('advancedResults', '✅ النافذة مفتوحة مع Alert', 'success');
                    } else {
                        alert('❌ فشل! النافذة لم تفتح.');
                        addResult('advancedResults', '❌ النافذة لم تفتح مع Alert', 'error');
                    }
                }, 1000);

            } catch (error) {
                alert('❌ خطأ: ' + error.message);
                addResult('advancedResults', `❌ خطأ مع Alert: ${error.message}`, 'error');
            }

            updateStatus('✅ تم الاختبار مع Alert', 'success');
        }

        // مسح جميع النتائج
        function clearAllTests() {
            document.getElementById('basicResults').innerHTML = '';
            document.getElementById('windowResults').innerHTML = '';
            document.getElementById('advancedResults').innerHTML = '';
            document.getElementById('consoleOutput').innerHTML = '';
            updateStatus('تم مسح جميع النتائج', 'info');
        }

        // مسح Console
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
            addConsoleLog('تم مسح Console', 'info');
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addConsoleLog('=== بدء الاختبار التلقائي ===');
                addConsoleLog('تحميل الصفحة مكتمل');

                if (typeof employees !== 'undefined') {
                    addConsoleLog('✅ متغير employees: موجود (' + employees.length + ' موظف)');
                } else {
                    addConsoleLog('❌ متغير employees: مفقود');
                }

                if (typeof incentivesRewardsData !== 'undefined') {
                    addConsoleLog('✅ متغير incentivesRewardsData: موجود (' + incentivesRewardsData.length + ' سجل)');
                } else {
                    addConsoleLog('❌ متغير incentivesRewardsData: مفقود');
                }

                if (typeof viewIncentivesRewards === 'function') {
                    addConsoleLog('✅ دالة viewIncentivesRewards: موجودة');
                } else {
                    addConsoleLog('❌ دالة viewIncentivesRewards: مفقودة');
                }

                addConsoleLog('=== انتهى الاختبار التلقائي ===');
            }, 1000);
        });
    </script>
</body>
</html>
