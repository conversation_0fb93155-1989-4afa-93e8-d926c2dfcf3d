<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة الحوافز والمكافآت المبسطة جداً</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }

        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 25px;
        }

        .feature-box {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }

        .feature-box h2 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .feature-list .icon {
            color: #28a745;
            font-size: 14px;
            width: 16px;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
            width: calc(50% - 16px);
            justify-content: center;
        }

        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.4);
        }

        .test-button.primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            width: 100%;
        }

        .test-button.primary:hover {
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .demo-employee {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            border: 2px solid #ffc107;
        }

        .demo-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        }

        .demo-info {
            flex: 1;
        }

        .demo-info h4 {
            margin: 0 0 5px 0;
            color: #856404;
            font-size: 16px;
        }

        .demo-info p {
            margin: 0;
            color: #856404;
            font-size: 13px;
        }

        .results-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border: 2px solid #e9ecef;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-result {
            padding: 10px 15px;
            margin: 6px 0;
            border-radius: 8px;
            font-size: 13px;
            border-right: 4px solid;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border-color: #28a745;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }

        .highlight {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight h3 {
            margin: 0 0 10px 0;
            color: #155724;
            font-size: 18px;
        }

        .highlight p {
            margin: 0;
            color: #155724;
            font-size: 14px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-gift"></i> نافذة الحوافز المبسطة جداً</h1>
            <p>أبسط واجهة ممكنة - 3 حقول فقط + زر حفظ كبير</p>
        </div>

        <div class="content">
            <!-- التبسيطات الجديدة -->
            <div class="feature-box">
                <h2><i class="fas fa-magic"></i> التبسيطات الجديدة</h2>

                <ul class="feature-list">
                    <li><i class="fas fa-check icon"></i> <strong>3 حقول فقط:</strong> النوع، المبلغ، الوصف</li>
                    <li><i class="fas fa-check icon"></i> <strong>تاريخ تلقائي:</strong> الشهر والسنة الحاليين تلقائياً</li>
                    <li><i class="fas fa-check icon"></i> <strong>زر حفظ كبير:</strong> يملأ عرض النافذة بالكامل</li>
                    <li><i class="fas fa-check icon"></i> <strong>سجل مبسط:</strong> كروت بدلاً من جدول</li>
                    <li><i class="fas fa-check icon"></i> <strong>نافذة أصغر:</strong> حجم مناسب للهاتف والكمبيوتر</li>
                    <li><i class="fas fa-check icon"></i> <strong>ألوان واضحة:</strong> أخضر للحوافز، أزرق للمكافآت</li>
                </ul>
            </div>

            <div class="highlight">
                <h3>🎯 الهدف من التبسيط</h3>
                <p>جعل إضافة الحوافز والمكافآت أمراً سهلاً وسريعاً في أقل خطوات ممكنة</p>
            </div>

            <!-- موظف تجريبي -->
            <div class="feature-box">
                <h2><i class="fas fa-user"></i> موظف تجريبي</h2>

                <div class="demo-employee">
                    <div class="demo-photo">مح</div>
                    <div class="demo-info">
                        <h4>محمد أحمد علي</h4>
                        <p><strong>الكود:</strong> EMP003 | <strong>المنصب:</strong> مهندس</p>
                    </div>
                </div>

                <button class="test-button primary" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
            </div>

            <!-- اختبارات سريعة -->
            <div class="feature-box">
                <h2><i class="fas fa-rocket"></i> اختبارات سريعة</h2>

                <div class="button-grid">
                    <button class="test-button" onclick="testWindow()">
                        <i class="fas fa-window-maximize"></i> فتح النافذة
                    </button>
                    <button class="test-button" onclick="addTestData()">
                        <i class="fas fa-database"></i> بيانات تجريبية
                    </button>
                </div>

                <button class="test-button primary" onclick="runFullTest()">
                    <i class="fas fa-play"></i> تشغيل اختبار شامل
                </button>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">🚀 جاهز لاختبار النافذة المبسطة جداً...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `${new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})} - ${message}`;
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 7777,
                name: 'محمد أحمد علي',
                position: 'مهندس برمجيات',
                employeeCode: 'EMP003',
                nationalId: '30123456789012',
                phone: '01123456789',
                employmentType: 'monthly',
                basicSalary: 9000,
                photo: 'https://randomuser.me/api/portraits/men/32.jpg'
            };

            // إضافة الموظف إلى القائمة
            const existingIndex = employees.findIndex(emp => emp.id === 7777);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي', 'warning');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء موظف تجريبي جديد', 'success');
            }

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult(`✅ تم حفظ: ${testEmployee.name}`, 'success');
        }

        // دالة لاختبار النافذة
        function testWindow() {
            addResult('🔍 اختبار فتح النافذة المبسطة...', 'info');

            const employee = employees.find(emp => emp.id === 7777);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(7777);
                addResult('✅ تم فتح النافذة بنجاح', 'success');
                addResult('👀 لاحظ البساطة: 3 حقول + زر حفظ كبير', 'info');
            } catch (error) {
                addResult(`❌ خطأ: ${error.message}`, 'error');
            }
        }

        // دالة لإضافة بيانات تجريبية
        function addTestData() {
            addResult('📊 إضافة بيانات تجريبية...', 'info');

            const employee = employees.find(emp => emp.id === 7777);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 7777,
                        type: 'incentive',
                        month: new Date().getMonth() + 1,
                        year: new Date().getFullYear(),
                        amount: 500,
                        description: 'حافز الأداء',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 7777,
                        type: 'reward',
                        month: new Date().getMonth() + 1,
                        year: new Date().getFullYear(),
                        amount: 1000,
                        description: 'مكافأة التميز',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];

                testRecords.forEach(record => {
                    incentivesRewardsData.push(record);
                });

                saveIncentivesRewardsToLocalStorage();
                addResult(`✅ تم إضافة ${testRecords.length} سجل`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إضافة البيانات: ${error.message}`, 'error');
            }
        }

        // دالة لتشغيل اختبار شامل
        function runFullTest() {
            addResult('🚀 بدء الاختبار الشامل للنافذة المبسطة...', 'info');

            // مسح النتائج السابقة
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';

            // الخطوة 1: إنشاء موظف
            setTimeout(() => {
                addResult('الخطوة 1: إنشاء موظف تجريبي', 'info');
                createTestEmployee();
            }, 500);

            // الخطوة 2: إضافة بيانات
            setTimeout(() => {
                addResult('الخطوة 2: إضافة بيانات تجريبية', 'info');
                addTestData();
            }, 1500);

            // الخطوة 3: فتح النافذة
            setTimeout(() => {
                addResult('الخطوة 3: فتح النافذة المبسطة', 'info');
                testWindow();
            }, 2500);

            // الخطوة 4: تعليمات الاختبار
            setTimeout(() => {
                addResult('الخطوة 4: تعليمات الاختبار اليدوي', 'warning');
                addResult('🎯 تحقق من الميزات التالية:', 'info');
                addResult('• النافذة صغيرة ومناسبة', 'info');
                addResult('• 3 حقول فقط (النوع، المبلغ، الوصف)', 'info');
                addResult('• زر الحفظ كبير ويملأ العرض', 'info');
                addResult('• السجلات تظهر كبطاقات ملونة', 'info');
                addResult('• لا توجد حقول شهر وسنة (تلقائية)', 'info');
                addResult('• سهولة الاستخدام القصوى', 'info');
                addResult('✅ الاختبار الشامل مكتمل!', 'success');
            }, 3500);
        }

        // فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎉 مرحباً بك في النافذة المبسطة جداً', 'success');
            addResult('📱 مصممة لتكون أبسط ما يمكن', 'info');

            setTimeout(() => {
                addResult('🔍 فحص سريع للنظام...', 'info');

                // فحص الدوال الأساسية
                const checks = [
                    { name: 'employees', check: () => typeof employees !== 'undefined' },
                    { name: 'incentivesRewardsData', check: () => typeof incentivesRewardsData !== 'undefined' },
                    { name: 'viewIncentivesRewards', check: () => typeof viewIncentivesRewards === 'function' },
                    { name: 'saveIncentiveReward', check: () => typeof saveIncentiveReward === 'function' }
                ];

                let allPassed = true;
                checks.forEach(check => {
                    if (check.check()) {
                        addResult(`✅ ${check.name}: متاح`, 'success');
                    } else {
                        addResult(`❌ ${check.name}: غير متاح`, 'error');
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    addResult('🎯 النظام جاهز! ابدأ بإنشاء موظف تجريبي', 'success');
                } else {
                    addResult('⚠️ هناك مشاكل في النظام', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
