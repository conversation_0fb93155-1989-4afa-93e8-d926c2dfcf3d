<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة تطبيق التاريخ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }

        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .button.success { background: #28a745; }
        .button.success:hover { background: #218838; }

        .button.warning { background: #ffc107; color: #212529; }
        .button.warning:hover { background: #e0a800; }

        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }

        .employee-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .employee-card.active {
            border-color: #28a745;
            background: #f8fff8;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }

        .employee-card h4 {
            margin: 0 0 8px 0;
            color: #333;
        }

        .employee-card p {
            margin: 4px 0;
            color: #666;
            font-size: 13px;
        }

        .activate-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }

        .activate-btn.active {
            background: #dc3545;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 اختبار نافذة تطبيق التاريخ على الكل (محدث)</h1>
            <p>اختبار شامل لوظيفة تطبيق التاريخ بالشهر والسنة على جميع الموظفين المنشطين</p>
            <div style="font-size: 14px; margin-top: 10px; opacity: 0.9;">
                ✨ الجديد: استخدام قوائم منسدلة للشهر والسنة بدلاً من حقل التاريخ
            </div>
        </div>

        <div class="test-section">
            <h4>🔧 إعداد البيانات التجريبية:</h4>
            <button class="button success" onclick="createTestData()">
                <i class="fas fa-plus"></i>
                إنشاء بيانات تجريبية
            </button>
            <button class="button warning" onclick="clearTestData()">
                <i class="fas fa-trash"></i>
                مسح البيانات
            </button>
        </div>

        <div class="test-section">
            <h4>👥 الموظفين التجريبيين:</h4>
            <div id="testEmployees">
                <!-- سيتم ملؤها ديناميكياً -->
            </div>
        </div>

        <div class="test-section">
            <h4>📅 اختبار نافذة تطبيق التاريخ:</h4>
            <button class="button" onclick="testDateApplicationModal()">
                <i class="fas fa-calendar-check"></i>
                فتح نافذة تطبيق التاريخ
            </button>
            <button class="button" onclick="testToggleDateButton()">
                <i class="fas fa-toggle-on"></i>
                اختبار زر التطبيق
            </button>
            <button class="button warning" onclick="testDateSelectors()">
                <i class="fas fa-list"></i>
                اختبار القوائم المنسدلة
            </button>
            <button class="button danger" onclick="testCancelApplication()">
                <i class="fas fa-calendar-times"></i>
                اختبار إلغاء التطبيق
            </button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div class="info-box" id="activeInfo">
            <strong>الموظفين المنشطين:</strong> <span id="activeCount">0</span>
        </div>

        <div class="console" id="console">
            [DATE_APPLICATION_TEST] نظام اختبار تطبيق التاريخ جاهز...<br>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function updateActiveCount() {
            const activeCountSpan = document.getElementById('activeCount');
            if (activeCountSpan && typeof activeEmployees !== 'undefined') {
                activeCountSpan.textContent = activeEmployees.size;
            }
        }

        function createTestData() {
            addLog('إنشاء بيانات تجريبية...');

            try {
                // إنشاء موظفين تجريبيين
                const testEmployees = [
                    {
                        id: 1001,
                        name: 'أحمد محمد علي',
                        position: 'مطور برمجيات',
                        employeeCode: 'DEV001',
                        employmentType: 'monthly',
                        basicSalary: 5000
                    },
                    {
                        id: 1002,
                        name: 'فاطمة أحمد حسن',
                        position: 'محاسبة',
                        employeeCode: 'ACC001',
                        employmentType: 'monthly',
                        basicSalary: 4500
                    },
                    {
                        id: 1003,
                        name: 'محمد عبدالله سالم',
                        position: 'مدير مشروع',
                        employeeCode: 'PM001',
                        employmentType: 'monthly',
                        basicSalary: 6000
                    },
                    {
                        id: 1004,
                        name: 'نورا سعد الدين',
                        position: 'مصممة جرافيك',
                        employeeCode: 'DES001',
                        employmentType: 'monthly',
                        basicSalary: 4000
                    }
                ];

                // إضافة الموظفين إلى القائمة الرئيسية
                testEmployees.forEach(emp => {
                    if (!employees.find(e => e.id === emp.id)) {
                        employees.push(emp);
                    }
                });

                // حفظ البيانات
                saveEmployeesToLocalStorage();

                // عرض الموظفين في الصفحة
                displayTestEmployees();

                addLog(`✓ تم إنشاء ${testEmployees.length} موظف تجريبي`);
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');

            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية', 'error');
            }
        }

        function displayTestEmployees() {
            const container = document.getElementById('testEmployees');
            const testEmployees = employees.filter(emp => emp.id >= 1001 && emp.id <= 1004);

            if (testEmployees.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد بيانات تجريبية - انقر على "إنشاء بيانات تجريبية"</p>';
                return;
            }

            container.innerHTML = testEmployees.map(emp => {
                const isActive = activeEmployees.has(emp.id);
                return `
                    <div class="employee-card ${isActive ? 'active' : ''}" id="emp-card-${emp.id}">
                        <h4>${emp.name}</h4>
                        <p><strong>الوظيفة:</strong> ${emp.position}</p>
                        <p><strong>الكود:</strong> ${emp.employeeCode}</p>
                        <p><strong>الراتب:</strong> ${emp.basicSalary} ج.م</p>
                        <button class="activate-btn ${isActive ? 'active' : ''}" onclick="toggleEmployeeActivation(${emp.id})">
                            ${isActive ? 'إلغاء التنشيط' : 'تنشيط'}
                        </button>
                    </div>
                `;
            }).join('');

            updateActiveCount();
        }

        function toggleEmployeeActivation(employeeId) {
            addLog(`تبديل حالة تنشيط الموظف ${employeeId}...`);

            if (activeEmployees.has(employeeId)) {
                activeEmployees.delete(employeeId);
                addLog(`✓ تم إلغاء تنشيط الموظف ${employeeId}`);
            } else {
                activeEmployees.add(employeeId);
                addLog(`✓ تم تنشيط الموظف ${employeeId}`);
            }

            // حفظ حالة التنشيط
            saveActiveEmployeesToLocalStorage();

            // تحديث العرض
            displayTestEmployees();
            updateActiveCount();
        }

        function clearTestData() {
            addLog('مسح البيانات التجريبية...');

            try {
                // إزالة الموظفين التجريبيين
                employees = employees.filter(emp => emp.id < 1001 || emp.id > 1004);

                // إزالة من قائمة المنشطين
                [1001, 1002, 1003, 1004].forEach(id => {
                    activeEmployees.delete(id);
                });

                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveActiveEmployeesToLocalStorage();

                // تحديث العرض
                displayTestEmployees();
                updateActiveCount();

                addLog('✓ تم مسح البيانات التجريبية');
                updateStatus('تم مسح البيانات التجريبية', 'warning');

            } catch (error) {
                addLog('✗ خطأ في مسح البيانات: ' + error.message);
                updateStatus('خطأ في مسح البيانات', 'error');
            }
        }

        function testDateApplicationModal() {
            addLog('اختبار فتح نافذة تطبيق التاريخ...');

            try {
                if (typeof openDateApplicationModal === 'function') {
                    openDateApplicationModal();
                    addLog('✓ تم استدعاء دالة فتح النافذة');
                    updateStatus('تم فتح نافذة تطبيق التاريخ!', 'success');
                } else {
                    addLog('✗ دالة openDateApplicationModal غير موجودة');
                    updateStatus('خطأ: دالة فتح النافذة غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function testToggleDateButton() {
            addLog('اختبار زر تطبيق التاريخ...');

            try {
                if (typeof toggleDateApplication === 'function') {
                    toggleDateApplication();
                    addLog('✓ تم استدعاء دالة تبديل التطبيق');
                    updateStatus('تم اختبار زر التطبيق!', 'success');
                } else {
                    addLog('✗ دالة toggleDateApplication غير موجودة');
                    updateStatus('خطأ: دالة زر التطبيق غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في اختبار الزر: ' + error.message);
                updateStatus('خطأ في اختبار الزر: ' + error.message, 'error');
            }
        }

        function testDateSelectors() {
            addLog('اختبار القوائم المنسدلة للشهر والسنة...');

            try {
                // فتح النافذة أولاً
                if (typeof openDateApplicationModal === 'function') {
                    openDateApplicationModal();
                    addLog('✓ تم فتح النافذة');

                    // انتظار قليل للتأكد من تحميل النافذة
                    setTimeout(() => {
                        const monthSelect = document.getElementById('applicationMonth');
                        const yearSelect = document.getElementById('applicationYear');
                        const previewSpan = document.getElementById('selectedDatePreview');

                        if (monthSelect && yearSelect && previewSpan) {
                            addLog('✓ تم العثور على القوائم المنسدلة');

                            // اختبار تغيير الشهر
                            monthSelect.value = '6'; // يونيو
                            monthSelect.dispatchEvent(new Event('change'));
                            addLog('✓ تم تغيير الشهر إلى يونيو');

                            // اختبار تغيير السنة
                            yearSelect.value = '2025';
                            yearSelect.dispatchEvent(new Event('change'));
                            addLog('✓ تم تغيير السنة إلى 2025');

                            // فحص المعاينة
                            setTimeout(() => {
                                const previewText = previewSpan.textContent;
                                addLog(`✓ نص المعاينة: ${previewText}`);

                                if (previewText.includes('يونيو') && previewText.includes('2025')) {
                                    addLog('✅ القوائم المنسدلة تعمل بشكل صحيح!');
                                    updateStatus('✅ القوائم المنسدلة تعمل بشكل مثالي!', 'success');
                                } else {
                                    addLog('⚠️ المعاينة لا تعكس التغييرات بشكل صحيح');
                                    updateStatus('⚠️ مشكلة في تحديث المعاينة', 'warning');
                                }
                            }, 500);

                        } else {
                            addLog('❌ لم يتم العثور على القوائم المنسدلة');
                            updateStatus('❌ القوائم المنسدلة غير موجودة', 'error');
                        }
                    }, 1000);

                } else {
                    addLog('✗ دالة فتح النافذة غير موجودة');
                    updateStatus('خطأ: دالة فتح النافذة غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في اختبار القوائم: ' + error.message);
                updateStatus('خطأ في اختبار القوائم: ' + error.message, 'error');
            }
        }

        function testCancelApplication() {
            addLog('اختبار إلغاء التطبيق...');

            try {
                if (typeof openCancelDateApplicationModal === 'function') {
                    openCancelDateApplicationModal();
                    addLog('✓ تم استدعاء دالة إلغاء التطبيق');
                    updateStatus('تم اختبار إلغاء التطبيق!', 'success');
                } else {
                    addLog('✗ دالة openCancelDateApplicationModal غير موجودة');
                    updateStatus('خطأ: دالة إلغاء التطبيق غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في اختبار الإلغاء: ' + error.message);
                updateStatus('خطأ في اختبار الإلغاء: ' + error.message, 'error');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة اختبار تطبيق التاريخ');

            // فحص سريع
            setTimeout(() => {
                if (typeof employees !== 'undefined') {
                    addLog('✓ البرنامج الأساسي محمل');
                    displayTestEmployees();
                    updateActiveCount();
                    updateStatus('جاهز لبدء الاختبار', 'success');
                } else {
                    addLog('⚠️ البرنامج الأساسي غير محمل');
                    updateStatus('تحذير: البرنامج الأساسي غير محمل', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
