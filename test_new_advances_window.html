<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 نافذة السلف الجديدة - مثل نافذة أيام الحضور</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .success-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .feature-icon {
            font-size: 2.5em;
            color: #28a745;
            margin-bottom: 15px;
        }
        .feature-title {
            color: #495057;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .feature-desc {
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .big-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        .big-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .big-button.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .comparison-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            border: 2px solid #dee2e6;
        }
        .comparison-title {
            color: #495057;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .comparison-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .comparison-item.old {
            border-left: 4px solid #ffc107;
        }
        .comparison-item.new {
            border-left: 4px solid #28a745;
        }
        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .comparison-item.old h4 {
            color: #856404;
        }
        .comparison-item.new h4 {
            color: #155724;
        }
        .result {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-window-restore"></i> نافذة السلف الجديدة</h1>
            <p style="font-size: 1.2em; color: #666;">بنفس تصميم نافذة أيام الحضور تمام<|im_start|> ولكن بحجم أصغر</p>
        </div>

        <div class="success-card">
            <i class="fas fa-check-circle"></i>
            تم إنشاء نافذة السلف بنفس تصميم نافذة أيام الحضور مع تحسينات خاصة!
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">تصميم متطابق</div>
                <div class="feature-desc">نفس الألوان والخطوط والتخطيط المستخدم في نافذة أيام الحضور</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📏</div>
                <div class="feature-title">حجم أصغر</div>
                <div class="feature-desc">عرض 70% بدلاً من 90% مع حد أقصى 700px بدلاً من 1000px</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🖱️</div>
                <div class="feature-title">قابلة للسحب</div>
                <div class="feature-desc">يمكن سحب النافذة وتحريكها مثل نافذة أيام الحضور تمام<|im_start|></div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📋</div>
                <div class="feature-title">جدول مطابق</div>
                <div class="feature-desc">نفس تصميم الجدول مع الألوان المتناوبة والتأثيرات</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📝</div>
                <div class="feature-title">نموذج محسن</div>
                <div class="feature-desc">نموذج إضافة السلف بنفس تصميم النماذج في النظام</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">أداء سريع</div>
                <div class="feature-desc">تحميل سريع مع CSS محسن وتأثيرات سلسة</div>
            </div>
        </div>

        <div class="comparison-section">
            <div class="comparison-title">مقارنة التصميم</div>
            <div class="comparison-grid">
                <div class="comparison-item old">
                    <h4>🔸 النافذة القديمة</h4>
                    <ul style="text-align: right; margin: 10px 0;">
                        <li>تصميم مختلف عن باقي النظام</li>
                        <li>حجم كبير (800px)</li>
                        <li>ألوان غير متناسقة</li>
                        <li>تخطيط معقد</li>
                        <li>غير قابلة للسحب</li>
                    </ul>
                </div>
                
                <div class="comparison-item new">
                    <h4>🔹 النافذة الجديدة</h4>
                    <ul style="text-align: right; margin: 10px 0;">
                        <li>تصميم متطابق مع نافذة أيام الحضور</li>
                        <li>حجم مناسب (700px)</li>
                        <li>ألوان متناسقة مع النظام</li>
                        <li>تخطيط بسيط ومنظم</li>
                        <li>قابلة للسحب والتحريك</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="big-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
            </button>
            
            <button class="big-button primary" onclick="openNewAdvancesWindow()">
                <i class="fas fa-window-restore"></i> فتح النافذة الجديدة
            </button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'exclamation-triangle'}"></i> ${message}`;
        }

        function createTestEmployee() {
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 54321,
                    name: 'سارة أحمد النافذة الجديدة',
                    position: 'محاسبة',
                    employeeCode: 'NEW54321',
                    employmentType: 'monthly',
                    basicSalary: 6000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 54321);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء بعض سجلات السلف التجريبية
                const testAdvances = [
                    {
                        id: Date.now() + 1,
                        employeeId: 54321,
                        type: 'advance',
                        month: 12,
                        year: 2024,
                        amount: 1500,
                        description: 'سلفة ديسمبر',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 54321,
                        type: 'deduction',
                        month: 11,
                        year: 2024,
                        amount: 300,
                        description: 'خصم تأخير',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 54321,
                        type: 'advance',
                        month: 10,
                        year: 2024,
                        amount: 2000,
                        description: 'سلفة أكتوبر',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                if (typeof advancesDeductionsData !== 'undefined') {
                    advancesDeductionsData = advancesDeductionsData.filter(record => record.employeeId !== 54321);
                    testAdvances.forEach(record => {
                        advancesDeductionsData.push(record);
                    });
                } else {
                    window.advancesDeductionsData = testAdvances;
                }
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                    saveAdvancesDeductionsToLocalStorage();
                }
                
                showResult(`✅ تم إنشاء الموظفة "${testEmployee.name}" مع ${testAdvances.length} سجلات سلف/خصم`, 'success');
                
            } catch (error) {
                showResult('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openNewAdvancesWindow() {
            try {
                if (!employees.find(emp => emp.id === 54321)) {
                    showResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // فتح النافذة الجديدة
                viewAdvancesDeductions(54321);
                
                showResult('🎉 تم فتح نافذة السلف الجديدة! لاحظ التصميم المطابق لنافذة أيام الحضور', 'success');
                
                // فحص النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        const modalContent = modal.querySelector('.advances-content');
                        if (modalContent) {
                            showResult(`✅ النافذة مفتوحة بالتصميم الجديد المطابق لنافذة أيام الحضور!`, 'success');
                        }
                    }
                }, 1000);
                
            } catch (error) {
                showResult('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewAdvancesDeductions === 'function') {
                    showResult('✅ النظام جاهز! ابدأ بإنشاء الموظف التجريبي', 'success');
                } else {
                    showResult('❌ خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
