<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الخصومات في حساب الأيام</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #1e7e34;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.warning:hover {
            background: #e0a800;
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        .log-entry.info { color: #007bff; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .example-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }

        .example-box h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .example-box .calculation {
            font-family: monospace;
            background: white;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>اختبار نظام الخصومات في حساب الأيام</h1>
            <p>اختبار شامل لنظام خصم الأيام والساعات مع عرض النتائج</p>
        </div>

        <div class="content">
            <!-- أمثلة على الحسابات -->
            <div class="test-section">
                <h3>أمثلة على حسابات الخصومات</h3>
                
                <div class="example-box">
                    <h4>مثال 1: خصم أيام</h4>
                    <div class="calculation">
                        الأيام المحسوبة: 25 يوم<br>
                        أيام الغياب: 2 يوم<br>
                        نوع الخصم: خصم أيام<br>
                        مقدار الخصم: 1 يوم<br>
                        <strong>النتيجة: 25 - 2 - 1 = 22 يوم</strong>
                    </div>
                </div>

                <div class="example-box">
                    <h4>مثال 2: خصم ساعات</h4>
                    <div class="calculation">
                        الأيام المحسوبة: 25 يوم<br>
                        أيام الغياب: 1 يوم<br>
                        نوع الخصم: خصم ساعات<br>
                        مقدار الخصم: 4 ساعات (0.5 يوم)<br>
                        <strong>النتيجة: 25 - 1 - 0.5 = 23.5 يوم (23 يوم و 4 ساعات)</strong>
                    </div>
                </div>

                <div class="example-box">
                    <h4>مثال 3: خصم ربع يوم</h4>
                    <div class="calculation">
                        الأيام المحسوبة: 20 يوم<br>
                        أيام الغياب: 0 يوم<br>
                        نوع الخصم: خصم ساعات<br>
                        مقدار الخصم: 2 ساعات (0.25 يوم)<br>
                        <strong>النتيجة: 20 - 0 - 0.25 = 19.75 يوم (19 يوم و 6 ساعات)</strong>
                    </div>
                </div>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn success" onclick="createTestEmployee()">إنشاء موظف تجريبي</button>
                    <button class="btn" onclick="createTestProjects()">إنشاء مشروعات</button>
                    <button class="btn warning" onclick="checkDeductionSystem()">فحص نظام الخصومات</button>
                </div>
            </div>

            <!-- قسم اختبارات الخصومات -->
            <div class="test-section">
                <h3>اختبارات نظام الخصومات</h3>
                <div class="grid">
                    <button class="btn" onclick="testDaysDeduction()">اختبار خصم الأيام</button>
                    <button class="btn" onclick="testHoursDeduction()">اختبار خصم الساعات</button>
                    <button class="btn warning" onclick="testQuarterDayDeduction()">اختبار خصم ربع يوم</button>
                    <button class="btn danger" onclick="testHalfDayDeduction()">اختبار خصم نصف يوم</button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn success" onclick="runAllDeductionTests()">تشغيل جميع اختبارات الخصومات</button>
                    <button class="btn" onclick="testSaveWithDeductions()">اختبار الحفظ مع الخصومات</button>
                    <button class="btn warning" onclick="testRecordsWithDeductions()">اختبار عرض السجلات</button>
                    <button class="btn danger" onclick="clearAllTestData()">مسح البيانات التجريبية</button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار نظام الخصومات...
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry info">تم تحميل صفحة اختبار نظام الخصومات</div>
                <div class="log-entry info">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 888,
                name: 'سارة أحمد (اختبار الخصومات)',
                position: 'محاسبة',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 4500,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 888)) {
                employees.push(testEmployee);
                addLog('تم إنشاء الموظف التجريبي للخصومات بنجاح', 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                addLog('الموظف التجريبي موجود بالفعل', 'info');
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createTestProjects() {
            const testProjects = [
                { id: 301, name: 'مشروع اختبار الخصومات 1', status: 'active' },
                { id: 302, name: 'مشروع اختبار الخصومات 2', status: 'active' }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                }
            });

            addLog('تم إنشاء مشروعات اختبار الخصومات بنجاح', 'success');
            updateStatus('تم إنشاء مشروعات اختبار الخصومات بنجاح', 'success');
        }

        function checkDeductionSystem() {
            const checks = [
                { name: 'دالة calculateActualDays', test: () => typeof calculateActualDays === 'function' },
                { name: 'حقل نوع الخصم', test: () => document.getElementById('deductionType') !== null },
                { name: 'حقل مقدار الخصم', test: () => document.getElementById('deductionAmount') !== null },
                { name: 'حقل الأيام قبل الخصم', test: () => document.getElementById('daysBeforeDeduction') !== null },
                { name: 'منطقة ملخص الخصم', test: () => document.getElementById('deductionSummary') !== null }
            ];

            let passed = 0;
            checks.forEach(check => {
                if (check.test()) {
                    addLog(`✓ ${check.name}`, 'success');
                    passed++;
                } else {
                    addLog(`✗ ${check.name}`, 'error');
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص نظام الخصومات: ${passed}/${checks.length} عناصر موجودة`, status);
        }

        function testDaysDeduction() {
            addLog('بدء اختبار خصم الأيام...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                // ملء البيانات
                document.getElementById('calculatedDays').value = '25';
                document.getElementById('absenceDays').value = '2';
                document.getElementById('deductionType').value = 'days';
                document.getElementById('deductionAmount').value = '1';
                
                // تشغيل الحساب
                calculateActualDays();
                
                // التحقق من النتيجة
                const result = document.getElementById('actualDaysDisplay').value;
                const expected = '22 يوم';
                
                if (result === expected) {
                    addLog(`✓ اختبار خصم الأيام نجح: ${result}`, 'success');
                    updateStatus('اختبار خصم الأيام نجح', 'success');
                } else {
                    addLog(`✗ اختبار خصم الأيام فشل: توقع "${expected}" لكن حصل على "${result}"`, 'error');
                    updateStatus('اختبار خصم الأيام فشل', 'error');
                }
            }, 500);
        }

        function testHoursDeduction() {
            addLog('بدء اختبار خصم الساعات...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                // ملء البيانات
                document.getElementById('calculatedDays').value = '25';
                document.getElementById('absenceDays').value = '1';
                document.getElementById('deductionType').value = 'hours';
                document.getElementById('deductionAmount').value = '4';
                
                // تشغيل الحساب
                calculateActualDays();
                
                // التحقق من النتيجة
                const result = document.getElementById('actualDaysDisplay').value;
                const expected = '23 يوم و 4.0 ساعة';
                
                if (result === expected) {
                    addLog(`✓ اختبار خصم الساعات نجح: ${result}`, 'success');
                    updateStatus('اختبار خصم الساعات نجح', 'success');
                } else {
                    addLog(`✗ اختبار خصم الساعات فشل: توقع "${expected}" لكن حصل على "${result}"`, 'error');
                    updateStatus('اختبار خصم الساعات فشل', 'error');
                }
            }, 500);
        }

        function testQuarterDayDeduction() {
            addLog('بدء اختبار خصم ربع يوم...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                // ملء البيانات
                document.getElementById('calculatedDays').value = '20';
                document.getElementById('absenceDays').value = '0';
                document.getElementById('deductionType').value = 'hours';
                document.getElementById('deductionAmount').value = '2';
                
                // تشغيل الحساب
                calculateActualDays();
                
                // التحقق من النتيجة
                const result = document.getElementById('actualDaysDisplay').value;
                const expected = '19 يوم و 6.0 ساعة';
                
                if (result === expected) {
                    addLog(`✓ اختبار خصم ربع يوم نجح: ${result}`, 'success');
                    updateStatus('اختبار خصم ربع يوم نجح', 'success');
                } else {
                    addLog(`✗ اختبار خصم ربع يوم فشل: توقع "${expected}" لكن حصل على "${result}"`, 'error');
                    updateStatus('اختبار خصم ربع يوم فشل', 'error');
                }
            }, 500);
        }

        function testHalfDayDeduction() {
            addLog('بدء اختبار خصم نصف يوم...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                // ملء البيانات
                document.getElementById('calculatedDays').value = '22';
                document.getElementById('absenceDays').value = '1';
                document.getElementById('deductionType').value = 'days';
                document.getElementById('deductionAmount').value = '0.5';
                
                // تشغيل الحساب
                calculateActualDays();
                
                // التحقق من النتيجة
                const result = document.getElementById('actualDaysDisplay').value;
                const expected = '20.5 يوم';
                
                if (result === expected) {
                    addLog(`✓ اختبار خصم نصف يوم نجح: ${result}`, 'success');
                    updateStatus('اختبار خصم نصف يوم نجح', 'success');
                } else {
                    addLog(`✗ اختبار خصم نصف يوم فشل: توقع "${expected}" لكن حصل على "${result}"`, 'error');
                    updateStatus('اختبار خصم نصف يوم فشل', 'error');
                }
            }, 500);
        }

        function testSaveWithDeductions() {
            addLog('بدء اختبار الحفظ مع الخصومات...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة وملء البيانات
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                // ملء البيانات
                const projectSelect = document.getElementById('daysProject');
                if (projectSelect && projectSelect.options.length > 1) {
                    projectSelect.selectedIndex = 1;
                }
                
                document.getElementById('calculatedDays').value = '24';
                document.getElementById('absenceDays').value = '1';
                document.getElementById('deductionType').value = 'hours';
                document.getElementById('deductionAmount').value = '6';
                
                // تشغيل الحساب
                calculateActualDays();
                
                // محاولة الحفظ
                const result = saveDaysCalculation(888);
                
                if (result !== false) {
                    addLog('✓ تم حفظ البيانات مع الخصومات بنجاح', 'success');
                    updateStatus('تم حفظ البيانات مع الخصومات بنجاح', 'success');
                } else {
                    addLog('✗ فشل في حفظ البيانات مع الخصومات', 'error');
                    updateStatus('فشل في حفظ البيانات مع الخصومات', 'error');
                }
            }, 500);
        }

        function testRecordsWithDeductions() {
            addLog('بدء اختبار عرض السجلات مع الخصومات...', 'info');
            
            if (!employees.find(emp => emp.id === 888)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(888);
            
            setTimeout(() => {
                viewDaysRecords();
                addLog('✓ تم اختبار عرض السجلات مع الخصومات', 'success');
                updateStatus('تم اختبار عرض السجلات مع الخصومات', 'success');
            }, 500);
        }

        function runAllDeductionTests() {
            addLog('بدء تشغيل جميع اختبارات الخصومات...', 'info');
            updateStatus('جاري تشغيل جميع اختبارات الخصومات...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => checkDeductionSystem(), 1500);
            setTimeout(() => testDaysDeduction(), 2500);
            setTimeout(() => testHoursDeduction(), 4000);
            setTimeout(() => testQuarterDayDeduction(), 5500);
            setTimeout(() => testHalfDayDeduction(), 7000);
            setTimeout(() => testSaveWithDeductions(), 8500);
            setTimeout(() => testRecordsWithDeductions(), 10000);

            setTimeout(() => {
                addLog('انتهى تشغيل جميع اختبارات الخصومات', 'success');
                updateStatus('انتهت جميع اختبارات الخصومات بنجاح', 'success');
            }, 11000);
        }

        function clearAllTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار الخصومات؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 888);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![301, 302].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 888);
                }

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('تم مسح جميع بيانات اختبار الخصومات', 'warning');
                updateStatus('تم مسح جميع بيانات اختبار الخصومات', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة اختبار نظام الخصومات', 'info');
            updateStatus('جاهز لاختبار نظام الخصومات - اضغط على الأزرار أعلاه', 'info');
        });
    </script>
</body>
</html>
