<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - السلف والخصومات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745, #20c997);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 26px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: calc(50% - 20px);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-double"></i> اختبار نهائي للسلف والخصومات</h1>
            <p>التأكد من عمل نظام السلف والخصومات بشكل صحيح</p>
        </div>

        <div class="status" id="status">
            جاهز للاختبار النهائي...
        </div>

        <div class="content">
            <div class="grid">
                <button class="test-button" onclick="createEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                
                <button class="test-button success" onclick="testAdvancesWindow()">
                    <i class="fas fa-window-maximize"></i> فتح نافذة السلف
                </button>
                
                <button class="test-button warning" onclick="addTestAdvance()">
                    <i class="fas fa-plus"></i> إضافة سلفة تجريبية
                </button>
                
                <button class="test-button danger" onclick="checkSavedData()">
                    <i class="fas fa-database"></i> فحص البيانات المحفوظة
                </button>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <button class="test-button" onclick="openMainApp()" style="width: 80%;">
                    <i class="fas fa-external-link-alt"></i> فتح التطبيق الرئيسي
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#333';
            if (type === 'success') color = '#28a745';
            else if (type === 'error') color = '#dc3545';
            else if (type === 'warning') color = '#ffc107';
            else if (type === 'info') color = '#17a2b8';
            
            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف تجريبي
        function createEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('👤 إنشاء موظف تجريبي للسلف والخصومات', 'info');
            
            const testEmployee = {
                id: 77777,
                name: "موظف تجريبي نهائي للسلف",
                position: "مختبر السلف والخصومات",
                employeeCode: "ADV_FINAL",
                nationalId: "12345678901234",
                phone: "01000000000",
                photo: "https://via.placeholder.com/150/28a745/ffffff?text=ADV",
                employmentType: "monthly",
                salary: { basic: 5000, total: 5000 },
                dateAdded: new Date().toISOString()
            };
            
            const existing = employees.find(emp => emp.id === 77777);
            if (!existing) {
                employees.push(testEmployee);
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            } else {
                addResult('⚠️ الموظف التجريبي موجود بالفعل', 'warning');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            }
        }

        // اختبار نافذة السلف
        function testAdvancesWindow() {
            updateStatus('جاري اختبار نافذة السلف...', 'warning');
            addResult('🪟 اختبار فتح نافذة السلف والخصومات', 'info');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 77777);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // التحقق من وجود الدالة
                if (typeof viewAdvancesDeductions !== 'function') {
                    addResult('❌ دالة فتح النافذة غير موجودة', 'error');
                    updateStatus('❌ الدالة مفقودة', 'error');
                    return;
                }
                
                // فتح النافذة
                viewAdvancesDeductions(77777);
                addResult('✅ تم فتح نافذة السلف والخصومات', 'success');
                
                // فحص النافذة بعد فترة
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        addResult('🎉 النافذة مفتوحة ومرئية!', 'success');
                        updateStatus('✅ النافذة تعمل بشكل صحيح', 'success');
                    } else {
                        addResult('❌ النافذة لم تظهر', 'error');
                        updateStatus('❌ مشكلة في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // إضافة سلفة تجريبية
        function addTestAdvance() {
            updateStatus('جاري إضافة سلفة تجريبية...', 'warning');
            addResult('💰 إضافة سلفة تجريبية', 'info');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 77777);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // إنشاء عناصر النموذج مؤقتاً
                const tempForm = document.createElement('div');
                tempForm.style.display = 'none';
                tempForm.innerHTML = `
                    <select id="advanceDeductionType">
                        <option value="advance" selected>سلفة</option>
                    </select>
                    <input id="advanceDeductionAmount" value="1500">
                    <input id="advanceDeductionDescription" value="سلفة تجريبية للاختبار">
                    <input id="advanceDeductionMonth" value="${new Date().getMonth() + 1}">
                    <input id="advanceDeductionYear" value="${new Date().getFullYear()}">
                `;
                document.body.appendChild(tempForm);
                
                // حفظ السلفة
                if (typeof saveAdvanceDeduction === 'function') {
                    const beforeCount = advancesDeductionsData ? advancesDeductionsData.length : 0;
                    addResult(`📊 عدد السجلات قبل الإضافة: ${beforeCount}`, 'info');
                    
                    saveAdvanceDeduction(77777);
                    
                    setTimeout(() => {
                        const afterCount = advancesDeductionsData ? advancesDeductionsData.length : 0;
                        addResult(`📊 عدد السجلات بعد الإضافة: ${afterCount}`, 'info');
                        
                        if (afterCount > beforeCount) {
                            addResult('✅ تم إضافة السلفة بنجاح', 'success');
                            updateStatus('✅ تم حفظ السلفة', 'success');
                        } else {
                            addResult('❌ لم يتم إضافة السلفة', 'error');
                            updateStatus('❌ فشل في حفظ السلفة', 'error');
                        }
                        
                        // إزالة النموذج المؤقت
                        tempForm.remove();
                    }, 1000);
                } else {
                    addResult('❌ دالة الحفظ غير موجودة', 'error');
                    updateStatus('❌ دالة الحفظ مفقودة', 'error');
                    tempForm.remove();
                }
                
            } catch (error) {
                addResult(`❌ خطأ في إضافة السلفة: ${error.message}`, 'error');
                updateStatus('❌ فشل في إضافة السلفة', 'error');
            }
        }

        // فحص البيانات المحفوظة
        function checkSavedData() {
            updateStatus('جاري فحص البيانات المحفوظة...', 'warning');
            addResult('🔍 فحص البيانات المحفوظة في localStorage', 'info');
            
            try {
                // فحص localStorage
                const savedData = localStorage.getItem('oscoAdvancesDeductions');
                if (savedData) {
                    const parsed = JSON.parse(savedData);
                    addResult(`📊 إجمالي السجلات المحفوظة: ${parsed.length}`, 'info');
                    
                    // فحص سجلات الموظف التجريبي
                    const employeeRecords = parsed.filter(r => r.employeeId === 77777);
                    addResult(`👤 سجلات الموظف التجريبي: ${employeeRecords.length}`, 'info');
                    
                    if (employeeRecords.length > 0) {
                        employeeRecords.forEach((record, index) => {
                            const typeText = record.type === 'advance' ? 'سلفة' : 'خصم';
                            addResult(`${index + 1}. ${typeText}: ${record.amount} ج.م - ${record.description || 'بدون وصف'}`, 'success');
                        });
                        updateStatus('✅ البيانات محفوظة بشكل صحيح', 'success');
                    } else {
                        addResult('⚠️ لا توجد سجلات للموظف التجريبي', 'warning');
                        updateStatus('⚠️ لا توجد سجلات', 'warning');
                    }
                } else {
                    addResult('❌ لا توجد بيانات محفوظة في localStorage', 'error');
                    updateStatus('❌ لا توجد بيانات محفوظة', 'error');
                }
                
                // فحص المتغير في الذاكرة
                if (typeof advancesDeductionsData !== 'undefined') {
                    addResult(`💾 البيانات في الذاكرة: ${advancesDeductionsData.length} سجل`, 'info');
                } else {
                    addResult('❌ متغير البيانات غير موجود في الذاكرة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في فحص البيانات', 'error');
            }
        }

        // فتح التطبيق الرئيسي
        function openMainApp() {
            window.open('index.html', '_blank');
            addResult('🔗 تم فتح التطبيق الرئيسي في نافذة جديدة', 'info');
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 بدء الاختبار التلقائي...', 'info');
                
                // فحص النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }
                
                // فحص دوال السلف
                const functions = ['viewAdvancesDeductions', 'saveAdvanceDeduction', 'deleteAdvanceDeduction'];
                let functionsOk = true;
                functions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        addResult(`✅ دالة ${func}: موجودة`, 'success');
                    } else {
                        addResult(`❌ دالة ${func}: مفقودة`, 'error');
                        functionsOk = false;
                    }
                });
                
                if (functionsOk) {
                    addResult('✅ جميع الدوال موجودة', 'success');
                    updateStatus('✅ النظام جاهز للاختبار', 'success');
                } else {
                    addResult('❌ بعض الدوال مفقودة', 'error');
                    updateStatus('❌ مشكلة في الدوال', 'error');
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
