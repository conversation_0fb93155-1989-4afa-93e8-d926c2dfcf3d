<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة البدلات مع التعديل المباشر</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
            font-weight: 600;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .test-button.primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .test-button.primary:hover {
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.warning:hover {
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.danger:hover {
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }

        .result-item {
            padding: 10px 15px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            background: #f8fff9;
            border-right: 4px solid #28a745;
        }

        .result-item.error {
            background: #fff8f8;
            border-right: 4px solid #dc3545;
        }

        .result-item.warning {
            background: #fffdf5;
            border-right: 4px solid #ffc107;
        }

        .result-item.info {
            background: #f8f9ff;
            border-right: 4px solid #007bff;
        }

        .employee-info {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .employee-info h4 {
            margin: 0 0 10px 0;
            color: #1976D2;
            font-size: 16px;
        }

        .employee-info p {
            margin: 5px 0;
            color: #424242;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نافذة البدلات مع التعديل المباشر</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">اختبار شامل لنافذة إدارة البدلات الجديدة</p>
        </div>

        <div class="content">
            <div class="test-section">
                <h3><i class="fas fa-database"></i> إعداد البيانات التجريبية</h3>
                <button class="test-button" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                <button class="test-button primary" onclick="addSampleAllowances()">
                    <i class="fas fa-coins"></i> إضافة بدلات تجريبية
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-window-maximize"></i> اختبار نافذة البدلات</h3>
                <button class="test-button" onclick="openAllowancesWindow()">
                    <i class="fas fa-money-bill-wave"></i> فتح نافذة البدلات
                </button>
                <button class="test-button warning" onclick="testInlineEditing()">
                    <i class="fas fa-edit"></i> اختبار التعديل المباشر
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-tools"></i> أدوات التشخيص</h3>
                <button class="test-button primary" onclick="checkAllowancesData()">
                    <i class="fas fa-search"></i> فحص بيانات البدلات
                </button>
                <button class="test-button danger" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
                <button class="test-button" onclick="openOriginalApp()">
                    <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
                </button>
            </div>

            <div id="employeeInfo" class="employee-info" style="display: none;">
                <!-- سيتم عرض معلومات الموظف التجريبي هنا -->
            </div>

            <div id="status" class="status info">جاهز لبدء الاختبار...</div>
            <div id="results" class="results"></div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        // متغير لتخزين ID الموظف التجريبي
        let testEmployeeId = 99999;

        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;

            let icon = '';
            switch(type) {
                case 'success': icon = '<i class="fas fa-check-circle"></i>'; break;
                case 'error': icon = '<i class="fas fa-times-circle"></i>'; break;
                case 'warning': icon = '<i class="fas fa-exclamation-triangle"></i>'; break;
                default: icon = '<i class="fas fa-info-circle"></i>';
            }

            resultItem.innerHTML = `${icon} ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('🔧 إنشاء موظف تجريبي...', 'info');
            updateStatus('جاري إنشاء الموظف التجريبي...', 'warning');

            try {
                // التحقق من وجود الموظف التجريبي
                const existingEmployee = employees.find(emp => emp.id === testEmployeeId);
                if (existingEmployee) {
                    addResult('ℹ️ الموظف التجريبي موجود بالفعل', 'info');
                    showEmployeeInfo(existingEmployee);
                    updateStatus('✅ الموظف التجريبي جاهز', 'success');
                    return;
                }

                // إنشاء موظف تجريبي جديد
                const testEmployee = {
                    id: testEmployeeId,
                    name: "أحمد محمد البدلات",
                    position: "مهندس البدلات",
                    employeeCode: "ALLOW001",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/28a745/ffffff?text=ALLOWANCES",
                    employmentType: "monthly",
                    dateAdded: new Date().toISOString(),
                    salary: {
                        basic: 8000,
                        total: 8000
                    }
                };

                // إضافة الموظف إلى النظام
                employees.push(testEmployee);
                saveEmployeesToLocalStorage();

                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                showEmployeeInfo(testEmployee);
                updateStatus('✅ الموظف التجريبي جاهز', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف التجريبي: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف التجريبي', 'error');
            }
        }

        // دالة لعرض معلومات الموظف
        function showEmployeeInfo(employee) {
            const employeeInfo = document.getElementById('employeeInfo');
            employeeInfo.innerHTML = `
                <h4>📋 معلومات الموظف التجريبي</h4>
                <p><strong>الاسم:</strong> ${employee.name}</p>
                <p><strong>الوظيفة:</strong> ${employee.position}</p>
                <p><strong>كود الموظف:</strong> ${employee.employeeCode}</p>
                <p><strong>ID:</strong> ${employee.id}</p>
            `;
            employeeInfo.style.display = 'block';
        }

        // دالة لإضافة بدلات تجريبية
        function addSampleAllowances() {
            addResult('💰 إضافة بدلات تجريبية...', 'info');
            updateStatus('جاري إضافة البدلات التجريبية...', 'warning');

            try {
                // التحقق من وجود الموظف التجريبي
                const employee = employees.find(emp => emp.id === testEmployeeId);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف التجريبي غير موجود', 'error');
                    return;
                }

                // إنشاء بدلات تجريبية
                const sampleAllowances = [
                    {
                        id: Date.now() + 1,
                        employeeId: testEmployeeId,
                        month: 12,
                        year: 2024,
                        type: 'transport',
                        amount: 500,
                        description: 'بدل انتقالات شهر ديسمبر',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: testEmployeeId,
                        month: 12,
                        year: 2024,
                        type: 'expat',
                        amount: 800,
                        description: 'بدل اغتراب شهر ديسمبر',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: testEmployeeId,
                        month: 11,
                        year: 2024,
                        type: 'meal',
                        amount: 300,
                        description: 'بدل وجبات شهر نوفمبر',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ];

                // إضافة البدلات إلى النظام
                sampleAllowances.forEach(allowance => {
                    allowancesData.push(allowance);
                });

                // حفظ البيانات
                saveAllowancesData();

                addResult('✅ تم إضافة 3 بدلات تجريبية بنجاح', 'success');
                addResult('🚗 بدل انتقالات: 500 ج.م', 'info');
                addResult('🌍 بدل اغتراب: 800 ج.م', 'info');
                addResult('🍽️ بدل وجبات: 300 ج.م', 'info');
                updateStatus('✅ البدلات التجريبية جاهزة', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إضافة البدلات التجريبية: ${error.message}`, 'error');
                updateStatus('❌ فشل في إضافة البدلات التجريبية', 'error');
            }
        }

        // دالة لفتح نافذة البدلات
        function openAllowancesWindow() {
            addResult('🪟 فتح نافذة البدلات...', 'info');
            updateStatus('جاري فتح نافذة البدلات...', 'warning');

            try {
                // التحقق من وجود الموظف التجريبي
                const employee = employees.find(emp => emp.id === testEmployeeId);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف التجريبي غير موجود', 'error');
                    return;
                }

                // فتح نافذة البدلات
                openAllowancesModal(testEmployeeId);

                addResult('✅ تم فتح نافذة البدلات بنجاح!', 'success');
                addResult('💡 يمكنك الآن اختبار التعديل المباشر في الجدول', 'info');
                updateStatus('✅ نافذة البدلات مفتوحة', 'success');

                // فحص النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('allowancesModal');
                    if (modal) {
                        addResult('🎉 النافذة مفتوحة بالتصميم الجديد مع التعديل المباشر!', 'success');
                        addResult('📝 جرب تعديل المبالغ والأوصاف مباشرة في الجدول', 'info');
                        addResult('➕ جرب إضافة بدل جديد باستخدام النموذج أعلى الجدول', 'info');
                    } else {
                        addResult('❌ فشل في فتح النافذة', 'error');
                    }
                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة البدلات: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // دالة لاختبار التعديل المباشر
        function testInlineEditing() {
            addResult('🧪 اختبار التعديل المباشر...', 'info');
            updateStatus('جاري اختبار التعديل المباشر...', 'warning');

            try {
                // التحقق من وجود النافذة
                const modal = document.getElementById('allowancesModal');
                if (!modal) {
                    addResult('❌ يرجى فتح نافذة البدلات أولاً', 'error');
                    updateStatus('❌ النافذة غير مفتوحة', 'error');
                    return;
                }

                // البحث عن حقول التعديل في الجدول
                const amountInputs = modal.querySelectorAll('input[type="number"]');
                const textInputs = modal.querySelectorAll('input[type="text"]');
                const selects = modal.querySelectorAll('select');

                addResult(`✅ تم العثور على ${amountInputs.length} حقل مبلغ`, 'success');
                addResult(`✅ تم العثور على ${textInputs.length} حقل نص`, 'success');
                addResult(`✅ تم العثور على ${selects.length} قائمة اختيار`, 'success');

                if (amountInputs.length > 0) {
                    addResult('💡 يمكنك تعديل المبالغ مباشرة في الجدول', 'info');
                    addResult('💡 التغييرات تُحفظ تلقائياً عند تغيير القيمة', 'info');
                }

                if (textInputs.length > 0) {
                    addResult('💡 يمكنك تعديل الأوصاف مباشرة في الجدول', 'info');
                }

                addResult('🎯 اختبر التعديل المباشر بتغيير أي قيمة في الجدول', 'success');
                updateStatus('✅ التعديل المباشر جاهز للاختبار', 'success');

            } catch (error) {
                addResult(`❌ خطأ في اختبار التعديل المباشر: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار التعديل المباشر', 'error');
            }
        }

        // دالة لفحص بيانات البدلات
        function checkAllowancesData() {
            addResult('🔍 فحص بيانات البدلات...', 'info');
            updateStatus('جاري فحص البيانات...', 'warning');

            try {
                // فحص المتغير العام
                if (typeof allowancesData === 'undefined') {
                    addResult('❌ متغير البدلات غير موجود', 'error');
                    updateStatus('❌ متغير البدلات غير موجود', 'error');
                    return;
                }

                addResult(`📊 إجمالي البدلات في النظام: ${allowancesData.length}`, 'info');

                // فحص بدلات الموظف التجريبي
                const employeeAllowances = allowancesData.filter(record =>
                    parseInt(record.employeeId) === testEmployeeId
                );

                addResult(`👤 بدلات الموظف التجريبي: ${employeeAllowances.length}`, 'info');

                if (employeeAllowances.length > 0) {
                    let totalAmount = 0;
                    employeeAllowances.forEach((allowance, index) => {
                        const amount = parseFloat(allowance.amount || 0);
                        totalAmount += amount;
                        addResult(`${index + 1}. ${allowance.type}: ${amount} ج.م - ${allowance.description || 'بدون وصف'}`, 'success');
                    });
                    addResult(`💰 إجمالي البدلات: ${totalAmount.toLocaleString()} ج.م`, 'success');
                } else {
                    addResult('⚠️ لا توجد بدلات للموظف التجريبي', 'warning');
                }

                // فحص localStorage
                const savedData = localStorage.getItem('allowancesData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    addResult(`💾 البيانات المحفوظة: ${parsedData.length} سجل`, 'info');
                } else {
                    addResult('⚠️ لا توجد بيانات محفوظة في localStorage', 'warning');
                }

                updateStatus('✅ تم فحص البيانات بنجاح', 'success');

            } catch (error) {
                addResult(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
                updateStatus('❌ فشل في فحص البيانات', 'error');
            }
        }

        // دالة لمسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // دالة لفتح التطبيق الأصلي
        function openOriginalApp() {
            window.open('index.html', '_blank');
            addResult('🔗 تم فتح التطبيق الأصلي في نافذة جديدة', 'info');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في اختبار نافذة البدلات الجديدة!', 'info');
                addResult('💡 هذه النافذة تدعم التعديل المباشر في الجدول', 'success');
                addResult('📋 ابدأ بإنشاء موظف تجريبي وإضافة بدلات تجريبية', 'info');
                updateStatus('✅ النظام جاهز للاختبار', 'success');
            }, 500);
        });
    </script>
</body>
</html>
