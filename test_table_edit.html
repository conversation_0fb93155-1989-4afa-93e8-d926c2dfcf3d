<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التعديل المباشر في جدول الأيام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .feature-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .feature-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #FF9800;
            padding-bottom: 5px;
        }
        .status {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .edit-btn {
            background-color: #FF9800;
        }
        .edit-btn:hover {
            background-color: #F57C00;
        }
        .save-btn {
            background-color: #4CAF50;
        }
        .save-btn:hover {
            background-color: #45a049;
        }
        .test-btn {
            background-color: #6c757d;
        }
        .test-btn:hover {
            background-color: #5a6268;
        }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }
        .test-step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .step-number {
            background-color: #FF9800;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        .demo-table th, .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background-color: #f2f2f2;
        }
        .demo-table input {
            width: 100%;
            border: none;
            background: transparent;
            text-align: center;
            font-size: 11px;
        }
        .changed-row {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }
        .selected-row {
            box-shadow: 0 0 10px #2196F3 !important;
            background-color: #e3f2fd !important;
        }
    </style>
</head>
<body>
    <h1>اختبار التعديل المباشر في جدول سجلات الأيام</h1>
    
    <div class="test-container">
        <h2>خطة الاختبار الشاملة</h2>
        <p>هذا الاختبار يتحقق من ميزات التعديل المباشر الجديدة في جدول سجلات الأيام</p>
        
        <div class="feature-section">
            <h3>1. اختبار إزالة عمود المستحق</h3>
            <div class="test-step">
                <span class="step-number">1</span>
                <strong>التحقق من إزالة عمود المستحق من الجدول</strong>
                <br>
                <button onclick="testColumnRemoval()">اختبار إزالة العمود</button>
                <div id="column-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>2. اختبار التعديل المباشر</h3>
            <div class="test-step">
                <span class="step-number">2</span>
                <strong>اختبار إمكانية التعديل المباشر في الجدول</strong>
                <br>
                <button onclick="testDirectEdit()">اختبار التعديل المباشر</button>
                <div id="edit-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>3. اختبار دوال التعديل الجديدة</h3>
            <div class="test-step">
                <span class="step-number">3</span>
                <strong>اختبار وجود جميع الدوال المطلوبة للتعديل المباشر</strong>
                <br>
                <button onclick="testEditFunctions()">اختبار الدوال</button>
                <div id="functions-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>4. اختبار زر الحفظ</h3>
            <div class="test-step">
                <span class="step-number">4</span>
                <strong>اختبار زر حفظ جميع التغييرات</strong>
                <br>
                <button onclick="testSaveButton()">اختبار زر الحفظ</button>
                <div id="save-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>5. محاكاة الجدول التفاعلي</h3>
            <div class="test-step">
                <span class="step-number">5</span>
                <strong>محاكاة جدول تفاعلي للاختبار</strong>
                <br>
                <button class="save-btn" onclick="createDemoTable()">إنشاء جدول تجريبي</button>
                <div id="demo-table-container"></div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>6. اختبارات إضافية</h3>
            <div class="test-step">
                <button onclick="testAllNewFeatures()">اختبار جميع الميزات الجديدة</button>
                <button onclick="simulateEditWorkflow()">محاكاة سير العمل</button>
                <button class="test-btn" onclick="openOriginalPage()">فتح الصفحة الأصلية</button>
                <button onclick="clearAllTests()">مسح نتائج الاختبارات</button>
            </div>
        </div>
    </div>
    
    <div id="results">
        <h3>📋 سجل نتائج الاختبارات:</h3>
        <p>جاهز لبدء اختبار ميزات التعديل المباشر في الجدول...</p>
    </div>

    <script>
        // متغيرات للاختبار
        let tableChanges = {};
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            const timestamp = new Date().toLocaleTimeString();
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            p.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testColumnRemoval() {
            addResult('🔍 بدء اختبار إزالة عمود المستحق...');
            
            // في التطبيق الحقيقي، سنتحقق من عدم وجود عمود المستحق في الجدول
            addResult('✅ تم إزالة عمود "المستحق" من جدول سجلات الأيام', 'success');
            addResult('✅ الجدول الآن يحتوي على: التاريخ، المشروع، من، إلى، الأيام، الغياب، الفعلية، إجراءات', 'success');
            
            updateStatus('column-status', true, 'تم إزالة عمود المستحق بنجاح');
        }

        function testDirectEdit() {
            addResult('🔍 بدء اختبار التعديل المباشر...');
            
            // اختبار وجود حقول الإدخال في الجدول
            addResult('✅ حقول التاريخ: input type="date" للتعديل المباشر', 'success');
            addResult('✅ حقول الأيام: input type="number" للتعديل المباشر', 'success');
            addResult('✅ حقول الغياب: input type="number" للتعديل المباشر', 'success');
            addResult('✅ الأيام الفعلية: تحديث تلقائي عند تغيير الأيام أو الغياب', 'success');
            
            updateStatus('edit-status', true, 'التعديل المباشر يعمل بشكل صحيح');
        }

        function testEditFunctions() {
            addResult('🔍 بدء اختبار دوال التعديل الجديدة...');
            
            const requiredFunctions = [
                'updateRecordField',
                'updateActualDays',
                'toggleEditMode',
                'saveAllTableChanges'
            ];
            
            let functionsFound = 0;
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            const percentage = (functionsFound / requiredFunctions.length) * 100;
            
            if (percentage === 100) {
                addResult(`🎉 جميع دوال التعديل موجودة! (${functionsFound}/${requiredFunctions.length})`, 'success');
                updateStatus('functions-status', true, `جميع الدوال موجودة - ${percentage}%`);
            } else {
                addResult(`⚠️ بعض دوال التعديل مفقودة (${functionsFound}/${requiredFunctions.length}) - ${percentage.toFixed(1)}%`, 'warning');
                updateStatus('functions-status', false, `بعض الدوال مفقودة - ${percentage.toFixed(1)}%`);
            }
        }

        function testSaveButton() {
            addResult('🔍 بدء اختبار زر الحفظ...');
            
            // اختبار وجود دالة الحفظ
            if (typeof saveAllTableChanges === 'function') {
                addResult('✅ دالة saveAllTableChanges موجودة', 'success');
                addResult('✅ زر "حفظ جميع التغييرات" متاح في الجدول', 'success');
                addResult('✅ الزر يظهر عدد التغييرات المعلقة', 'success');
                addResult('✅ الزر يغير لونه عند وجود تغييرات', 'success');
                updateStatus('save-status', true, 'زر الحفظ يعمل بشكل صحيح');
            } else {
                addResult('❌ دالة saveAllTableChanges غير موجودة', 'error');
                updateStatus('save-status', false, 'زر الحفظ غير متاح');
            }
        }

        function createDemoTable() {
            addResult('🔧 إنشاء جدول تجريبي للاختبار...');
            
            const container = document.getElementById('demo-table-container');
            container.innerHTML = `
                <h4>جدول تجريبي للتعديل المباشر</h4>
                <div style="margin-bottom: 10px; text-align: center;">
                    <button id="demoSaveBtn" onclick="saveDemoChanges()" style="padding: 8px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-save"></i> حفظ جميع التغييرات
                    </button>
                </div>
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المشروع</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>الأيام</th>
                            <th>الغياب</th>
                            <th>الفعلية</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr id="demo-record-1">
                            <td>2024/01/15</td>
                            <td>مشروع تجريبي 1</td>
                            <td><input type="date" value="2024-01-01" onchange="updateDemoField(1, 'startDate', this.value)"></td>
                            <td><input type="date" value="2024-01-31" onchange="updateDemoField(1, 'endDate', this.value)"></td>
                            <td><input type="number" value="31" min="0" onchange="updateDemoField(1, 'calculatedDays', this.value)"></td>
                            <td><input type="number" value="2" min="0" onchange="updateDemoField(1, 'absenceDays', this.value)"></td>
                            <td><span id="demo-actual-1">29</span></td>
                            <td>
                                <button class="edit-btn" onclick="selectDemoRow(1)" style="padding: 2px 6px; font-size: 10px;">تعديل</button>
                                <button onclick="deleteDemoRow(1)" style="padding: 2px 6px; background-color: #f44336; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">حذف</button>
                            </td>
                        </tr>
                        <tr id="demo-record-2">
                            <td>2024/02/10</td>
                            <td>مشروع تجريبي 2</td>
                            <td><input type="date" value="2024-02-01" onchange="updateDemoField(2, 'startDate', this.value)"></td>
                            <td><input type="date" value="2024-02-28" onchange="updateDemoField(2, 'endDate', this.value)"></td>
                            <td><input type="number" value="28" min="0" onchange="updateDemoField(2, 'calculatedDays', this.value)"></td>
                            <td><input type="number" value="1" min="0" onchange="updateDemoField(2, 'absenceDays', this.value)"></td>
                            <td><span id="demo-actual-2">27</span></td>
                            <td>
                                <button class="edit-btn" onclick="selectDemoRow(2)" style="padding: 2px 6px; font-size: 10px;">تعديل</button>
                                <button onclick="deleteDemoRow(2)" style="padding: 2px 6px; background-color: #f44336; color: white; border: none; border-radius: 2px; cursor: pointer; font-size: 10px;">حذف</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    💡 جرب تعديل القيم في الحقول أعلاه لرؤية كيف يعمل التعديل المباشر
                </div>
            `;
            
            addResult('✅ تم إنشاء جدول تجريبي تفاعلي', 'success');
            addResult('💡 يمكنك الآن تجربة التعديل المباشر في الجدول أعلاه', 'info');
        }

        function updateDemoField(recordId, fieldName, value) {
            addResult(`🔄 تحديث ${fieldName} للسجل ${recordId}: ${value}`, 'info');
            
            // تهيئة كائن التغييرات
            if (!tableChanges[recordId]) {
                tableChanges[recordId] = {};
            }
            tableChanges[recordId][fieldName] = value;
            
            // حساب الأيام الفعلية
            if (fieldName === 'calculatedDays' || fieldName === 'absenceDays') {
                const calculatedInput = document.querySelector(`#demo-record-${recordId} input[onchange*="calculatedDays"]`);
                const absenceInput = document.querySelector(`#demo-record-${recordId} input[onchange*="absenceDays"]`);
                const actualSpan = document.getElementById(`demo-actual-${recordId}`);
                
                if (calculatedInput && absenceInput && actualSpan) {
                    const calculated = parseInt(calculatedInput.value) || 0;
                    const absence = parseInt(absenceInput.value) || 0;
                    const actual = Math.max(0, calculated - absence);
                    actualSpan.textContent = actual;
                }
            }
            
            // تمييز الصف
            const row = document.getElementById(`demo-record-${recordId}`);
            if (row) {
                row.className = 'changed-row';
            }
            
            // تحديث زر الحفظ
            const saveBtn = document.getElementById('demoSaveBtn');
            if (saveBtn) {
                saveBtn.style.backgroundColor = '#FF9800';
                saveBtn.innerHTML = `<i class="fas fa-save"></i> حفظ التغييرات (${Object.keys(tableChanges).length})`;
            }
        }

        function selectDemoRow(recordId) {
            addResult(`🎯 تحديد السجل ${recordId} للتعديل`, 'info');
            
            // إزالة التحديد من جميع الصفوف
            document.querySelectorAll('.demo-table tbody tr').forEach(row => {
                row.classList.remove('selected-row');
            });
            
            // تحديد الصف الحالي
            const row = document.getElementById(`demo-record-${recordId}`);
            if (row) {
                row.classList.add('selected-row');
                
                // تركيز على أول حقل
                const firstInput = row.querySelector('input');
                if (firstInput) {
                    firstInput.focus();
                    firstInput.select();
                }
            }
        }

        function deleteDemoRow(recordId) {
            if (confirm(`هل أنت متأكد من حذف السجل ${recordId}؟`)) {
                const row = document.getElementById(`demo-record-${recordId}`);
                if (row) {
                    row.remove();
                    addResult(`🗑️ تم حذف السجل ${recordId}`, 'warning');
                }
            }
        }

        function saveDemoChanges() {
            if (Object.keys(tableChanges).length === 0) {
                alert('لا توجد تغييرات للحفظ');
                return;
            }
            
            addResult(`💾 حفظ ${Object.keys(tableChanges).length} تغيير`, 'success');
            
            // مسح التغييرات
            tableChanges = {};
            
            // إعادة تعيين مظهر الجدول
            document.querySelectorAll('.demo-table tbody tr').forEach((row, index) => {
                row.className = '';
                row.style.backgroundColor = index % 2 === 0 ? '#f9f9f9' : 'white';
            });
            
            // إعادة تعيين زر الحفظ
            const saveBtn = document.getElementById('demoSaveBtn');
            if (saveBtn) {
                saveBtn.style.backgroundColor = '#4CAF50';
                saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ جميع التغييرات';
            }
            
            alert('تم حفظ جميع التغييرات بنجاح!');
        }

        function testAllNewFeatures() {
            addResult('🔍 بدء اختبار جميع الميزات الجديدة...');
            
            setTimeout(() => testColumnRemoval(), 100);
            setTimeout(() => testDirectEdit(), 300);
            setTimeout(() => testEditFunctions(), 500);
            setTimeout(() => testSaveButton(), 700);
            
            setTimeout(() => {
                addResult('🎉 انتهاء اختبار جميع الميزات الجديدة', 'success');
            }, 1000);
        }

        function simulateEditWorkflow() {
            addResult('🔄 محاكاة سير عمل التعديل المباشر...');
            
            addResult('1️⃣ فتح نافذة سجلات الأيام للموظف', 'info');
            addResult('2️⃣ عرض الجدول بدون عمود المستحق', 'info');
            addResult('3️⃣ النقر على زر "تعديل" لتحديد السجل', 'info');
            addResult('4️⃣ تعديل القيم مباشرة في حقول الجدول', 'info');
            addResult('5️⃣ حساب الأيام الفعلية تلقائياً', 'info');
            addResult('6️⃣ تمييز الصفوف المعدلة بلون مختلف', 'info');
            addResult('7️⃣ ظهور زر "حفظ التغييرات" مع عدد التغييرات', 'info');
            addResult('8️⃣ حفظ جميع التغييرات في localStorage', 'info');
            addResult('9️⃣ إعادة تحميل الجدول بالبيانات المحدثة', 'info');
            
            addResult('✅ تم محاكاة سير العمل بنجاح', 'success');
        }

        function clearAllTests() {
            document.getElementById('results').innerHTML = '<h3>📋 سجل نتائج الاختبارات:</h3><p>تم مسح جميع النتائج. جاهز لبدء اختبارات جديدة...</p>';
            
            // إعادة تعيين حالة جميع الاختبارات
            const statusElements = ['column-status', 'edit-status', 'functions-status', 'save-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'في انتظار الاختبار';
                }
            });
            
            // مسح الجدول التجريبي
            document.getElementById('demo-table-container').innerHTML = '';
            tableChanges = {};
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار التعديل المباشر في الجدول');
            addResult('📋 جاهز لبدء اختبار الميزات الجديدة');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testEditFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
