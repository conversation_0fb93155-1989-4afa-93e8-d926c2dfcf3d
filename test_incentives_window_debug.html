<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة نافذة الحوافز</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #e74c3c;
        }

        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .button {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .button.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            color: #28a745;
        }

        .result-item.error {
            color: #dc3545;
        }

        .result-item.warning {
            color: #ffc107;
        }

        .result-item.info {
            color: #17a2b8;
        }

        .icon {
            font-size: 1.2em;
            margin-left: 8px;
        }

        .employee-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .employee-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .employee-card button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .employee-card button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص مشكلة نافذة الحوافز</h1>
            <p>فحص شامل لمعرفة سبب عدم فتح النافذة</p>
        </div>

        <div class="content">
            <div class="status info" id="mainStatus">
                <strong>الحالة:</strong> جاري التحضير للتشخيص...
            </div>

            <div class="test-section">
                <h3>🔧 خطوات التشخيص</h3>
                <button class="button" onclick="step1_checkBasics()">
                    <span class="icon">1️⃣</span>
                    فحص الأساسيات
                </button>
                <button class="button warning" onclick="step2_createTestEmployee()">
                    <span class="icon">2️⃣</span>
                    إنشاء موظف تجريبي
                </button>
                <button class="button success" onclick="step3_testWindow()">
                    <span class="icon">3️⃣</span>
                    اختبار فتح النافذة
                </button>
                <button class="button" onclick="step4_manualTest()">
                    <span class="icon">4️⃣</span>
                    اختبار يدوي
                </button>
            </div>

            <div class="test-section">
                <h3>👤 الموظف التجريبي</h3>
                <div id="employeeCard">
                    <div class="result-item info">لم يتم إنشاء موظف تجريبي بعد</div>
                </div>
            </div>

            <div class="test-section">
                <h3>📊 نتائج التشخيص</h3>
                <div class="results" id="diagnosticResults">
                    <div class="result-item info">انتظار بدء التشخيص...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('mainStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('diagnosticResults');
            const item = document.createElement('div');
            item.className = `result-item ${type}`;
            item.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(item);
            results.scrollTop = results.scrollHeight;
        }

        function step1_checkBasics() {
            updateStatus('جاري فحص الأساسيات...', 'warning');
            addResult('🔍 بدء فحص الأساسيات', 'info');
            
            // فحص المتغيرات الأساسية
            if (typeof employees !== 'undefined') {
                addResult(`✅ متغير employees موجود (${employees.length} موظف)`, 'success');
            } else {
                addResult('❌ متغير employees غير موجود', 'error');
            }

            if (typeof incentivesRewardsData !== 'undefined') {
                addResult(`✅ متغير incentivesRewardsData موجود (${incentivesRewardsData.length} سجل)`, 'success');
            } else {
                addResult('❌ متغير incentivesRewardsData غير موجود', 'error');
            }

            // فحص الدوال المطلوبة
            const requiredFunctions = [
                'viewIncentivesRewards',
                'closeIncentivesRewardsModal',
                'saveIncentiveReward',
                'deleteIncentiveReward',
                'showTemporaryMessage',
                'getMonthName'
            ];

            let functionsFound = 0;
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            if (functionsFound === requiredFunctions.length) {
                addResult(`✅ جميع الدوال المطلوبة موجودة (${functionsFound}/${requiredFunctions.length})`, 'success');
                updateStatus('✅ فحص الأساسيات مكتمل - جميع المتطلبات موجودة', 'success');
            } else {
                addResult(`❌ بعض الدوال مفقودة (${functionsFound}/${requiredFunctions.length})`, 'error');
                updateStatus('❌ فحص الأساسيات فشل - دوال مفقودة', 'error');
            }
        }

        function step2_createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('👤 بدء إنشاء موظف تجريبي', 'info');
            
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 99999,
                    name: 'موظف تجريبي - تشخيص النافذة',
                    employeeCode: 'DEBUG99999',
                    position: 'مطور تشخيص',
                    employmentType: 'monthly',
                    basicSalary: 5000,
                    createdAt: new Date().toISOString()
                };

                // التحقق من عدم وجود الموظف مسبقاً
                const existingEmployee = employees.find(emp => emp.id === 99999);
                if (existingEmployee) {
                    addResult('⚠️ الموظف التجريبي موجود مسبقاً', 'warning');
                } else {
                    employees.push(testEmployee);
                    addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    
                    // حفظ البيانات
                    if (typeof saveEmployeesToLocalStorage === 'function') {
                        saveEmployeesToLocalStorage();
                        addResult('✅ تم حفظ بيانات الموظف', 'success');
                    }
                }

                // عرض بطاقة الموظف
                const employeeCard = document.getElementById('employeeCard');
                employeeCard.innerHTML = `
                    <div class="employee-card">
                        <h4>${testEmployee.name}</h4>
                        <p><strong>الكود:</strong> ${testEmployee.employeeCode}</p>
                        <p><strong>الوظيفة:</strong> ${testEmployee.position}</p>
                        <p><strong>الراتب:</strong> ${testEmployee.basicSalary.toLocaleString()} ج.م</p>
                        <button onclick="testDirectCall()">اختبار مباشر للنافذة</button>
                        <button onclick="testWithConsole()">اختبار مع console</button>
                    </div>
                `;

                updateStatus('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف التجريبي', 'error');
            }
        }

        function step3_testWindow() {
            updateStatus('جاري اختبار فتح النافذة...', 'warning');
            addResult('🪟 بدء اختبار فتح نافذة الحوافز', 'info');
            
            try {
                // التحقق من وجود الموظف التجريبي
                const employee = employees.find(emp => emp.id === 99999);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ فشل - الموظف التجريبي غير موجود', 'error');
                    return;
                }

                addResult('✅ تم العثور على الموظف التجريبي', 'success');

                // اختبار فتح النافذة
                addResult('🔄 محاولة فتح نافذة الحوافز...', 'info');
                
                if (typeof viewIncentivesRewards === 'function') {
                    // محاولة فتح النافذة
                    viewIncentivesRewards(99999);
                    
                    // التحقق من وجود النافذة
                    setTimeout(() => {
                        const modal = document.getElementById('incentivesRewardsModal');
                        if (modal) {
                            addResult('✅ تم فتح النافذة بنجاح!', 'success');
                            addResult('👀 النافذة مرئية الآن - تحقق من الشاشة', 'info');
                            updateStatus('✅ نجح فتح النافذة!', 'success');
                        } else {
                            addResult('❌ النافذة لم تظهر رغم استدعاء الدالة', 'error');
                            updateStatus('❌ فشل في فتح النافذة', 'error');
                        }
                    }, 500);
                    
                } else {
                    addResult('❌ دالة viewIncentivesRewards غير موجودة', 'error');
                    updateStatus('❌ فشل - الدالة غير موجودة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في اختبار النافذة: ${error.message}`, 'error');
                addResult(`📍 تفاصيل الخطأ: ${error.stack}`, 'error');
                updateStatus('❌ فشل في اختبار النافذة', 'error');
            }
        }

        function step4_manualTest() {
            updateStatus('جاري الاختبار اليدوي...', 'warning');
            addResult('🖱️ بدء الاختبار اليدوي', 'info');
            
            const employeeCard = document.getElementById('employeeCard');
            employeeCard.innerHTML += `
                <div style="margin-top: 15px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                    <h5>اختبار يدوي:</h5>
                    <p>اضغط على الأزرار أدناه لاختبار النافذة يدوياً:</p>
                    <button onclick="manualViewIncentives()">فتح نافذة الحوافز</button>
                    <button onclick="checkModalExists()">فحص وجود النافذة</button>
                    <button onclick="forceCloseModal()">إغلاق النافذة</button>
                </div>
            `;
            
            addResult('✅ تم إضافة أزرار الاختبار اليدوي', 'success');
            updateStatus('✅ الاختبار اليدوي جاهز', 'success');
        }

        function testDirectCall() {
            addResult('🎯 اختبار مباشر لدالة viewIncentivesRewards', 'info');
            try {
                viewIncentivesRewards(99999);
                addResult('✅ تم استدعاء الدالة بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في الاستدعاء المباشر: ${error.message}`, 'error');
            }
        }

        function testWithConsole() {
            addResult('🖥️ اختبار مع console.log', 'info');
            console.log('=== بدء اختبار نافذة الحوافز ===');
            console.log('الموظف المطلوب:', employees.find(emp => emp.id === 99999));
            console.log('بيانات الحوافز:', incentivesRewardsData);
            
            try {
                console.log('محاولة فتح النافذة...');
                viewIncentivesRewards(99999);
                console.log('تم استدعاء الدالة');
                addResult('✅ تحقق من console للتفاصيل', 'success');
            } catch (error) {
                console.error('خطأ:', error);
                addResult(`❌ خطأ: ${error.message}`, 'error');
            }
        }

        function manualViewIncentives() {
            addResult('🔧 فتح النافذة يدوياً...', 'info');
            viewIncentivesRewards(99999);
        }

        function checkModalExists() {
            const modal = document.getElementById('incentivesRewardsModal');
            if (modal) {
                addResult('✅ النافذة موجودة في DOM', 'success');
                addResult(`📏 أبعاد النافذة: ${modal.offsetWidth}x${modal.offsetHeight}`, 'info');
                addResult(`👁️ مرئية: ${modal.style.display !== 'none' ? 'نعم' : 'لا'}`, 'info');
            } else {
                addResult('❌ النافذة غير موجودة في DOM', 'error');
            }
        }

        function forceCloseModal() {
            const modal = document.getElementById('incentivesRewardsModal');
            if (modal) {
                modal.remove();
                addResult('✅ تم إغلاق النافذة', 'success');
            } else {
                addResult('⚠️ لا توجد نافذة لإغلاقها', 'warning');
            }
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateStatus('✅ النظام محمل - جاهز للتشخيص', 'success');
                addResult('🚀 النظام جاهز للتشخيص', 'success');
                addResult('💡 ابدأ بالخطوة الأولى: فحص الأساسيات', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
