<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قائمة المشروعات المنسدلة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .projects-btn {
            background-color: #FF9800;
        }
        .projects-btn:hover {
            background-color: #F57C00;
        }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .project-option {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
        }
        .planning { background-color: #FFF3CD; color: #856404; }
        .in-progress { background-color: #D1ECF1; color: #0C5460; }
        .completed { background-color: #D4EDDA; color: #155724; }
        .on-hold { background-color: #F8D7DA; color: #721C24; }
    </style>
</head>
<body>
    <h1>اختبار قائمة المشروعات المنسدلة في حساب الأيام</h1>
    
    <div class="test-container">
        <h2>محاكاة نافذة حساب الأيام</h2>
        
        <div class="form-group">
            <label for="projectSelect">المشروع</label>
            <select id="projectSelect">
                <option value="">اختر مشروع...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="startDate">تاريخ البداية</label>
            <input type="date" id="startDate">
        </div>
        
        <div class="form-group">
            <label for="endDate">تاريخ النهاية</label>
            <input type="date" id="endDate">
        </div>
        
        <div class="form-group">
            <label for="calculatedDays">عدد الأيام المحسوبة</label>
            <input type="number" id="calculatedDays" readonly>
        </div>
        
        <div id="project-info" class="status info" style="display: none;">
            معلومات المشروع المختار ستظهر هنا
        </div>
        
        <div>
            <button onclick="testProjectsDropdown()">اختبار القائمة المنسدلة</button>
            <button onclick="loadTestProjects()">تحميل مشروعات تجريبية</button>
            <button onclick="clearProjects()">مسح المشروعات</button>
            <button class="projects-btn" onclick="openProjectsModal()">إدارة المشروعات</button>
            <button onclick="openOriginalPage()">فتح الصفحة الأصلية</button>
        </div>
    </div>
    
    <div id="results">
        <h3>نتائج الاختبارات:</h3>
        <p>جاهز لبدء الاختبارات...</p>
    </div>

    <script>
        // متغيرات للاختبار
        let projects = [];
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            p.style.color = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#007bff';
            p.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function getProjectStatusText(status) {
            const statusMap = {
                'planning': 'تخطيط',
                'in-progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'on-hold': 'متوقف'
            };
            return statusMap[status] || status;
        }

        function populateProjectsDropdown() {
            addResult('بدء ملء قائمة المشروعات المنسدلة...');
            
            const projectSelect = document.getElementById('projectSelect');
            if (!projectSelect) {
                addResult('✗ لم يتم العثور على عنصر projectSelect', 'error');
                return;
            }
            
            // مسح الخيارات الحالية
            projectSelect.innerHTML = '<option value="">اختر مشروع...</option>';
            
            // إضافة خيار لإدخال مشروع جديد
            const newProjectOption = document.createElement('option');
            newProjectOption.value = 'new_project';
            newProjectOption.textContent = '+ إضافة مشروع جديد';
            newProjectOption.style.fontWeight = 'bold';
            newProjectOption.style.color = '#FF9800';
            projectSelect.appendChild(newProjectOption);
            
            // إضافة فاصل
            const separatorOption = document.createElement('option');
            separatorOption.disabled = true;
            separatorOption.textContent = '──────────────';
            projectSelect.appendChild(separatorOption);
            
            // إضافة المشروعات الموجودة
            if (projects && projects.length > 0) {
                projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = `${project.name} (${getProjectStatusText(project.status)})`;
                    
                    // تلوين الخيار حسب حالة المشروع
                    switch(project.status) {
                        case 'planning':
                            option.style.color = '#FFC107';
                            break;
                        case 'in-progress':
                            option.style.color = '#2196F3';
                            break;
                        case 'completed':
                            option.style.color = '#4CAF50';
                            break;
                        case 'on-hold':
                            option.style.color = '#FF5722';
                            break;
                    }
                    
                    projectSelect.appendChild(option);
                });
                
                addResult(`✓ تم إضافة ${projects.length} مشروع إلى القائمة المنسدلة`, 'success');
            } else {
                // إضافة رسالة عدم وجود مشروعات
                const noProjectsOption = document.createElement('option');
                noProjectsOption.disabled = true;
                noProjectsOption.textContent = 'لا توجد مشروعات مسجلة';
                noProjectsOption.style.color = '#999';
                projectSelect.appendChild(noProjectsOption);
                
                addResult('⚠ لا توجد مشروعات لإضافتها إلى القائمة', 'warning');
            }
            
            // إضافة مستمع أحداث للقائمة المنسدلة
            projectSelect.onchange = function() {
                const projectInfo = document.getElementById('project-info');
                
                if (this.value === 'new_project') {
                    addResult('تم اختيار "إضافة مشروع جديد"', 'info');
                    projectInfo.style.display = 'none';
                    // في التطبيق الحقيقي، سيتم فتح نافذة المشروعات
                    alert('سيتم فتح نافذة إضافة مشروع جديد');
                    this.value = '';
                } else if (this.value) {
                    // تم اختيار مشروع موجود
                    const selectedProject = projects.find(p => p.id == this.value);
                    if (selectedProject) {
                        addResult(`✓ تم اختيار المشروع: ${selectedProject.name}`, 'success');
                        
                        // عرض معلومات المشروع
                        projectInfo.innerHTML = `
                            <strong>المشروع المختار:</strong> ${selectedProject.name}<br>
                            <strong>الحالة:</strong> ${getProjectStatusText(selectedProject.status)}<br>
                            <strong>الوصف:</strong> ${selectedProject.description || 'لا يوجد وصف'}<br>
                            <strong>الميزانية:</strong> ${selectedProject.budget ? selectedProject.budget.toLocaleString() + ' جنيه' : 'غير محددة'}
                        `;
                        projectInfo.className = `status ${selectedProject.status}`;
                        projectInfo.style.display = 'block';
                        
                        // ملء تواريخ المشروع تلقائياً
                        if (selectedProject.startDate) {
                            document.getElementById('startDate').value = selectedProject.startDate;
                            addResult(`تم ملء تاريخ البداية: ${selectedProject.startDate}`, 'info');
                        }
                        
                        if (selectedProject.endDate) {
                            document.getElementById('endDate').value = selectedProject.endDate;
                            addResult(`تم ملء تاريخ النهاية: ${selectedProject.endDate}`, 'info');
                        }
                        
                        // حساب عدد الأيام تلقائياً
                        if (selectedProject.startDate && selectedProject.endDate) {
                            const startDate = new Date(selectedProject.startDate);
                            const endDate = new Date(selectedProject.endDate);
                            const timeDiff = endDate.getTime() - startDate.getTime();
                            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                            
                            if (daysDiff > 0) {
                                document.getElementById('calculatedDays').value = daysDiff;
                                addResult(`✓ تم حساب عدد الأيام تلقائياً: ${daysDiff} يوم`, 'success');
                            }
                        }
                    }
                } else {
                    projectInfo.style.display = 'none';
                    addResult('تم إلغاء اختيار المشروع', 'info');
                }
            };
        }

        function testProjectsDropdown() {
            addResult('=== بدء اختبار قائمة المشروعات المنسدلة ===');
            populateProjectsDropdown();
            addResult('=== انتهاء الاختبار ===');
        }

        function loadTestProjects() {
            projects = [
                {
                    id: 1,
                    name: "تطوير نظام إدارة الموارد البشرية",
                    description: "تطوير نظام شامل لإدارة الموظفين والرواتب",
                    startDate: "2024-01-01",
                    endDate: "2024-06-30",
                    status: "in-progress",
                    budget: 50000
                },
                {
                    id: 2,
                    name: "تحديث البنية التحتية للشبكة",
                    description: "تحديث وتطوير شبكة الشركة الداخلية",
                    startDate: "2024-02-15",
                    endDate: "2024-04-30",
                    status: "planning",
                    budget: 30000
                },
                {
                    id: 3,
                    name: "تدريب الموظفين على النظام الجديد",
                    description: "برنامج تدريبي شامل للموظفين",
                    startDate: "2024-03-01",
                    endDate: "2024-03-31",
                    status: "completed",
                    budget: 15000
                }
            ];
            
            addResult(`✓ تم تحميل ${projects.length} مشروع تجريبي`, 'success');
            populateProjectsDropdown();
        }

        function clearProjects() {
            projects = [];
            addResult('تم مسح جميع المشروعات', 'warning');
            populateProjectsDropdown();
        }

        function openProjectsModal() {
            addResult('محاولة فتح نافذة إدارة المشروعات...', 'info');
            if (typeof window.openProjectsModal === 'function') {
                window.openProjectsModal();
                addResult('✓ تم فتح نافذة المشروعات', 'success');
            } else {
                alert('نافذة إدارة المشروعات متاحة في الصفحة الأصلية فقط');
                addResult('⚠ نافذة المشروعات غير متاحة في صفحة الاختبار', 'warning');
            }
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('تم تحميل صفحة اختبار قائمة المشروعات المنسدلة');
            addResult('انقر على "تحميل مشروعات تجريبية" ثم "اختبار القائمة المنسدلة"');
            
            // تحميل مشروعات تجريبية تلقائياً
            setTimeout(() => {
                loadTestProjects();
            }, 1000);
        });
    </script>
</body>
</html>
