<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للنافذة الجديدة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            margin: 10px;
            min-width: 200px;
        }

        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
        }

        .test-button.secondary:hover {
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
        }

        .instructions {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #4CAF50;
            text-align: right;
            margin: 20px 0;
        }

        .instructions h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .instructions ul {
            list-style: none;
            padding: 0;
        }

        .instructions li {
            color: #666;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .instructions li:before {
            content: "✅ ";
            color: #4CAF50;
            font-weight: bold;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: 500;
            background: #e3f2fd;
            color: #1565c0;
            border: 2px solid #2196F3;
        }

        .status.success {
            background: #f0fff4;
            color: #2e7d32;
            border: 2px solid #4CAF50;
        }

        .status.error {
            background: #fff5f5;
            color: #c62828;
            border: 2px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار النافذة الجديدة</h1>
            <p>اختبار سريع للنافذة البسيطة المحسنة</p>
        </div>

        <div>
            <button class="test-button" onclick="testNewWindow()">
                🚀 اختبار النافذة الجديدة
            </button>
            
            <button class="test-button secondary" onclick="openOriginalApp()">
                🌐 فتح التطبيق الأصلي
            </button>
        </div>

        <div class="instructions">
            <h3>🎯 مميزات النافذة الجديدة:</h3>
            <ul>
                <li>تصميم بسيط ونظيف</li>
                <li>حساب دقيق للبدلات اليومية</li>
                <li>عرض واضح لجميع مكونات الراتب</li>
                <li>واجهة مستخدم عصرية</li>
                <li>أزرار تفاعلية للإجراءات</li>
                <li>حجم مناسب وليس كبير</li>
            </ul>
        </div>

        <div class="status" id="status">
            🚀 جاهز للاختبار - اضغط على الزر الأخضر أعلاه
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // اختبار النافذة الجديدة
        function testNewWindow() {
            updateStatus('🧪 جاري اختبار النافذة الجديدة...', 'info');
            
            try {
                // محاولة استخدام الدالة الجديدة
                if (typeof testNewWindowNow === 'function') {
                    testNewWindowNow();
                    updateStatus('✅ تم تشغيل اختبار النافذة الجديدة بنجاح!', 'success');
                } else if (typeof showNewSalaryWindow === 'function') {
                    showNewSalaryWindow();
                    updateStatus('✅ تم فتح النافذة الجديدة بنجاح!', 'success');
                } else {
                    updateStatus('❌ دوال الاختبار غير متاحة', 'error');
                }
            } catch (error) {
                updateStatus(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('خطأ في اختبار النافذة:', error);
            }
        }

        // فتح التطبيق الأصلي
        function openOriginalApp() {
            updateStatus('🌐 فتح التطبيق الأصلي...', 'info');
            window.open('index.html', '_blank');
            updateStatus('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
        }

        // رسالة ترحيب عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateStatus('🎉 تم تحميل صفحة الاختبار بنجاح - جاهز للاختبار!', 'success');
                
                // رسالة في وحدة التحكم
                console.log(`
🎉 صفحة اختبار النافذة الجديدة جاهزة!

🚀 للاختبار السريع:
   testNewWindowNow()

🎯 أو اضغط على الزر الأخضر في الصفحة

✨ النافذة الجديدة تتميز بـ:
   • تصميم بسيط ونظيف
   • حساب دقيق للبدلات
   • واجهة عصرية ومتجاوبة
                `);
            }, 1000);
        });
    </script>
</body>
</html>
