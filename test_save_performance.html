<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أداء الحفظ المحسن</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6f42c1;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .performance-metrics {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #495057;
        }
        
        .console-monitor {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .improvement-list {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .improvement-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .improvement-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            background: #28a745;
        }
        
        .test-scenario {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .scenario-step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tachometer-alt"></i> اختبار أداء الحفظ المحسن</h1>
            <p>فحص التحسينات الجديدة في نظام الحفظ التلقائي</p>
        </div>

        <div class="improvement-list">
            <h4><i class="fas fa-rocket"></i> التحسينات المطبقة:</h4>
            
            <div class="improvement-item">
                <div class="improvement-icon">✓</div>
                <div>
                    <strong>تأخير ذكي للحفظ</strong>
                    <br><small>تجميع التغييرات المتعددة في عملية حفظ واحدة (500ms)</small>
                </div>
            </div>
            
            <div class="improvement-item">
                <div class="improvement-icon">✓</div>
                <div>
                    <strong>إلغاء الحفظ المتكرر</strong>
                    <br><small>منع الحفظ المتعدد للحقل نفسه</small>
                </div>
            </div>
            
            <div class="improvement-item">
                <div class="improvement-icon">✓</div>
                <div>
                    <strong>تحديث مظهر محسن</strong>
                    <br><small>تحديث جميع الصفوف المعدلة مرة واحدة</small>
                </div>
            </div>
            
            <div class="improvement-item">
                <div class="improvement-icon">✓</div>
                <div>
                    <strong>إدارة ذاكرة أفضل</strong>
                    <br><small>مسح تتبع التغييرات بعد الحفظ</small>
                </div>
            </div>
        </div>

        <div class="test-scenario">
            <h4><i class="fas fa-flask"></i> سيناريو الاختبار:</h4>
            
            <div class="scenario-step">
                <strong>1. إنشاء بيانات تجريبية</strong> - موظف مع سجلات أيام متعددة
            </div>
            
            <div class="scenario-step">
                <strong>2. فتح نافذة السجلات</strong> - تفعيل التعديل المباشر والحفظ التلقائي
            </div>
            
            <div class="scenario-step">
                <strong>3. تعديل سريع متعدد</strong> - تغيير نفس الحقل عدة مرات بسرعة
            </div>
            
            <div class="scenario-step">
                <strong>4. مراقبة الأداء</strong> - فحص عدد مرات الحفظ الفعلية
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button success" onclick="runPerformanceTest()">
                <i class="fas fa-play"></i> تشغيل اختبار الأداء
            </button>
            
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button warning" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>
            
            <button class="test-button danger" onclick="clearConsoleMonitor()">
                <i class="fas fa-eraser"></i> مسح المراقب
            </button>
        </div>

        <div class="performance-metrics">
            <h4><i class="fas fa-chart-line"></i> مقاييس الأداء:</h4>
            
            <div class="metric-item">
                <span>عدد التعديلات المرسلة:</span>
                <span class="metric-value" id="editCount">0</span>
            </div>
            
            <div class="metric-item">
                <span>عدد عمليات الحفظ الفعلية:</span>
                <span class="metric-value" id="saveCount">0</span>
            </div>
            
            <div class="metric-item">
                <span>نسبة التحسن:</span>
                <span class="metric-value" id="improvementRatio">0%</span>
            </div>
            
            <div class="metric-item">
                <span>زمن آخر حفظ:</span>
                <span class="metric-value" id="lastSaveTime">-</span>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار الأداء المحسن...</p>
        </div>

        <div class="console-monitor" id="consoleMonitor">
            [PERFORMANCE] مراقب الأداء جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let editCount = 0;
        let saveCount = 0;
        let testStartTime = 0;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addToMonitor(message) {
            const monitor = document.getElementById('consoleMonitor');
            const timestamp = new Date().toLocaleTimeString();
            monitor.innerHTML += `[${timestamp}] ${message}<br>`;
            monitor.scrollTop = monitor.scrollHeight;
        }

        function updateMetrics() {
            document.getElementById('editCount').textContent = editCount;
            document.getElementById('saveCount').textContent = saveCount;
            
            const improvement = editCount > 0 ? ((editCount - saveCount) / editCount * 100).toFixed(1) : 0;
            document.getElementById('improvementRatio').textContent = improvement + '%';
            
            document.getElementById('lastSaveTime').textContent = new Date().toLocaleTimeString();
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 777)) {
                    employees.push({
                        id: 777,
                        name: 'سارة أحمد - اختبار الأداء',
                        position: 'مطورة واجهات',
                        employeeCode: 'PERF777',
                        employmentType: 'monthly',
                        basicSalary: 6500
                    });
                }
                
                // إنشاء سجلات أيام متعددة
                const testRecords = [
                    {
                        id: Date.now() + 100,
                        employeeId: 777,
                        projectName: 'مشروع اختبار الأداء الأول',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 1,
                        overtimeHours: 5,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 200,
                        employeeId: 777,
                        projectName: 'مشروع اختبار الأداء الثاني',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 0,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 300,
                        employeeId: 777,
                        projectName: 'مشروع اختبار الأداء الثالث',
                        startDate: '2024-03-01',
                        endDate: '2024-03-31',
                        calculatedDays: 31,
                        absenceDays: 2,
                        overtimeHours: 12,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });
                
                // حفظ البيانات
                saveDaysDataToLocalStorage();
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح! (موظف واحد + 3 سجلات)', 'success');
                addToMonitor('✓ تم إنشاء البيانات التجريبية');
                
            } catch (error) {
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
                addToMonitor('✗ خطأ في إنشاء البيانات: ' + error.message);
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 777)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                viewDaysRecords(777);
                updateStatus('تم فتح نافذة السجلات! فعل الحفظ التلقائي وابدأ الاختبار', 'success');
                addToMonitor('✓ تم فتح نافذة السجلات للموظف 777');
                
            } catch (error) {
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
                addToMonitor('✗ خطأ في فتح النافذة: ' + error.message);
            }
        }

        function runPerformanceTest() {
            updateStatus('بدء اختبار الأداء...', 'warning');
            addToMonitor('=== بدء اختبار الأداء ===');
            
            // إعادة تعيين العدادات
            editCount = 0;
            saveCount = 0;
            testStartTime = Date.now();
            updateMetrics();
            
            // محاكاة تعديلات سريعة متعددة
            setTimeout(() => {
                addToMonitor('محاكاة 10 تعديلات سريعة...');
                
                for (let i = 1; i <= 10; i++) {
                    setTimeout(() => {
                        editCount++;
                        addToMonitor(`تعديل رقم ${i}: تغيير قيمة إلى ${20 + i}`);
                        updateMetrics();
                        
                        if (i === 10) {
                            // انتظار انتهاء جميع عمليات الحفظ
                            setTimeout(() => {
                                const testDuration = Date.now() - testStartTime;
                                addToMonitor(`=== انتهاء الاختبار ===`);
                                addToMonitor(`المدة الإجمالية: ${testDuration}ms`);
                                addToMonitor(`التحسن: ${editCount - saveCount} عملية حفظ تم توفيرها`);
                                
                                updateStatus(`اختبار الأداء مكتمل! تم توفير ${editCount - saveCount} عملية حفظ`, 'success');
                            }, 2000);
                        }
                    }, i * 100); // تعديل كل 100ms
                }
            }, 1000);
        }

        function clearConsoleMonitor() {
            document.getElementById('consoleMonitor').innerHTML = '[PERFORMANCE] تم مسح المراقب...<br>';
            editCount = 0;
            saveCount = 0;
            updateMetrics();
        }

        // مراقبة دوال الحفظ
        if (typeof saveDaysDataToLocalStorage === 'function') {
            const originalSave = saveDaysDataToLocalStorage;
            saveDaysDataToLocalStorage = function() {
                saveCount++;
                addToMonitor(`💾 تم تنفيذ عملية حفظ رقم ${saveCount}`);
                updateMetrics();
                return originalSave.apply(this, arguments);
            };
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة اختبار الأداء - جاهز للبدء!', 'info');
            addToMonitor('تم تحميل مراقب الأداء');
            updateMetrics();
        });
    </script>
</body>
</html>
