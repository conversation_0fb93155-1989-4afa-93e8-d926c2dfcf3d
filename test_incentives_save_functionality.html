<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ الحوافز والمكافآت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }

        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .button.secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .button.warning {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .button.danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            color: #28a745;
        }

        .result-item.error {
            color: #dc3545;
        }

        .result-item.warning {
            color: #ffc107;
        }

        .result-item.info {
            color: #17a2b8;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .card h4 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        .icon {
            font-size: 1.2em;
            margin-left: 8px;
        }

        .employee-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .data-summary {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💾 اختبار حفظ الحوافز والمكافآت</h1>
            <p>التحقق من عمل دوال الحفظ والاسترجاع بشكل صحيح</p>
        </div>

        <div class="content">
            <div class="status info" id="mainStatus">
                <strong>الحالة:</strong> جاري التحضير للاختبار...
            </div>

            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <button class="button" onclick="createTestEmployee()">
                    <span class="icon">👤</span>
                    إنشاء موظف تجريبي
                </button>
                <button class="button secondary" onclick="testSaveFunctionality()">
                    <span class="icon">💾</span>
                    اختبار دوال الحفظ
                </button>
                <button class="button warning" onclick="testIncentiveWindow()">
                    <span class="icon">🏆</span>
                    اختبار نافذة الحوافز
                </button>
                <button class="button danger" onclick="clearTestData()">
                    <span class="icon">🗑️</span>
                    مسح البيانات التجريبية
                </button>
            </div>

            <div class="grid">
                <div class="card">
                    <h4>📊 نتائج الاختبار</h4>
                    <div class="results" id="testResults">
                        <div class="result-item info">انتظار بدء الاختبار...</div>
                    </div>
                </div>

                <div class="card">
                    <h4>👤 معلومات الموظف التجريبي</h4>
                    <div id="employeeInfo">
                        <div class="result-item info">لم يتم إنشاء موظف تجريبي بعد</div>
                    </div>
                </div>

                <div class="card">
                    <h4>💰 ملخص البيانات</h4>
                    <div id="dataSummary">
                        <div class="result-item info">لا توجد بيانات</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('mainStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const item = document.createElement('div');
            item.className = `result-item ${type}`;
            item.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(item);
            results.scrollTop = results.scrollHeight;
        }

        function updateEmployeeInfo(employee) {
            const employeeInfo = document.getElementById('employeeInfo');
            if (employee) {
                employeeInfo.innerHTML = `
                    <div class="employee-info">
                        <div><strong>الاسم:</strong> ${employee.name}</div>
                        <div><strong>الكود:</strong> ${employee.employeeCode || 'غير محدد'}</div>
                        <div><strong>الوظيفة:</strong> ${employee.position || 'غير محدد'}</div>
                        <div><strong>نوع التوظيف:</strong> ${employee.employmentType === 'monthly' ? 'شهري' : 'يومي'}</div>
                        <div><strong>الراتب الأساسي:</strong> ${employee.basicSalary ? employee.basicSalary.toLocaleString() + ' ج.م' : 'غير محدد'}</div>
                    </div>
                `;
            } else {
                employeeInfo.innerHTML = '<div class="result-item error">لم يتم العثور على الموظف</div>';
            }
        }

        function updateDataSummary() {
            const dataSummary = document.getElementById('dataSummary');
            
            if (typeof incentivesRewardsData !== 'undefined') {
                const testRecords = incentivesRewardsData.filter(record => record.employeeId === 12345);
                const totalIncentives = testRecords.filter(r => r.type === 'incentive').reduce((sum, r) => sum + r.amount, 0);
                const totalRewards = testRecords.filter(r => r.type === 'reward').reduce((sum, r) => sum + r.amount, 0);
                
                dataSummary.innerHTML = `
                    <div class="data-summary">
                        <div><strong>عدد السجلات:</strong> ${testRecords.length}</div>
                        <div><strong>إجمالي الحوافز:</strong> ${totalIncentives.toLocaleString()} ج.م</div>
                        <div><strong>إجمالي المكافآت:</strong> ${totalRewards.toLocaleString()} ج.م</div>
                        <div><strong>الإجمالي العام:</strong> ${(totalIncentives + totalRewards).toLocaleString()} ج.م</div>
                    </div>
                `;
            } else {
                dataSummary.innerHTML = '<div class="result-item warning">متغير incentivesRewardsData غير موجود</div>';
            }
        }

        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('🔧 بدء إنشاء موظف تجريبي', 'info');
            
            try {
                // التحقق من وجود المتغيرات
                if (typeof employees === 'undefined') {
                    addResult('❌ متغير employees غير موجود', 'error');
                    updateStatus('❌ فشل في إنشاء الموظف - متغير employees غير موجود', 'error');
                    return;
                }

                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 12345,
                    name: 'موظف تجريبي - اختبار الحوافز',
                    employeeCode: 'TEST12345',
                    position: 'مطور نظم',
                    employmentType: 'monthly',
                    basicSalary: 7000,
                    createdAt: new Date().toISOString()
                };

                // التحقق من عدم وجود الموظف مسبقاً
                const existingEmployee = employees.find(emp => emp.id === 12345);
                if (existingEmployee) {
                    addResult('⚠️ الموظف التجريبي موجود مسبقاً', 'warning');
                    updateEmployeeInfo(existingEmployee);
                } else {
                    // إضافة الموظف
                    employees.push(testEmployee);
                    addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    updateEmployeeInfo(testEmployee);
                    
                    // حفظ البيانات
                    if (typeof saveEmployeesToLocalStorage === 'function') {
                        saveEmployeesToLocalStorage();
                        addResult('✅ تم حفظ بيانات الموظف في localStorage', 'success');
                    }
                }

                updateStatus('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateDataSummary();

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف التجريبي', 'error');
            }
        }

        function testSaveFunctionality() {
            updateStatus('جاري اختبار دوال الحفظ...', 'warning');
            addResult('💾 بدء اختبار دوال الحفظ والاسترجاع', 'info');
            
            try {
                // التحقق من وجود الدوال المطلوبة
                const requiredFunctions = [
                    'saveIncentivesRewardsToLocalStorage',
                    'loadIncentivesRewardsFromLocalStorage',
                    'saveIncentive',
                    'saveReward',
                    'deleteIncentiveReward'
                ];

                let functionsFound = 0;
                requiredFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                        functionsFound++;
                    } else {
                        addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                    }
                });

                if (functionsFound === requiredFunctions.length) {
                    addResult('✅ جميع الدوال المطلوبة موجودة', 'success');
                    
                    // اختبار إنشاء بيانات تجريبية
                    testCreateIncentiveData();
                    
                } else {
                    addResult(`❌ بعض الدوال مفقودة (${functionsFound}/${requiredFunctions.length})`, 'error');
                    updateStatus('❌ فشل في اختبار الدوال - دوال مفقودة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في اختبار الدوال: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار دوال الحفظ', 'error');
            }
        }

        function testCreateIncentiveData() {
            addResult('🎯 اختبار إنشاء بيانات الحوافز والمكافآت...', 'info');
            
            try {
                // التحقق من وجود المتغير
                if (typeof incentivesRewardsData === 'undefined') {
                    addResult('❌ متغير incentivesRewardsData غير موجود', 'error');
                    return;
                }

                // إنشاء بيانات تجريبية
                const testData = [
                    {
                        id: Date.now() + 1,
                        employeeId: 12345,
                        type: 'incentive',
                        amount: 1000,
                        month: new Date().getMonth() + 1,
                        year: new Date().getFullYear(),
                        description: 'حافز اختبار الحفظ',
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 12345,
                        type: 'reward',
                        amount: 1500,
                        month: new Date().getMonth() + 1,
                        year: new Date().getFullYear(),
                        description: 'مكافأة اختبار الحفظ',
                        createdAt: new Date().toISOString()
                    }
                ];

                // إضافة البيانات
                testData.forEach(record => {
                    incentivesRewardsData.push(record);
                    const typeText = record.type === 'incentive' ? 'حافز' : 'مكافأة';
                    addResult(`✅ تم إضافة ${typeText}: ${record.amount.toLocaleString()} ج.م`, 'success');
                });

                // اختبار الحفظ
                if (typeof saveIncentivesRewardsToLocalStorage === 'function') {
                    saveIncentivesRewardsToLocalStorage();
                    addResult('✅ تم حفظ البيانات في localStorage', 'success');
                    
                    // اختبار الاسترجاع
                    testLoadData();
                } else {
                    addResult('❌ دالة saveIncentivesRewardsToLocalStorage غير موجودة', 'error');
                }

                updateDataSummary();
                updateStatus('✅ تم اختبار دوال الحفظ بنجاح', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء البيانات: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار إنشاء البيانات', 'error');
            }
        }

        function testLoadData() {
            addResult('📥 اختبار استرجاع البيانات...', 'info');
            
            try {
                // حفظ البيانات الحالية
                const originalData = [...incentivesRewardsData];
                
                // مسح البيانات مؤقتاً
                incentivesRewardsData.length = 0;
                addResult('🗑️ تم مسح البيانات مؤقتاً للاختبار', 'info');
                
                // استرجاع البيانات
                if (typeof loadIncentivesRewardsFromLocalStorage === 'function') {
                    loadIncentivesRewardsFromLocalStorage();
                    
                    // التحقق من استرجاع البيانات
                    const testRecords = incentivesRewardsData.filter(record => record.employeeId === 12345);
                    if (testRecords.length > 0) {
                        addResult(`✅ تم استرجاع ${testRecords.length} سجل بنجاح`, 'success');
                    } else {
                        addResult('⚠️ لم يتم استرجاع أي سجلات للموظف التجريبي', 'warning');
                    }
                } else {
                    addResult('❌ دالة loadIncentivesRewardsFromLocalStorage غير موجودة', 'error');
                    // استعادة البيانات الأصلية
                    incentivesRewardsData.push(...originalData);
                }

            } catch (error) {
                addResult(`❌ خطأ في اختبار الاسترجاع: ${error.message}`, 'error');
            }
        }

        function testIncentiveWindow() {
            updateStatus('جاري اختبار نافذة الحوافز...', 'warning');
            addResult('🏆 اختبار فتح نافذة الحوافز والمكافآت', 'info');
            
            try {
                // التحقق من وجود الموظف التجريبي
                const employee = employees.find(emp => emp.id === 12345);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ فشل في فتح النافذة - الموظف غير موجود', 'error');
                    return;
                }

                // اختبار فتح النافذة
                if (typeof viewIncentivesRewards === 'function') {
                    viewIncentivesRewards(12345);
                    addResult('✅ تم فتح نافذة الحوافز والمكافآت بنجاح', 'success');
                    addResult('👀 تحقق من النافذة المفتوحة وجرب إضافة حافز أو مكافأة', 'info');
                    updateStatus('✅ تم فتح نافذة الحوافز بنجاح', 'success');
                } else {
                    addResult('❌ دالة viewIncentivesRewards غير موجودة', 'error');
                    updateStatus('❌ فشل في فتح النافذة - الدالة غير موجودة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح نافذة الحوافز', 'error');
            }
        }

        function clearTestData() {
            updateStatus('جاري مسح البيانات التجريبية...', 'warning');
            addResult('🗑️ بدء مسح البيانات التجريبية', 'info');
            
            try {
                // مسح الموظف التجريبي
                if (typeof employees !== 'undefined') {
                    const originalLength = employees.length;
                    employees = employees.filter(emp => emp.id !== 12345);
                    const removedEmployees = originalLength - employees.length;
                    
                    if (removedEmployees > 0) {
                        addResult(`✅ تم مسح ${removedEmployees} موظف تجريبي`, 'success');
                        
                        // حفظ التغييرات
                        if (typeof saveEmployeesToLocalStorage === 'function') {
                            saveEmployeesToLocalStorage();
                        }
                    }
                }

                // مسح بيانات الحوافز والمكافآت التجريبية
                if (typeof incentivesRewardsData !== 'undefined') {
                    const originalLength = incentivesRewardsData.length;
                    incentivesRewardsData = incentivesRewardsData.filter(record => record.employeeId !== 12345);
                    const removedRecords = originalLength - incentivesRewardsData.length;
                    
                    if (removedRecords > 0) {
                        addResult(`✅ تم مسح ${removedRecords} سجل حوافز ومكافآت`, 'success');
                        
                        // حفظ التغييرات
                        if (typeof saveIncentivesRewardsToLocalStorage === 'function') {
                            saveIncentivesRewardsToLocalStorage();
                        }
                    }
                }

                // مسح localStorage التجريبي
                localStorage.removeItem('oscoIncentivesRewards_test');
                addResult('✅ تم مسح بيانات localStorage التجريبية', 'success');

                updateEmployeeInfo(null);
                updateDataSummary();
                updateStatus('✅ تم مسح جميع البيانات التجريبية', 'success');

            } catch (error) {
                addResult(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
                updateStatus('❌ فشل في مسح البيانات التجريبية', 'error');
            }
        }

        // تشغيل فحص سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof incentivesRewardsData !== 'undefined') {
                    updateStatus('✅ النظام الأساسي محمل بنجاح', 'success');
                    addResult('🚀 النظام جاهز للاختبار', 'success');
                } else {
                    updateStatus('❌ فشل في تحميل النظام الأساسي', 'error');
                    addResult('❌ بعض المتغيرات الأساسية غير موجودة', 'error');
                }
                updateDataSummary();
            }, 1000);
        });
    </script>
</body>
</html>
