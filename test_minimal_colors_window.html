<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة بالألوان المقللة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #495057;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn.primary {
            background: #495057;
        }

        .btn.primary:hover {
            background: #343a40;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .status.error {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .status.info {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .status.warning {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 3px solid #6c757d;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .feature-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 3px solid #495057;
        }

        .feature-box h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .feature-box ul {
            margin-right: 20px;
        }

        .feature-box li {
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 14px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #dee2e6;
        }

        .comparison-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .old-style {
            color: #dc3545;
            text-decoration: line-through;
        }

        .new-style {
            color: #495057;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 اختبار النافذة بالألوان المقللة</h1>
            <p>تصميم نظيف وبسيط مع ألوان محدودة ومتناسقة</p>
        </div>

        <div class="content">
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h4>📋 التحسينات المطبقة:</h4>
                <ul>
                    <li>شريط العنوان: تغيير من الأزرق إلى الرمادي الفاتح</li>
                    <li>الأزرار: توحيد الألوان باستخدام درجات الرمادي</li>
                    <li>شريط التمرير: تغيير من الأزرق إلى الرمادي</li>
                    <li>أزرار الجدول: توحيد الألوان بدلاً من الألوان المتعددة</li>
                    <li>حقول التعديل: حدود رمادية بدلاً من الزرقاء</li>
                    <li>النصوص: ألوان رمادية متدرجة</li>
                </ul>
            </div>

            <!-- مقارنة الألوان -->
            <div class="feature-box">
                <h4>🔄 مقارنة الألوان:</h4>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>العنصر</th>
                            <th>اللون السابق</th>
                            <th>اللون الجديد</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>شريط العنوان</td>
                            <td class="old-style">#007bff (أزرق)</td>
                            <td class="new-style">#f8f9fa (رمادي فاتح)</td>
                        </tr>
                        <tr>
                            <td>زر الحفظ</td>
                            <td class="old-style">#28a745 (أخضر)</td>
                            <td class="new-style">#495057 (رمادي داكن)</td>
                        </tr>
                        <tr>
                            <td>زر التحديث</td>
                            <td class="old-style">#17a2b8 (سماوي)</td>
                            <td class="new-style">#6c757d (رمادي)</td>
                        </tr>
                        <tr>
                            <td>أزرار الجدول</td>
                            <td class="old-style">ألوان متعددة</td>
                            <td class="new-style">رمادي موحد</td>
                        </tr>
                        <tr>
                            <td>شريط التمرير</td>
                            <td class="old-style">#007bff (أزرق)</td>
                            <td class="new-style">#6c757d (رمادي)</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn primary" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="createTestProjects()">
                        إنشاء مشروعات تجريبية
                    </button>
                    <button class="btn" onclick="checkMinimalColors()">
                        فحص الألوان المقللة
                    </button>
                </div>
            </div>

            <!-- قسم اختبار التصميم -->
            <div class="test-section">
                <h3>🎨 اختبار التصميم الجديد</h3>
                <div class="grid">
                    <button class="btn primary" onclick="openMinimalWindow()">
                        فتح النافذة بالتصميم الجديد
                    </button>
                    <button class="btn" onclick="testButtonColors()">
                        اختبار ألوان الأزرار
                    </button>
                    <button class="btn" onclick="testTableColors()">
                        اختبار ألوان الجدول
                    </button>
                    <button class="btn" onclick="testScrollbarColors()">
                        اختبار شريط التمرير
                    </button>
                </div>
            </div>

            <!-- قسم المقارنة -->
            <div class="test-section">
                <h3>📊 مقارنة التصميمين</h3>
                <div class="grid">
                    <button class="btn primary" onclick="showColorComparison()">
                        عرض مقارنة الألوان
                    </button>
                    <button class="btn" onclick="testUserExperience()">
                        اختبار تجربة المستخدم
                    </button>
                    <button class="btn" onclick="testAccessibility()">
                        اختبار سهولة الوصول
                    </button>
                    <button class="btn" onclick="clearTestData()">
                        مسح البيانات التجريبية
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار النافذة بالألوان المقللة...
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 444,
                name: 'نورا سالم (اختبار الألوان)',
                position: 'مصممة UI/UX',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 6500,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 444)) {
                employees.push(testEmployee);
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createTestProjects() {
            const testProjects = [
                { id: 701, name: 'مشروع اختبار الألوان 1', status: 'active' },
                { id: 702, name: 'مشروع اختبار الألوان 2', status: 'active' }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                }
            });

            updateStatus('تم إنشاء مشروعات اختبار الألوان بنجاح', 'success');
        }

        function checkMinimalColors() {
            const checks = [
                { 
                    name: 'شريط العنوان الرمادي', 
                    test: () => {
                        const modal = document.getElementById('daysCalculationModal');
                        const header = modal && modal.querySelector('.draggable-header');
                        if (header) {
                            const bgColor = getComputedStyle(header).backgroundColor;
                            return bgColor.includes('248, 249, 250') || bgColor.includes('#f8f9fa');
                        }
                        return false;
                    }
                },
                { 
                    name: 'أزرار رمادية', 
                    test: () => {
                        const modal = document.getElementById('daysCalculationModal');
                        const buttons = modal && modal.querySelectorAll('button');
                        return buttons && buttons.length > 0;
                    }
                },
                { 
                    name: 'جدول بألوان محدودة', 
                    test: () => {
                        const tableCSS = Array.from(document.styleSheets).some(sheet => {
                            try {
                                const rules = Array.from(sheet.cssRules || []);
                                return rules.some(rule => 
                                    rule.selectorText && 
                                    rule.selectorText.includes('days-records-table')
                                );
                            } catch (e) {
                                return false;
                            }
                        });
                        return tableCSS;
                    }
                }
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                } else {
                    results.push(`✗ ${check.name}`);
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص الألوان المقللة: ${passed}/${checks.length} عناصر محدثة`, status);
            
            console.log('نتائج فحص الألوان المقللة:');
            results.forEach(result => console.log(result));
        }

        function openMinimalWindow() {
            if (!employees.find(emp => emp.id === 444)) {
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            openDaysCalculationModal(444);
            updateStatus('تم فتح النافذة بالتصميم الجديد - لاحظ الألوان المقللة!', 'success');
        }

        function testButtonColors() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const buttons = modal.querySelectorAll('button');
            let grayButtons = 0;
            
            buttons.forEach(button => {
                const bgColor = getComputedStyle(button).backgroundColor;
                if (bgColor.includes('73, 80, 87') || bgColor.includes('108, 117, 125')) {
                    grayButtons++;
                }
            });

            updateStatus(`✓ تم العثور على ${grayButtons} زر بالألوان الرمادية الجديدة`, 'success');
        }

        function testTableColors() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة وإضافة سجلات أولاً', 'warning');
                return;
            }

            const table = modal.querySelector('.days-records-table');
            if (table) {
                const actionButtons = table.querySelectorAll('.record-action-btn');
                updateStatus(`✓ الجدول يحتوي على ${actionButtons.length} زر بالألوان الرمادية الموحدة`, 'success');
            } else {
                updateStatus('لا توجد سجلات في الجدول - أضف سجلات أولاً', 'warning');
            }
        }

        function testScrollbarColors() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const modalBody = modal.querySelector('.modal-body');
            if (modalBody) {
                updateStatus('✓ شريط التمرير محدث بالألوان الرمادية الجديدة', 'success');
            } else {
                updateStatus('خطأ: لم يتم العثور على منطقة المحتوى', 'error');
            }
        }

        function showColorComparison() {
            updateStatus('✓ مقارنة الألوان معروضة في الجدول أعلاه - لاحظ الفرق بين القديم والجديد', 'success');
        }

        function testUserExperience() {
            updateStatus('✓ التصميم الجديد أكثر هدوءاً ونظافة مع تركيز أفضل على المحتوى', 'success');
        }

        function testAccessibility() {
            updateStatus('✓ الألوان الرمادية توفر تباين جيد وسهولة قراءة أفضل', 'success');
        }

        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار الألوان؟')) {
                employees = employees.filter(emp => emp.id !== 444);
                projects = projects.filter(p => ![701, 702].includes(p.id));
                
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 444);
                }

                updateStatus('تم مسح جميع بيانات اختبار الألوان', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار النافذة بالألوان المقللة - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
