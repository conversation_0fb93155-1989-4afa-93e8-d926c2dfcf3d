<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تعديل سجلات الأيام</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #results {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .demo-table th, .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .selected-row {
            background-color: #e3f2fd !important;
            border: 2px solid #2196F3 !important;
        }
        .modified-row {
            background-color: #fff3cd !important;
            border-left: 4px solid #FF9800 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-cogs"></i> اختبار تعديل سجلات الأيام</h1>
        <p>هذه الصفحة لاختبار وظائف تعديل سجلات الأيام في التطبيق</p>

        <!-- قسم اختبار الدوال -->
        <div class="test-section">
            <h2>1. اختبار وجود الدوال</h2>
            <button class="test-button" onclick="testFunctionsExistence()">
                <i class="fas fa-search"></i> فحص وجود الدوال
            </button>
            <div id="functions-status" class="status info">في انتظار الاختبار</div>
        </div>

        <!-- قسم اختبار البيانات -->
        <div class="test-section">
            <h2>2. اختبار البيانات</h2>
            <button class="test-button" onclick="testDataAvailability()">
                <i class="fas fa-database"></i> فحص البيانات
            </button>
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            <div id="data-status" class="status info">في انتظار الاختبار</div>
        </div>

        <!-- قسم اختبار التعديل -->
        <div class="test-section">
            <h2>3. اختبار التعديل المباشر</h2>
            <button class="test-button" onclick="testEditFunctionality()">
                <i class="fas fa-edit"></i> اختبار التعديل
            </button>
            <button class="test-button" onclick="simulateEditWorkflow()">
                <i class="fas fa-play"></i> محاكاة سير العمل
            </button>
            <div id="edit-status" class="status info">في انتظار الاختبار</div>
            
            <!-- جدول تجريبي -->
            <div id="demo-table-container"></div>
        </div>

        <!-- قسم النتائج -->
        <div class="test-section">
            <h2>4. سجل النتائج</h2>
            <button class="test-button" onclick="clearAllTests()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
            <button class="test-button" onclick="openOriginalPage()">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة الأصلية
            </button>
            <div id="results">
                <h3>📋 سجل نتائج الاختبارات:</h3>
                <p>جاهز لبدء الاختبارات...</p>
            </div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            const timestamp = new Date().toLocaleTimeString();
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            p.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testFunctionsExistence() {
            addResult('🔍 بدء اختبار وجود الدوال...');
            
            const requiredFunctions = [
                'toggleEditMode',
                'updateRecordField',
                'saveAllTableChanges',
                'deleteDaysRecord',
                'setCurrentEmployeeData',
                'applyFilters',
                'clearFilters',
                'rebuildFilteredTable'
            ];
            
            let functionsFound = 0;
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            const percentage = (functionsFound / requiredFunctions.length) * 100;
            addResult(`📊 النتيجة: ${functionsFound}/${requiredFunctions.length} دالة موجودة (${percentage.toFixed(1)}%)`, 'info');
            
            const success = functionsFound === requiredFunctions.length;
            updateStatus('functions-status', success, success ? 'جميع الدوال موجودة' : `${functionsFound}/${requiredFunctions.length} دالة موجودة`);
        }

        function testDataAvailability() {
            addResult('🔍 بدء اختبار توفر البيانات...');
            
            let dataScore = 0;
            let totalChecks = 0;
            
            // فحص المتغيرات العامة
            totalChecks++;
            if (typeof tableChanges !== 'undefined') {
                addResult('✅ متغير tableChanges موجود', 'success');
                dataScore++;
            } else {
                addResult('❌ متغير tableChanges غير موجود', 'error');
            }
            
            totalChecks++;
            if (typeof currentEmployeeData !== 'undefined') {
                addResult('✅ متغير currentEmployeeData موجود', 'success');
                dataScore++;
            } else {
                addResult('❌ متغير currentEmployeeData غير موجود', 'error');
            }
            
            // فحص localStorage
            totalChecks++;
            try {
                const daysData = localStorage.getItem('oscoEmployeeDaysData');
                if (daysData) {
                    const parsed = JSON.parse(daysData);
                    addResult(`✅ بيانات الأيام في localStorage: ${parsed.length} سجل`, 'success');
                    dataScore++;
                } else {
                    addResult('⚠️ لا توجد بيانات أيام في localStorage', 'warning');
                }
            } catch (error) {
                addResult('❌ خطأ في قراءة localStorage: ' + error.message, 'error');
            }
            
            const success = dataScore >= 2;
            updateStatus('data-status', success, success ? 'البيانات متاحة' : 'مشاكل في البيانات');
        }

        function createTestData() {
            addResult('🔧 إنشاء بيانات تجريبية...');
            
            // إنشاء بيانات تجريبية
            const testData = [
                {
                    id: 1001,
                    employeeId: 1,
                    employeeName: 'أحمد محمد',
                    projectName: 'مشروع تجريبي 1',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 30,
                    absenceDays: 2,
                    overtimeHours: 10,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 1002,
                    employeeId: 1,
                    employeeName: 'أحمد محمد',
                    projectName: 'مشروع تجريبي 2',
                    startDate: '2024-02-01',
                    endDate: '2024-02-28',
                    calculatedDays: 28,
                    absenceDays: 1,
                    overtimeHours: 5,
                    createdAt: new Date().toISOString()
                }
            ];
            
            // حفظ في localStorage
            localStorage.setItem('oscoEmployeeDaysData', JSON.stringify(testData));
            addResult('✅ تم إنشاء وحفظ البيانات التجريبية', 'success');
            
            // إنشاء جدول تجريبي
            createDemoTable(testData);
        }

        function createDemoTable(data) {
            const container = document.getElementById('demo-table-container');
            container.innerHTML = `
                <h4>جدول تجريبي للتعديل:</h4>
                <table class="demo-table" id="demoTable">
                    <thead>
                        <tr>
                            <th>المشروع</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>الأيام</th>
                            <th>الغياب</th>
                            <th>الساعات الإضافية</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map((record, index) => `
                            <tr id="demo-record-${record.id}" style="background-color: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                                <td>${record.projectName}</td>
                                <td>
                                    <input type="date" value="${record.startDate}" 
                                           onchange="testUpdateField(${record.id}, 'startDate', this.value)"
                                           style="border: none; background: transparent; text-align: center;">
                                </td>
                                <td>
                                    <input type="date" value="${record.endDate}"
                                           onchange="testUpdateField(${record.id}, 'endDate', this.value)"
                                           style="border: none; background: transparent; text-align: center;">
                                </td>
                                <td>
                                    <input type="number" value="${record.calculatedDays}" min="0"
                                           onchange="testUpdateField(${record.id}, 'calculatedDays', this.value)"
                                           style="width: 60px; border: none; background: transparent; text-align: center;">
                                </td>
                                <td>
                                    <input type="number" value="${record.absenceDays}" min="0"
                                           onchange="testUpdateField(${record.id}, 'absenceDays', this.value)"
                                           style="width: 50px; border: none; background: transparent; text-align: center;">
                                </td>
                                <td>
                                    <input type="number" value="${record.overtimeHours}" min="0"
                                           onchange="testUpdateField(${record.id}, 'overtimeHours', this.value)"
                                           style="width: 60px; border: none; background: transparent; text-align: center;">
                                </td>
                                <td>
                                    <button onclick="testToggleEdit(${record.id})" style="padding: 4px 8px; background: #FF9800; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 11px;">
                                        تعديل
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                <div style="margin-top: 10px;">
                    <button id="demoSaveBtn" onclick="testSaveChanges()" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            `;
        }

        // دوال اختبار محلية
        let testTableChanges = {};

        function testToggleEdit(recordId) {
            addResult(`🎯 اختبار تبديل وضع التعديل للسجل ${recordId}`, 'info');
            
            // إزالة التحديد من جميع الصفوف
            document.querySelectorAll('.demo-table tbody tr').forEach(row => {
                row.classList.remove('selected-row');
            });
            
            // تحديد الصف الحالي
            const row = document.getElementById(`demo-record-${recordId}`);
            if (row) {
                row.classList.add('selected-row');
                addResult(`✅ تم تحديد السجل ${recordId} للتعديل`, 'success');
            }
        }

        function testUpdateField(recordId, fieldName, newValue) {
            addResult(`📝 تحديث ${fieldName} للسجل ${recordId}: ${newValue}`, 'info');
            
            if (!testTableChanges[recordId]) {
                testTableChanges[recordId] = {};
            }
            testTableChanges[recordId][fieldName] = newValue;
            
            // تمييز الصف
            const row = document.getElementById(`demo-record-${recordId}`);
            if (row) {
                row.classList.add('modified-row');
            }
            
            // تحديث زر الحفظ
            const saveBtn = document.getElementById('demoSaveBtn');
            if (saveBtn) {
                const changesCount = Object.keys(testTableChanges).length;
                saveBtn.innerHTML = `<i class="fas fa-save"></i> حفظ التغييرات (${changesCount})`;
                saveBtn.style.backgroundColor = '#FF9800';
            }
        }

        function testSaveChanges() {
            const changesCount = Object.keys(testTableChanges).length;
            addResult(`💾 حفظ ${changesCount} تغيير`, 'success');
            
            // مسح التغييرات
            testTableChanges = {};
            
            // إعادة تعيين الجدول
            document.querySelectorAll('.demo-table tbody tr').forEach((row, index) => {
                row.classList.remove('modified-row', 'selected-row');
                row.style.backgroundColor = index % 2 === 0 ? '#f9f9f9' : 'white';
            });
            
            // إعادة تعيين زر الحفظ
            const saveBtn = document.getElementById('demoSaveBtn');
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
                saveBtn.style.backgroundColor = '#4CAF50';
            }
            
            alert('تم حفظ جميع التغييرات بنجاح!');
        }

        function testEditFunctionality() {
            addResult('🔧 اختبار وظائف التعديل...');
            
            // اختبار الدوال الأساسية
            if (typeof toggleEditMode === 'function') {
                addResult('✅ دالة toggleEditMode متاحة', 'success');
            } else {
                addResult('❌ دالة toggleEditMode غير متاحة', 'error');
            }
            
            if (typeof updateRecordField === 'function') {
                addResult('✅ دالة updateRecordField متاحة', 'success');
            } else {
                addResult('❌ دالة updateRecordField غير متاحة', 'error');
            }
            
            updateStatus('edit-status', true, 'تم اختبار وظائف التعديل');
        }

        function simulateEditWorkflow() {
            addResult('🔄 محاكاة سير عمل التعديل...');
            
            addResult('1️⃣ فتح نافذة سجلات الأيام', 'info');
            addResult('2️⃣ النقر على زر "تعديل" لتحديد السجل', 'info');
            addResult('3️⃣ تعديل القيم مباشرة في الجدول', 'info');
            addResult('4️⃣ تمييز الصفوف المعدلة بلون مختلف', 'info');
            addResult('5️⃣ تحديث زر "حفظ التغييرات"', 'info');
            addResult('6️⃣ حفظ جميع التغييرات', 'info');
            
            addResult('✅ تم محاكاة سير العمل بنجاح', 'success');
        }

        function clearAllTests() {
            document.getElementById('results').innerHTML = '<h3>📋 سجل نتائج الاختبارات:</h3><p>تم مسح جميع النتائج. جاهز لبدء اختبارات جديدة...</p>';
            
            // إعادة تعيين حالة جميع الاختبارات
            const statusElements = ['functions-status', 'data-status', 'edit-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'في انتظار الاختبار';
                }
            });
            
            // مسح الجدول التجريبي
            document.getElementById('demo-table-container').innerHTML = '';
            testTableChanges = {};
        }

        function openOriginalPage() {
            window.open('index.html', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار تعديل سجلات الأيام');
            addResult('📋 جاهز لبدء اختبار الميزات');
        });
    </script>
</body>
</html>
