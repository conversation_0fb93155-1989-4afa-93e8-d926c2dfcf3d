<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة المبسطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .button.primary { background: #007bff; }
        .button.primary:hover { background: #0056b3; }
        
        .button.warning { background: #ffc107; color: #212529; }
        .button.warning:hover { background: #e0a800; }
        
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .employee-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            min-width: 180px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .employee-card.active {
            border-color: #28a745;
            background: #f8fff8;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }
        
        .employee-card h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        
        .employee-card p {
            margin: 4px 0;
            color: #666;
            font-size: 13px;
        }
        
        .activate-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }
        
        .activate-btn.active {
            background: #dc3545;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✨ اختبار النافذة المبسطة لتطبيق التاريخ</h1>
            <p>نافذة مبسطة بدون قائمة الموظفين - فقط الشهر والسنة ونوع العملية</p>
        </div>

        <div class="test-section">
            <h4>🔧 إعداد البيانات التجريبية:</h4>
            <button class="button" onclick="createTestData()">
                <i class="fas fa-plus"></i>
                إنشاء بيانات تجريبية
            </button>
            <button class="button warning" onclick="clearTestData()">
                <i class="fas fa-trash"></i>
                مسح البيانات
            </button>
        </div>

        <div class="test-section">
            <h4>👥 الموظفين التجريبيين:</h4>
            <div id="testEmployees">
                <!-- سيتم ملؤها ديناميكياً -->
            </div>
        </div>

        <div class="test-section">
            <h4>📅 اختبار النافذة المبسطة:</h4>
            <button class="button primary" onclick="testSimpleDateModal()">
                <i class="fas fa-calendar-check"></i>
                فتح النافذة المبسطة
            </button>
            <button class="button" onclick="testDateApplication()">
                <i class="fas fa-play"></i>
                اختبار التطبيق الكامل
            </button>
            <button class="button danger" onclick="testCancelApplication()">
                <i class="fas fa-calendar-times"></i>
                اختبار إلغاء التطبيق
            </button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div class="info-box" id="activeInfo">
            <strong>الموظفين المنشطين:</strong> <span id="activeCount">0</span>
        </div>

        <div class="console" id="console">
            [SIMPLE_DATE_MODAL_TEST] نظام اختبار النافذة المبسطة جاهز...<br>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function updateActiveCount() {
            const activeCountSpan = document.getElementById('activeCount');
            if (activeCountSpan && typeof activeEmployees !== 'undefined') {
                activeCountSpan.textContent = activeEmployees.size;
            }
        }

        function createTestData() {
            addLog('إنشاء بيانات تجريبية...');
            
            try {
                // إنشاء موظفين تجريبيين
                const testEmployees = [
                    {
                        id: 2001,
                        name: 'سارة أحمد محمد',
                        position: 'مطورة واجهات',
                        employeeCode: 'UI001',
                        employmentType: 'monthly',
                        basicSalary: 4800
                    },
                    {
                        id: 2002,
                        name: 'خالد عبدالرحمن',
                        position: 'مطور خلفية',
                        employeeCode: 'BE001',
                        employmentType: 'monthly',
                        basicSalary: 5200
                    },
                    {
                        id: 2003,
                        name: 'منى سالم الدين',
                        position: 'محللة أنظمة',
                        employeeCode: 'SA001',
                        employmentType: 'monthly',
                        basicSalary: 4600
                    }
                ];
                
                // إضافة الموظفين إلى القائمة الرئيسية
                testEmployees.forEach(emp => {
                    if (!employees.find(e => e.id === emp.id)) {
                        employees.push(emp);
                    }
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                
                // عرض الموظفين في الصفحة
                displayTestEmployees();
                
                addLog(`✓ تم إنشاء ${testEmployees.length} موظف تجريبي`);
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية', 'error');
            }
        }

        function displayTestEmployees() {
            const container = document.getElementById('testEmployees');
            const testEmployees = employees.filter(emp => emp.id >= 2001 && emp.id <= 2003);
            
            if (testEmployees.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد بيانات تجريبية - انقر على "إنشاء بيانات تجريبية"</p>';
                return;
            }
            
            container.innerHTML = testEmployees.map(emp => {
                const isActive = activeEmployees.has(emp.id);
                return `
                    <div class="employee-card ${isActive ? 'active' : ''}" id="emp-card-${emp.id}">
                        <h4>${emp.name}</h4>
                        <p><strong>الوظيفة:</strong> ${emp.position}</p>
                        <p><strong>الكود:</strong> ${emp.employeeCode}</p>
                        <button class="activate-btn ${isActive ? 'active' : ''}" onclick="toggleEmployeeActivation(${emp.id})">
                            ${isActive ? 'إلغاء التنشيط' : 'تنشيط'}
                        </button>
                    </div>
                `;
            }).join('');
            
            updateActiveCount();
        }

        function toggleEmployeeActivation(employeeId) {
            addLog(`تبديل حالة تنشيط الموظف ${employeeId}...`);
            
            if (activeEmployees.has(employeeId)) {
                activeEmployees.delete(employeeId);
                addLog(`✓ تم إلغاء تنشيط الموظف ${employeeId}`);
            } else {
                activeEmployees.add(employeeId);
                addLog(`✓ تم تنشيط الموظف ${employeeId}`);
            }
            
            // حفظ حالة التنشيط
            saveActiveEmployeesToLocalStorage();
            
            // تحديث العرض
            displayTestEmployees();
            updateActiveCount();
        }

        function clearTestData() {
            addLog('مسح البيانات التجريبية...');
            
            try {
                // إزالة الموظفين التجريبيين
                employees = employees.filter(emp => emp.id < 2001 || emp.id > 2003);
                
                // إزالة من قائمة المنشطين
                [2001, 2002, 2003].forEach(id => {
                    activeEmployees.delete(id);
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveActiveEmployeesToLocalStorage();
                
                // تحديث العرض
                displayTestEmployees();
                updateActiveCount();
                
                addLog('✓ تم مسح البيانات التجريبية');
                updateStatus('تم مسح البيانات التجريبية', 'warning');
                
            } catch (error) {
                addLog('✗ خطأ في مسح البيانات: ' + error.message);
                updateStatus('خطأ في مسح البيانات', 'error');
            }
        }

        function testSimpleDateModal() {
            addLog('اختبار فتح النافذة المبسطة...');
            
            try {
                if (typeof openDateApplicationModal === 'function') {
                    openDateApplicationModal();
                    addLog('✓ تم استدعاء دالة فتح النافذة');
                    updateStatus('تم فتح النافذة المبسطة!', 'success');
                } else {
                    addLog('✗ دالة openDateApplicationModal غير موجودة');
                    updateStatus('خطأ: دالة فتح النافذة غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function testDateApplication() {
            addLog('اختبار التطبيق الكامل...');
            
            const activeCount = activeEmployees.size;
            if (activeCount === 0) {
                addLog('⚠️ لا يوجد موظفين منشطين');
                updateStatus('يرجى تنشيط موظف واحد على الأقل أولاً', 'warning');
                return;
            }
            
            try {
                // فتح النافذة
                openDateApplicationModal();
                addLog('✓ تم فتح النافذة');
                
                // محاكاة التطبيق بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('dateApplicationModal');
                    if (modal && modal.style.display === 'block') {
                        addLog('✓ النافذة مفتوحة ومرئية');
                        addLog(`✓ جاهز لتطبيق التاريخ على ${activeCount} موظف`);
                        updateStatus(`✅ النافذة جاهزة للتطبيق على ${activeCount} موظف!`, 'success');
                    } else {
                        addLog('❌ النافذة غير مرئية');
                        updateStatus('❌ مشكلة في عرض النافذة', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                addLog('✗ خطأ في اختبار التطبيق: ' + error.message);
                updateStatus('خطأ في اختبار التطبيق: ' + error.message, 'error');
            }
        }

        function testCancelApplication() {
            addLog('اختبار إلغاء التطبيق...');
            
            try {
                if (typeof openCancelDateApplicationModal === 'function') {
                    openCancelDateApplicationModal();
                    addLog('✓ تم استدعاء دالة إلغاء التطبيق');
                    updateStatus('تم اختبار إلغاء التطبيق!', 'success');
                } else {
                    addLog('✗ دالة openCancelDateApplicationModal غير موجودة');
                    updateStatus('خطأ: دالة إلغاء التطبيق غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في اختبار الإلغاء: ' + error.message);
                updateStatus('خطأ في اختبار الإلغاء: ' + error.message, 'error');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة اختبار النافذة المبسطة');
            
            setTimeout(() => {
                if (typeof employees !== 'undefined') {
                    addLog('✅ البرنامج الأساسي محمل');
                    displayTestEmployees();
                    updateActiveCount();
                    updateStatus('جاهز لبدء الاختبار', 'success');
                } else {
                    addLog('❌ البرنامج الأساسي غير محمل');
                    updateStatus('❌ البرنامج الأساسي غير محمل', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
