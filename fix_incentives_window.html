<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح نافذة الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .results {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        .status {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #27ae60;
        }

        .status.error {
            background: #e74c3c;
        }

        .status.warning {
            background: #f39c12;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> إصلاح نافذة الحوافز والمكافآت</h1>
            <p>تشخيص شامل وإصلاح مشكلة عدم فتح النافذة</p>
        </div>

        <div class="status" id="status">
            جاهز للتشخيص والإصلاح...
        </div>

        <div class="content">
            <div class="grid">
                <button class="test-button" onclick="step1_diagnose()">
                    <i class="fas fa-search"></i> 1. تشخيص المشكلة
                </button>
                
                <button class="test-button warning" onclick="step2_testSimple()">
                    <i class="fas fa-vial"></i> 2. اختبار مبسط
                </button>
                
                <button class="test-button success" onclick="step3_forceOpen()">
                    <i class="fas fa-hammer"></i> 3. فتح بالقوة
                </button>
                
                <button class="test-button danger" onclick="step4_recreate()">
                    <i class="fas fa-redo"></i> 4. إعادة إنشاء
                </button>
            </div>

            <div class="grid">
                <button class="test-button" onclick="testConsole()">
                    <i class="fas fa-terminal"></i> اختبار Console
                </button>
                
                <button class="test-button" onclick="createEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف
                </button>
                
                <button class="test-button" onclick="clearAll()">
                    <i class="fas fa-broom"></i> مسح الكل
                </button>
                
                <button class="test-button" onclick="showHelp()">
                    <i class="fas fa-question"></i> المساعدة
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, color = '#ecf0f1') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="color: ${color}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // الخطوة 1: تشخيص المشكلة
        function step1_diagnose() {
            updateStatus('جاري التشخيص...', 'warning');
            addResult('🔍 بدء التشخيص الشامل', '#3498db');
            
            // فحص تحميل النظام
            if (typeof employees === 'undefined') {
                addResult('❌ النظام غير محمل - متغير employees مفقود', '#e74c3c');
                updateStatus('❌ النظام غير محمل', 'error');
                return;
            }
            
            addResult(`✅ النظام محمل - ${employees.length} موظف`, '#27ae60');
            
            // فحص الدوال
            const functions = ['viewIncentivesRewards', 'getMonthName', 'closeIncentivesRewardsModal'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    addResult(`✅ دالة ${func}: موجودة`, '#27ae60');
                } else {
                    addResult(`❌ دالة ${func}: مفقودة`, '#e74c3c');
                }
            });
            
            // فحص المتغيرات
            if (typeof incentivesRewardsData !== 'undefined') {
                addResult(`✅ متغير incentivesRewardsData: موجود (${incentivesRewardsData.length} سجل)`, '#27ae60');
            } else {
                addResult('❌ متغير incentivesRewardsData: مفقود', '#e74c3c');
            }
            
            updateStatus('✅ انتهى التشخيص', 'success');
        }

        // الخطوة 2: اختبار مبسط
        function step2_testSimple() {
            updateStatus('جاري الاختبار المبسط...', 'warning');
            addResult('🧪 بدء الاختبار المبسط', '#f39c12');
            
            try {
                if (typeof openSimpleIncentivesModal === 'function') {
                    openSimpleIncentivesModal(1);
                    addResult('✅ تم فتح النافذة المبسطة', '#27ae60');
                    updateStatus('✅ النافذة المبسطة تعمل', 'success');
                } else {
                    addResult('❌ دالة النافذة المبسطة غير موجودة', '#e74c3c');
                    updateStatus('❌ النافذة المبسطة لا تعمل', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في النافذة المبسطة: ${error.message}`, '#e74c3c');
                updateStatus('❌ فشل الاختبار المبسط', 'error');
            }
        }

        // الخطوة 3: فتح بالقوة
        function step3_forceOpen() {
            updateStatus('جاري الفتح بالقوة...', 'warning');
            addResult('💪 محاولة فتح النافذة بالقوة', '#e67e22');
            
            try {
                // إنشاء موظف تجريبي إذا لم يكن موجوداً
                if (employees.length === 0) {
                    createEmployee();
                }
                
                const employeeId = employees[0].id;
                addResult(`🎯 استهداف الموظف: ${employees[0].name} (ID: ${employeeId})`, '#3498db');
                
                // محاولة فتح النافذة
                if (typeof viewIncentivesRewards === 'function') {
                    addResult('🔄 استدعاء viewIncentivesRewards...', '#f39c12');
                    viewIncentivesRewards(employeeId);
                    
                    // فحص النافذة بعد فترة
                    setTimeout(() => {
                        const modal = document.getElementById('incentivesRewardsModal');
                        if (modal) {
                            addResult('🎉 النافذة مفتوحة ومرئية!', '#27ae60');
                            updateStatus('✅ نجح الفتح بالقوة!', 'success');
                        } else {
                            addResult('❌ النافذة لم تظهر حتى بالقوة', '#e74c3c');
                            updateStatus('❌ فشل الفتح بالقوة', 'error');
                        }
                    }, 1000);
                } else {
                    addResult('❌ دالة viewIncentivesRewards غير موجودة', '#e74c3c');
                    updateStatus('❌ الدالة مفقودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في الفتح بالقوة: ${error.message}`, '#e74c3c');
                updateStatus('❌ فشل الفتح بالقوة', 'error');
            }
        }

        // الخطوة 4: إعادة إنشاء النافذة
        function step4_recreate() {
            updateStatus('جاري إعادة الإنشاء...', 'warning');
            addResult('🔄 إعادة إنشاء النافذة من الصفر', '#9b59b6');
            
            try {
                // إزالة أي نافذة موجودة
                const existing = document.getElementById('incentivesRewardsModal');
                if (existing) {
                    existing.remove();
                    addResult('🗑️ تم إزالة النافذة الموجودة', '#f39c12');
                }
                
                // إنشاء نافذة جديدة بسيطة
                const modal = document.createElement('div');
                modal.id = 'incentivesRewardsModal';
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    z-index: 99999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;
                
                modal.innerHTML = `
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        max-width: 600px;
                        width: 90%;
                        text-align: center;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    ">
                        <h2 style="color: #27ae60; margin-bottom: 20px; font-size: 24px;">
                            🏆 نافذة الحوافز والمكافآت
                        </h2>
                        <p style="margin-bottom: 30px; color: #7f8c8d; font-size: 16px;">
                            تم إنشاء النافذة بنجاح! النظام يعمل الآن.
                        </p>
                        <button onclick="closeIncentivesRewardsModal()" style="
                            background: #e74c3c;
                            color: white;
                            border: none;
                            padding: 15px 30px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#c0392b'" onmouseout="this.style.background='#e74c3c'">
                            إغلاق النافذة
                        </button>
                    </div>
                `;
                
                document.body.appendChild(modal);
                addResult('✅ تم إنشاء النافذة الجديدة بنجاح', '#27ae60');
                updateStatus('✅ تم إعادة الإنشاء بنجاح!', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في إعادة الإنشاء: ${error.message}`, '#e74c3c');
                updateStatus('❌ فشل إعادة الإنشاء', 'error');
            }
        }

        // اختبار Console
        function testConsole() {
            addResult('🖥️ اختبار Console...', '#9b59b6');
            
            if (typeof testIncentivesRewardsSystem === 'function') {
                testIncentivesRewardsSystem();
                addResult('✅ تم تشغيل اختبار Console', '#27ae60');
            } else {
                addResult('❌ دالة اختبار Console غير موجودة', '#e74c3c');
            }
        }

        // إنشاء موظف تجريبي
        function createEmployee() {
            addResult('👤 إنشاء موظف تجريبي...', '#3498db');
            
            const testEmployee = {
                id: 77777,
                name: "موظف تجريبي للحوافز",
                position: "مختبر النظام",
                employeeCode: "FIX_TEST",
                nationalId: "12345678901234",
                phone: "01000000000",
                photo: "https://via.placeholder.com/150/27ae60/ffffff?text=FIX",
                employmentType: "monthly",
                salary: { basic: 5000, total: 5000 },
                dateAdded: new Date().toISOString()
            };
            
            const existing = employees.find(emp => emp.id === 77777);
            if (!existing) {
                employees.push(testEmployee);
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                addResult('✅ تم إنشاء الموظف التجريبي', '#27ae60');
            } else {
                addResult('⚠️ الموظف التجريبي موجود بالفعل', '#f39c12');
            }
        }

        // مسح الكل
        function clearAll() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح جميع النتائج', 'info');
        }

        // المساعدة
        function showHelp() {
            addResult('📖 دليل الاستخدام:', '#3498db');
            addResult('1. ابدأ بالتشخيص لفحص النظام', '#ecf0f1');
            addResult('2. جرب الاختبار المبسط', '#ecf0f1');
            addResult('3. استخدم الفتح بالقوة إذا فشل الاختبار', '#ecf0f1');
            addResult('4. أعد الإنشاء كحل أخير', '#ecf0f1');
            addResult('5. تحقق من Console للمزيد من التفاصيل', '#ecf0f1');
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 بدء الفحص التلقائي...', '#3498db');
                step1_diagnose();
            }, 1000);
        });
    </script>
</body>
</html>
