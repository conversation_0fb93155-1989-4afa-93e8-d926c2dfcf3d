<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - فلتر المشروع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #007bff; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-bug"></i> اختبار بسيط - فلتر المشروع</h1>
        <p>اختبار مبسط للتحقق من ظهور فلتر المشروع في نافذة أيام الحضور</p>
        
        <div>
            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
            </button>
            
            <button class="test-button" onclick="openRecordsWindow()">
                <i class="fas fa-calendar-alt"></i> فتح نافذة السجلات
            </button>
            
            <button class="test-button" onclick="checkFilter()">
                <i class="fas fa-search"></i> فحص الفلتر
            </button>
            
            <button class="test-button" onclick="clearLog()">
                <i class="fas fa-trash"></i> مسح السجل
            </button>
        </div>

        <div class="log" id="log">
            <div class="log-entry log-info">[تحميل] جاهز للاختبار...</div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function createTestEmployee() {
            log('➕ إنشاء موظف تجريبي...', 'info');
            
            try {
                const testEmployee = {
                    id: 99999,
                    name: 'موظف اختبار الفلتر',
                    position: 'مطور',
                    employeeCode: 'FILTER99999',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 99999);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء سجلات تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 99999,
                        projectName: 'مشروع الاختبار الأول',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 22,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 99999,
                        projectName: 'مشروع الاختبار الثاني',
                        startDate: '2024-02-01',
                        endDate: '2024-02-29',
                        calculatedDays: 20,
                        absenceDays: 1,
                        overtimeHours: 5,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة للموظف
                employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 99999);
                
                // إضافة السجلات الجديدة
                testRecords.forEach(record => {
                    employeeDaysData.push(record);
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();
                
                log(`✅ تم إنشاء الموظف: ${testEmployee.name}`, 'success');
                log(`✅ تم إنشاء ${testRecords.length} سجل لمشروعين مختلفين`, 'success');
                
            } catch (error) {
                log('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            log('📋 فتح نافذة السجلات...', 'info');
            
            try {
                if (!employees.find(emp => emp.id === 99999)) {
                    log('⚠️ لا يوجد موظف تجريبي، سيتم إنشاؤه...', 'warning');
                    createTestEmployee();
                }
                
                if (typeof viewDaysRecords === 'function') {
                    viewDaysRecords(99999);
                    log('✅ تم فتح نافذة السجلات', 'success');
                } else {
                    log('❌ دالة viewDaysRecords غير موجودة', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function checkFilter() {
            log('🔍 فحص فلتر المشروع...', 'info');
            
            try {
                const modal = document.getElementById('daysRecordsModal');
                if (!modal) {
                    log('❌ النافذة غير مفتوحة - افتح النافذة أولاً', 'error');
                    return;
                }
                
                log('✅ النافذة مفتوحة', 'success');
                
                // فحص عناصر الفلتر
                const projectFilter = modal.querySelector('#projectFilter');
                const filterStatus = modal.querySelector('#filterStatus');
                
                log(`🔍 فلتر المشروع (#projectFilter): ${projectFilter ? '✅ موجود' : '❌ مفقود'}`, projectFilter ? 'success' : 'error');
                log(`🔍 مؤشر الحالة (#filterStatus): ${filterStatus ? '✅ موجود' : '❌ مفقود'}`, filterStatus ? 'success' : 'error');
                
                if (projectFilter) {
                    const options = projectFilter.querySelectorAll('option');
                    log(`📊 عدد خيارات الفلتر: ${options.length}`, 'info');
                    
                    options.forEach((option, index) => {
                        log(`  ${index + 1}. "${option.textContent}" (value: "${option.value}")`, 'info');
                    });
                }
                
                // فحص النص في النافذة
                if (modal.innerHTML.includes('فلتر المشروع')) {
                    log('✅ النص "فلتر المشروع" موجود في النافذة', 'success');
                } else {
                    log('❌ النص "فلتر المشروع" غير موجود في النافذة', 'error');
                }
                
                // فحص الأقسام
                const filterSections = modal.querySelectorAll('div[style*="background: #f8f9fa"]');
                log(`📊 عدد الأقسام بخلفية #f8f9fa: ${filterSections.length}`, 'info');
                
            } catch (error) {
                log('❌ خطأ في فحص الفلتر: ' + error.message, 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة الاختبار البسيط', 'success');
            
            setTimeout(() => {
                log('🔍 فحص النظام...', 'info');
                
                if (typeof employees !== 'undefined') {
                    log('✅ متغير employees موجود', 'success');
                } else {
                    log('❌ متغير employees غير موجود', 'error');
                }
                
                if (typeof viewDaysRecords === 'function') {
                    log('✅ دالة viewDaysRecords موجودة', 'success');
                } else {
                    log('❌ دالة viewDaysRecords غير موجودة', 'error');
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
