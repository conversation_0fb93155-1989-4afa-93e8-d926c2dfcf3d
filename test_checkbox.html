<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Checkbox</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .employees-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .employees-header h3 {
            margin: 0;
            padding: 0;
            border: none;
        }

        .bulk-activation {
            display: flex;
            align-items: center;
        }

        /* أنماط checkbox تنشيط الكل */
        .activate-all-container {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            user-select: none;
            font-size: 15px;
            font-weight: 600;
            color: #495057;
            transition: all 0.3s ease;
            padding: 10px 16px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .activate-all-container:hover {
            color: #28a745;
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-color: #28a745;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
        }

        .activate-all-container input[type="checkbox"] {
            width: 20px;
            height: 20px;
            cursor: pointer;
            accent-color: #28a745;
            transform: scale(1.2);
        }

        .activate-all-container i {
            font-size: 16px;
            color: #6c757d;
            transition: color 0.3s ease;
        }

        .activate-all-text {
            font-size: 15px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* تأثير عند التفعيل */
        .activate-all-container:hover i {
            color: #28a745;
        }

        .activate-all-container input[type="checkbox"]:checked ~ i {
            color: #28a745;
        }

        .activate-all-container input[type="checkbox"]:checked ~ .activate-all-text {
            color: #28a745;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار Checkbox تنشيط الكل</h1>
        
        <div id="status" class="status info">جاري التحميل...</div>
        
        <!-- محاكاة رأس قائمة الموظفين -->
        <div class="employees-header">
            <h3>قائمة الموظفين</h3>
            <div class="bulk-activation">
                <label class="activate-all-container">
                    <input type="checkbox" id="activateAllEmployees" onchange="testToggleAll()">
                    <i class="fas fa-users"></i>
                    <span class="activate-all-text">تنشيط الكل</span>
                </label>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>اختبارات:</h3>
            <button onclick="testCheckboxExists()" style="margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                فحص وجود Checkbox
            </button>
            <button onclick="testCheckboxFunction()" style="margin: 5px; padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                اختبار الدالة
            </button>
            <button onclick="testStyles()" style="margin: 5px; padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 5px; cursor: pointer;">
                فحص الأنماط
            </button>
        </div>
        
        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        function testToggleAll() {
            const checkbox = document.getElementById('activateAllEmployees');
            const isChecked = checkbox.checked;
            
            console.log('تم تغيير حالة Checkbox:', isChecked);
            addResult(`تم ${isChecked ? 'تفعيل' : 'إلغاء تفعيل'} Checkbox`, 'success');
        }

        function testCheckboxExists() {
            const checkbox = document.getElementById('activateAllEmployees');
            if (checkbox) {
                addResult('✅ Checkbox موجود ويعمل', 'success');
                console.log('Checkbox element:', checkbox);
            } else {
                addResult('❌ Checkbox غير موجود', 'error');
            }
        }

        function testCheckboxFunction() {
            const checkbox = document.getElementById('activateAllEmployees');
            if (checkbox) {
                // تغيير حالة Checkbox برمجياً
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
                addResult('✅ تم تشغيل الدالة برمجياً', 'success');
            } else {
                addResult('❌ لا يمكن اختبار الدالة - Checkbox غير موجود', 'error');
            }
        }

        function testStyles() {
            const container = document.querySelector('.activate-all-container');
            if (container) {
                const styles = window.getComputedStyle(container);
                addResult(`✅ الأنماط مطبقة - اللون: ${styles.color}`, 'success');
                console.log('Container styles:', styles);
            } else {
                addResult('❌ لا يمكن العثور على Container', 'error');
            }
        }

        // تشغيل اختبار أولي
        window.addEventListener('load', function() {
            showStatus('✅ تم تحميل الصفحة بنجاح', 'success');
            testCheckboxExists();
        });
    </script>
</body>
</html>
