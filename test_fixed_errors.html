<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأخطاء المصلحة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .error-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .error-item:last-child {
            border-bottom: none;
        }
        
        .error-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .error-icon.fixed {
            background: #28a745;
        }
        
        .error-icon.pending {
            background: #ffc107;
        }
        
        .error-icon.failed {
            background: #dc3545;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> اختبار الأخطاء المصلحة</h1>
            <p>التحقق من إصلاح جميع الأخطاء في النظام</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button success" onclick="runErrorFixTest()">
                <i class="fas fa-play"></i> اختبار الأخطاء المصلحة
            </button>
            
            <button class="test-button" onclick="testConsoleErrors()">
                <i class="fas fa-terminal"></i> فحص أخطاء Console
            </button>
            
            <button class="test-button warning" onclick="testEditButtons()">
                <i class="fas fa-edit"></i> اختبار أزرار التعديل
            </button>
            
            <button class="test-button danger" onclick="openOriginalApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
            </button>
        </div>

        <div class="error-list">
            <h4><i class="fas fa-bug"></i> قائمة الأخطاء المصلحة:</h4>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error1">✓</div>
                <div>
                    <strong>displayEmployees is not defined</strong>
                    <br><small>تم تغييرها إلى populateEmployees</small>
                </div>
            </div>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error2">✓</div>
                <div>
                    <strong>saveDaysDataToLocalStorage مفقودة</strong>
                    <br><small>تم إضافة دوال localStorage للأيام</small>
                </div>
            </div>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error3">✓</div>
                <div>
                    <strong>أزرار التعديل مفقودة</strong>
                    <br><small>تم إضافة أزرار التعديل في الوضع التقليدي</small>
                </div>
            </div>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error4">✓</div>
                <div>
                    <strong>editDaysRecordFromModal مفقودة</strong>
                    <br><small>تم إضافة نافذة التعديل المنفصلة</small>
                </div>
            </div>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error5">✓</div>
                <div>
                    <strong>التغييرات لا تحفظ</strong>
                    <br><small>تم إصلاح نظام الحفظ في localStorage</small>
                </div>
            </div>
            
            <div class="error-item">
                <div class="error-icon fixed" id="error6">✓</div>
                <div>
                    <strong>refreshDaysRecordsTable لا تعمل</strong>
                    <br><small>تم إصلاح منطق إنشاء الصفوف</small>
                </div>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار الأخطاء المصلحة...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [CONSOLE] جاهز لفحص أخطاء Console...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addConsoleLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function updateErrorStatus(errorNumber, status) {
            const icon = document.getElementById(`error${errorNumber}`);
            if (icon) {
                if (status === 'fixed') {
                    icon.className = 'error-icon fixed';
                    icon.innerHTML = '✓';
                } else if (status === 'failed') {
                    icon.className = 'error-icon failed';
                    icon.innerHTML = '✗';
                } else {
                    icon.className = 'error-icon pending';
                    icon.innerHTML = '?';
                }
            }
        }

        function runErrorFixTest() {
            updateStatus('بدء اختبار الأخطاء المصلحة...', 'warning');
            addConsoleLog('بدء اختبار شامل للأخطاء المصلحة...');
            
            let fixedErrors = 0;
            let totalErrors = 6;
            
            // اختبار 1: displayEmployees
            setTimeout(() => {
                if (typeof populateEmployees === 'function') {
                    updateErrorStatus(1, 'fixed');
                    addConsoleLog('✓ تم إصلاح خطأ displayEmployees');
                    fixedErrors++;
                } else {
                    updateErrorStatus(1, 'failed');
                    addConsoleLog('✗ خطأ displayEmployees لم يتم إصلاحه');
                }
            }, 500);
            
            // اختبار 2: localStorage functions
            setTimeout(() => {
                if (typeof saveDaysDataToLocalStorage === 'function' && typeof loadDaysDataFromLocalStorage === 'function') {
                    updateErrorStatus(2, 'fixed');
                    addConsoleLog('✓ تم إصلاح دوال localStorage');
                    fixedErrors++;
                } else {
                    updateErrorStatus(2, 'failed');
                    addConsoleLog('✗ دوال localStorage لم يتم إصلاحها');
                }
            }, 1000);
            
            // اختبار 3: Edit buttons
            setTimeout(() => {
                if (typeof editDaysRecordFromModal === 'function') {
                    updateErrorStatus(3, 'fixed');
                    addConsoleLog('✓ تم إصلاح أزرار التعديل');
                    fixedErrors++;
                } else {
                    updateErrorStatus(3, 'failed');
                    addConsoleLog('✗ أزرار التعديل لم يتم إصلاحها');
                }
            }, 1500);
            
            // اختبار 4: Modal edit function
            setTimeout(() => {
                if (typeof saveEditedRecord === 'function') {
                    updateErrorStatus(4, 'fixed');
                    addConsoleLog('✓ تم إصلاح نافذة التعديل');
                    fixedErrors++;
                } else {
                    updateErrorStatus(4, 'failed');
                    addConsoleLog('✗ نافذة التعديل لم يتم إصلاحها');
                }
            }, 2000);
            
            // اختبار 5: Save functionality
            setTimeout(() => {
                if (typeof saveAllRecordsChanges === 'function') {
                    updateErrorStatus(5, 'fixed');
                    addConsoleLog('✓ تم إصلاح نظام الحفظ');
                    fixedErrors++;
                } else {
                    updateErrorStatus(5, 'failed');
                    addConsoleLog('✗ نظام الحفظ لم يتم إصلاحه');
                }
            }, 2500);
            
            // اختبار 6: Table refresh
            setTimeout(() => {
                if (typeof refreshDaysRecordsTable === 'function') {
                    updateErrorStatus(6, 'fixed');
                    addConsoleLog('✓ تم إصلاح تحديث الجدول');
                    fixedErrors++;
                } else {
                    updateErrorStatus(6, 'failed');
                    addConsoleLog('✗ تحديث الجدول لم يتم إصلاحه');
                }
            }, 3000);
            
            // النتيجة النهائية
            setTimeout(() => {
                const percentage = (fixedErrors / totalErrors) * 100;
                addConsoleLog(`=== النتيجة النهائية ===`);
                addConsoleLog(`تم إصلاح ${fixedErrors}/${totalErrors} خطأ (${percentage.toFixed(0)}%)`);
                
                if (percentage >= 100) {
                    updateStatus('🎉 تم إصلاح جميع الأخطاء بنجاح! النظام جاهز للاستخدام', 'success');
                } else if (percentage >= 80) {
                    updateStatus(`تم إصلاح معظم الأخطاء (${percentage.toFixed(0)}%) - النظام يعمل بشكل جيد`, 'warning');
                } else {
                    updateStatus(`تم إصلاح ${percentage.toFixed(0)}% من الأخطاء - يحتاج المزيد من العمل`, 'error');
                }
            }, 4000);
        }

        function testConsoleErrors() {
            addConsoleLog('فحص أخطاء Console...');
            
            // فحص الأخطاء في Console
            const originalError = console.error;
            const originalWarn = console.warn;
            let errorCount = 0;
            let warnCount = 0;
            
            console.error = function(...args) {
                errorCount++;
                addConsoleLog(`ERROR: ${args.join(' ')}`);
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                warnCount++;
                addConsoleLog(`WARN: ${args.join(' ')}`);
                originalWarn.apply(console, args);
            };
            
            // اختبار بعض الدوال
            setTimeout(() => {
                try {
                    populateEmployees();
                    addConsoleLog('✓ populateEmployees تعمل بدون أخطاء');
                } catch (error) {
                    addConsoleLog(`✗ خطأ في populateEmployees: ${error.message}`);
                }
                
                // استعادة console الأصلي
                console.error = originalError;
                console.warn = originalWarn;
                
                addConsoleLog(`=== تقرير Console ===`);
                addConsoleLog(`أخطاء: ${errorCount}`);
                addConsoleLog(`تحذيرات: ${warnCount}`);
                
                if (errorCount === 0) {
                    updateStatus('✅ لا توجد أخطاء في Console!', 'success');
                } else {
                    updateStatus(`⚠️ تم العثور على ${errorCount} خطأ في Console`, 'warning');
                }
            }, 1000);
        }

        function testEditButtons() {
            // إنشاء بيانات تجريبية
            if (!employees.find(emp => emp.id === 999)) {
                employees.push({
                    id: 999,
                    name: 'موظف اختبار الأخطاء',
                    position: 'مطور',
                    employeeCode: 'TEST999',
                    employmentType: 'monthly',
                    basicSalary: 5000
                });
            }
            
            // فتح نافذة السجلات
            try {
                viewDaysRecords(999);
                updateStatus('تم فتح نافذة السجلات لاختبار أزرار التعديل', 'success');
                addConsoleLog('✓ تم فتح نافذة السجلات بنجاح');
            } catch (error) {
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
                addConsoleLog(`✗ خطأ في فتح النافذة: ${error.message}`);
            }
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة اختبار الأخطاء المصلحة', 'info');
            addConsoleLog('تم تحميل صفحة الاختبار');
            
            // اختبار سريع تلقائي
            setTimeout(() => {
                addConsoleLog('--- اختبار تلقائي سريع ---');
                
                if (typeof populateEmployees === 'function') {
                    addConsoleLog('✓ populateEmployees متاحة');
                } else {
                    addConsoleLog('✗ populateEmployees غير متاحة');
                }
                
                if (typeof saveDaysDataToLocalStorage === 'function') {
                    addConsoleLog('✓ saveDaysDataToLocalStorage متاحة');
                } else {
                    addConsoleLog('✗ saveDaysDataToLocalStorage غير متاحة');
                }
                
                if (typeof editDaysRecordFromModal === 'function') {
                    addConsoleLog('✓ editDaysRecordFromModal متاحة');
                } else {
                    addConsoleLog('✗ editDaysRecordFromModal غير متاحة');
                }
            }, 2000);
        });
    </script>
</body>
</html>
