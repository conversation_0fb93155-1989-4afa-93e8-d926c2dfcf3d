<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث النافذة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: block;
            width: 100%;
        }
        .test-button:hover {
            background: #1976D2;
        }
        .result {
            background: #e8f5e9;
            border: 1px solid #4CAF50;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار تحديث نافذة حساب الراتب</h1>
        
        <div id="results"></div>
        
        <button class="test-button" onclick="testCacheUpdate()">
            🧪 اختبار تحديث الكاش
        </button>
        
        <button class="test-button" onclick="testNewWindow()">
            🪟 اختبار النافذة الجديدة
        </button>
        
        <button class="test-button" onclick="clearAllCache()">
            🗑️ مسح جميع البيانات المحفوظة
        </button>
        
        <button class="test-button" onclick="forceReload()">
            🔄 إعادة تحميل قوية
        </button>
    </div>

    <!-- تضمين الملف الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function addResult(message, type = 'success') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type === 'error' ? 'error' : ''}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function testCacheUpdate() {
            addResult('🔍 اختبار تحديث الكاش...', 'info');
            
            // اختبار وجود الدالة
            if (typeof openSalaryCalculator === 'function') {
                addResult('✅ دالة openSalaryCalculator موجودة');
                
                // اختبار استدعاء الدالة
                try {
                    // إنشاء موظف تجريبي إذا لم يكن موجود
                    if (typeof employees === 'undefined' || !employees || employees.length === 0) {
                        window.employees = [{
                            id: 999,
                            name: 'موظف تجريبي',
                            position: 'مطور',
                            type: 'monthly',
                            salary: { basic: 5000 }
                        }];
                        addResult('✅ تم إنشاء موظف تجريبي');
                    }
                    
                    // استدعاء الدالة
                    openSalaryCalculator(999);
                    addResult('✅ تم استدعاء الدالة بنجاح');
                    
                } catch (error) {
                    addResult(`❌ خطأ في استدعاء الدالة: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ دالة openSalaryCalculator غير موجودة', 'error');
            }
        }

        function testNewWindow() {
            addResult('🪟 اختبار النافذة الجديدة...', 'info');
            
            // إنشاء نافذة اختبار مباشرة
            const modal = document.createElement('div');
            modal.id = 'testModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 5px;
                    width: 380px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    text-align: center;
                ">
                    <h3 style="color: #2196F3; margin: 0 0 15px 0;">🎉 النافذة المحدثة!</h3>
                    <p style="margin: 10px 0; font-size: 14px;">هذه هي النافذة الجديدة المصغرة</p>
                    <button onclick="document.getElementById('testModal').remove()" style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 3px;
                        cursor: pointer;
                        font-size: 12px;
                    ">إغلاق</button>
                </div>
            `;
            
            document.body.appendChild(modal);
            addResult('✅ تم إنشاء نافذة اختبار مصغرة');
        }

        function clearAllCache() {
            addResult('🗑️ مسح جميع البيانات المحفوظة...', 'info');
            
            try {
                // مسح localStorage
                localStorage.clear();
                addResult('✅ تم مسح localStorage');
                
                // مسح sessionStorage
                sessionStorage.clear();
                addResult('✅ تم مسح sessionStorage');
                
                addResult('✅ تم مسح جميع البيانات المحفوظة');
                addResult('🔄 يرجى إعادة تحميل الصفحة الآن', 'info');
                
            } catch (error) {
                addResult(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function forceReload() {
            addResult('🔄 إعادة تحميل قوية...', 'info');
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('🚀 تم تحميل صفحة الاختبار', 'info');
            addResult('📝 استخدم الأزرار أعلاه لاختبار التحديثات', 'info');
        });
    </script>
</body>
</html>
