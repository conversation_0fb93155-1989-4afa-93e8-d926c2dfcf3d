<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة القابلة للحركة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn.success:hover {
            background: #1e7e34;
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .btn.warning:hover {
            background: #e0a800;
            box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
        }

        .btn.danger {
            background: #dc3545;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .btn.danger:hover {
            background: #c82333;
            box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .instructions {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .demo-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .demo-area h4 {
            color: #6c757d;
            margin-bottom: 10px;
        }

        .demo-area p {
            color: #adb5bd;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖱️ اختبار النافذة القابلة للحركة</h1>
            <p>اختبار ميزة سحب وحركة نافذة حساب الأيام</p>
        </div>

        <div class="content">
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h4>📋 تعليمات الاستخدام:</h4>
                <ul>
                    <li>اضغط على "فتح النافذة" لفتح نافذة حساب الأيام</li>
                    <li>اسحب النافذة من شريط العنوان (الجزء الأزرق) لتحريكها</li>
                    <li>لاحظ رمز السحب (⋮⋮) في الجانب الأيسر من شريط العنوان</li>
                    <li>يمكنك سحب النافذة إلى أي مكان على الشاشة</li>
                    <li>النافذة تعود لموضعها الأصلي عند إعادة فتحها</li>
                </ul>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn success" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="createTestProjects()">
                        إنشاء مشروعات تجريبية
                    </button>
                    <button class="btn warning" onclick="checkDragSystem()">
                        فحص نظام السحب
                    </button>
                </div>
            </div>

            <!-- قسم اختبار السحب -->
            <div class="test-section">
                <h3>🖱️ اختبار ميزة السحب</h3>
                <div class="grid">
                    <button class="btn" onclick="openDraggableWindow()">
                        فتح النافذة القابلة للحركة
                    </button>
                    <button class="btn" onclick="testDragFunctionality()">
                        اختبار وظائف السحب
                    </button>
                    <button class="btn warning" onclick="resetWindowPosition()">
                        إعادة تعيين الموضع
                    </button>
                    <button class="btn danger" onclick="closeAllWindows()">
                        إغلاق جميع النوافذ
                    </button>
                </div>
            </div>

            <!-- منطقة العرض التوضيحي -->
            <div class="demo-area">
                <h4>منطقة العرض التوضيحي</h4>
                <p>ستظهر النافذة القابلة للحركة هنا عند فتحها</p>
                <p>جرب سحبها إلى مواضع مختلفة!</p>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار النافذة القابلة للحركة...
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 777,
                name: 'علي محمد (اختبار السحب)',
                position: 'مطور برمجيات',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 6000,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 777)) {
                employees.push(testEmployee);
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createTestProjects() {
            const testProjects = [
                { id: 401, name: 'مشروع اختبار السحب 1', status: 'active' },
                { id: 402, name: 'مشروع اختبار السحب 2', status: 'active' }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                }
            });

            updateStatus('تم إنشاء مشروعات اختبار السحب بنجاح', 'success');
        }

        function checkDragSystem() {
            const checks = [
                { name: 'دالة makeDraggable', test: () => typeof makeDraggable === 'function' },
                { name: 'دالة resetModalPosition', test: () => typeof resetModalPosition === 'function' },
                { name: 'دالة openDaysCalculationModal', test: () => typeof openDaysCalculationModal === 'function' },
                { name: 'نافذة حساب الأيام', test: () => document.getElementById('daysCalculationModal') !== null },
                { name: 'عنصر draggable-modal', test: () => {
                    const modal = document.getElementById('daysCalculationModal');
                    return modal && modal.querySelector('.draggable-modal') !== null;
                }},
                { name: 'عنصر draggable-header', test: () => {
                    const modal = document.getElementById('daysCalculationModal');
                    return modal && modal.querySelector('.draggable-header') !== null;
                }}
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                } else {
                    results.push(`✗ ${check.name}`);
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص نظام السحب: ${passed}/${checks.length} عناصر موجودة`, status);
            
            console.log('نتائج فحص نظام السحب:');
            results.forEach(result => console.log(result));
        }

        function openDraggableWindow() {
            if (!employees.find(emp => emp.id === 777)) {
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            openDaysCalculationModal(777);
            updateStatus('تم فتح النافذة القابلة للحركة - جرب سحبها من شريط العنوان!', 'success');
        }

        function testDragFunctionality() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const draggableModal = modal.querySelector('.draggable-modal');
            const draggableHeader = modal.querySelector('.draggable-header');

            if (draggableModal && draggableHeader) {
                updateStatus('✓ النافذة جاهزة للسحب - اسحب من شريط العنوان الأزرق', 'success');
                
                // إضافة تأثير بصري لتوضيح منطقة السحب
                draggableHeader.style.animation = 'pulse 2s infinite';
                
                setTimeout(() => {
                    draggableHeader.style.animation = '';
                }, 4000);
            } else {
                updateStatus('خطأ: عناصر السحب غير موجودة', 'error');
            }
        }

        function resetWindowPosition() {
            const modal = document.getElementById('daysCalculationModal');
            if (modal) {
                resetModalPosition(modal);
                updateStatus('تم إعادة تعيين موضع النافذة', 'success');
            } else {
                updateStatus('لم يتم العثور على النافذة', 'warning');
            }
        }

        function closeAllWindows() {
            const modal = document.getElementById('daysCalculationModal');
            if (modal) {
                modal.style.display = 'none';
                updateStatus('تم إغلاق جميع النوافذ', 'info');
            }
        }

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
                100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
            }
        `;
        document.head.appendChild(style);

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار النافذة القابلة للحركة - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
