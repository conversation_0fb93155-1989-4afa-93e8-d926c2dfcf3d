<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة التشخيص النهائية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
            font-size: 13px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        
        .debug-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        
        .debug-panel {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            font-size: 11px;
        }
        
        .debug-panel h5 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 12px;
            font-weight: 600;
        }
        
        .debug-value {
            background: #e9ecef;
            padding: 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            margin: 3px 0;
            word-break: break-all;
            max-height: 60px;
            overflow-y: auto;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 11px;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .test-button:hover { transform: translateY(-1px); }
        .test-button.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .test-button.warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; }
        .test-button.danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            max-height: 250px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
            margin: 15px 0;
            font-size: 12px;
        }
        
        .status-card.success { border-left-color: #28a745; background: #d4edda; }
        .status-card.warning { border-left-color: #ffc107; background: #fff3cd; }
        .status-card.error { border-left-color: #dc3545; background: #f8d7da; }
        
        .live-monitor {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #bbdefb;
            margin: 10px 0;
            font-size: 11px;
        }
        
        .event-log {
            background: #fff3e0;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ffcc02;
            margin: 8px 0;
            font-size: 10px;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> أداة التشخيص النهائية</h1>
            <p>تشخيص شامل ومتقدم لمشكلة عدم تتبع التغييرات</p>
        </div>

        <div style="text-align: center; margin: 15px 0;">
            <button class="test-button danger" onclick="runUltimateTest()">
                <i class="fas fa-rocket"></i> تشخيص شامل
            </button>
            
            <button class="test-button" onclick="createData()">
                <i class="fas fa-plus"></i> إنشاء بيانات
            </button>
            
            <button class="test-button warning" onclick="openWindow()">
                <i class="fas fa-window-maximize"></i> فتح النافذة
            </button>
            
            <button class="test-button success" onclick="interceptFunctions()">
                <i class="fas fa-shield-alt"></i> مراقبة الدوال
            </button>
            
            <button class="test-button" onclick="manualEdit()">
                <i class="fas fa-edit"></i> تعديل يدوي
            </button>
            
            <button class="test-button" onclick="forceSave()">
                <i class="fas fa-save"></i> حفظ إجباري
            </button>
        </div>

        <div class="debug-grid">
            <div class="debug-panel">
                <h5><i class="fas fa-database"></i> البيانات الأساسية</h5>
                <div class="debug-value" id="employeesCount">الموظفين: 0</div>
                <div class="debug-value" id="daysDataCount">سجلات الأيام: 0</div>
                <div class="debug-value" id="recordsChangesData">recordsChanges: {}</div>
            </div>
            
            <div class="debug-panel">
                <h5><i class="fas fa-cogs"></i> حالة الدوال</h5>
                <div class="debug-value" id="updateRecordFieldFunc">updateRecordField: غير محدد</div>
                <div class="debug-value" id="saveAllChangesFunc">saveAllRecordsChanges: غير محدد</div>
                <div class="debug-value" id="viewDaysRecordsFunc">viewDaysRecords: غير محدد</div>
            </div>
            
            <div class="debug-panel">
                <h5><i class="fas fa-window-maximize"></i> حالة النافذة</h5>
                <div class="debug-value" id="modalState">النافذة: مغلقة</div>
                <div class="debug-value" id="saveButtonState">زر الحفظ: غير موجود</div>
                <div class="debug-value" id="inputFieldsState">حقول الإدخال: 0</div>
            </div>
        </div>

        <div class="live-monitor">
            <h5><i class="fas fa-eye"></i> المراقبة المباشرة:</h5>
            <div id="liveStatus">في انتظار الأحداث...</div>
        </div>

        <div class="event-log">
            <h5><i class="fas fa-list"></i> سجل الأحداث:</h5>
            <div id="eventLog">لا توجد أحداث بعد...</div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء التشخيص النهائي...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [ULTIMATE_DEBUG] أداة التشخيص النهائية جاهزة...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let intercepted = false;
        let eventCounter = 0;
        let originalFunctions = {};
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function addEvent(event) {
            eventCounter++;
            const eventLog = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            eventLog.innerHTML += `[${eventCounter}] ${timestamp}: ${event}<br>`;
            eventLog.scrollTop = eventLog.scrollHeight;
            
            document.getElementById('liveStatus').textContent = `آخر حدث: ${event}`;
        }

        function updateDebugPanels() {
            // البيانات الأساسية
            document.getElementById('employeesCount').textContent = 
                `الموظفين: ${(employees || []).length}`;
            document.getElementById('daysDataCount').textContent = 
                `سجلات الأيام: ${(employeeDaysData || []).length}`;
            document.getElementById('recordsChangesData').textContent = 
                `recordsChanges: ${JSON.stringify(recordsChanges || {})}`;
            
            // حالة الدوال
            document.getElementById('updateRecordFieldFunc').textContent = 
                `updateRecordField: ${typeof updateRecordField === 'function' ? 'موجودة ✓' : 'مفقودة ✗'}`;
            document.getElementById('saveAllChangesFunc').textContent = 
                `saveAllRecordsChanges: ${typeof saveAllRecordsChanges === 'function' ? 'موجودة ✓' : 'مفقودة ✗'}`;
            document.getElementById('viewDaysRecordsFunc').textContent = 
                `viewDaysRecords: ${typeof viewDaysRecords === 'function' ? 'موجودة ✓' : 'مفقودة ✗'}`;
            
            // حالة النافذة
            const modal = document.getElementById('daysRecordsModal');
            const saveBtn = modal ? modal.querySelector('#saveChangesBtn') : null;
            const inputFields = modal ? modal.querySelectorAll('input[onchange*="updateRecordField"]') : [];
            
            document.getElementById('modalState').textContent = 
                `النافذة: ${modal ? 'مفتوحة ✓' : 'مغلقة ✗'}`;
            document.getElementById('saveButtonState').textContent = 
                `زر الحفظ: ${saveBtn ? 'موجود ✓' : 'غير موجود ✗'}`;
            document.getElementById('inputFieldsState').textContent = 
                `حقول الإدخال: ${inputFields.length}`;
        }

        function interceptFunctions() {
            if (intercepted) {
                addLog('⚠️ المراقبة مفعلة مسبقاً');
                return;
            }
            
            addLog('🔧 تفعيل مراقبة الدوال...');
            
            // مراقبة updateRecordField
            if (typeof updateRecordField === 'function') {
                originalFunctions.updateRecordField = updateRecordField;
                
                window.updateRecordField = function(recordId, fieldName, newValue) {
                    addEvent(`updateRecordField(${recordId}, "${fieldName}", "${newValue}")`);
                    addLog(`🔔 استدعاء updateRecordField: ${recordId}, ${fieldName}, ${newValue}`);
                    
                    try {
                        const result = originalFunctions.updateRecordField.apply(this, arguments);
                        addEvent(`updateRecordField نجح`);
                        addLog(`✅ updateRecordField نجح`);
                        updateDebugPanels();
                        return result;
                    } catch (error) {
                        addEvent(`updateRecordField فشل: ${error.message}`);
                        addLog(`❌ updateRecordField فشل: ${error.message}`);
                        throw error;
                    }
                };
                
                addLog('✓ تم تفعيل مراقبة updateRecordField');
            } else {
                addLog('❌ updateRecordField غير موجودة');
            }
            
            // مراقبة saveAllRecordsChanges
            if (typeof saveAllRecordsChanges === 'function') {
                originalFunctions.saveAllRecordsChanges = saveAllRecordsChanges;
                
                window.saveAllRecordsChanges = function(employeeId) {
                    addEvent(`saveAllRecordsChanges(${employeeId})`);
                    addLog(`🔔 استدعاء saveAllRecordsChanges: ${employeeId}`);
                    
                    try {
                        const result = originalFunctions.saveAllRecordsChanges.apply(this, arguments);
                        addEvent(`saveAllRecordsChanges نجح`);
                        addLog(`✅ saveAllRecordsChanges نجح`);
                        updateDebugPanels();
                        return result;
                    } catch (error) {
                        addEvent(`saveAllRecordsChanges فشل: ${error.message}`);
                        addLog(`❌ saveAllRecordsChanges فشل: ${error.message}`);
                        throw error;
                    }
                };
                
                addLog('✓ تم تفعيل مراقبة saveAllRecordsChanges');
            } else {
                addLog('❌ saveAllRecordsChanges غير موجودة');
            }
            
            intercepted = true;
            updateStatus('تم تفعيل مراقبة الدوال بنجاح!', 'success');
        }

        function createData() {
            addLog('🔧 إنشاء بيانات تجريبية...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 111)) {
                    employees.push({
                        id: 111,
                        name: 'أحمد محمد - تشخيص نهائي',
                        position: 'مطور',
                        employeeCode: 'DEBUG111',
                        employmentType: 'monthly',
                        basicSalary: 5000
                    });
                    addEvent('تم إنشاء موظف تجريبي');
                }
                
                // إنشاء سجل أيام
                const testRecord = {
                    id: Date.now() + 1000,
                    employeeId: 111,
                    projectName: 'مشروع التشخيص النهائي',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 25,
                    absenceDays: 0,
                    overtimeHours: 5,
                    createdAt: new Date().toISOString()
                };
                
                if (!employeeDaysData.find(r => r.id === testRecord.id)) {
                    employeeDaysData.push(testRecord);
                    addEvent('تم إنشاء سجل أيام تجريبي');
                }
                
                saveDaysDataToLocalStorage();
                updateDebugPanels();
                addLog('✓ تم إنشاء البيانات التجريبية');
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('❌ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openWindow() {
            try {
                if (!employees.find(emp => emp.id === 111)) {
                    updateStatus('يرجى إنشاء البيانات أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة السجلات...');
                addEvent('فتح نافذة السجلات');
                
                viewDaysRecords(111);
                
                setTimeout(() => {
                    updateDebugPanels();
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        addLog('✓ تم فتح النافذة بنجاح');
                        addEvent('النافذة مفتوحة');
                        updateStatus('تم فتح النافذة بنجاح!', 'success');
                    } else {
                        addLog('❌ فشل في فتح النافذة');
                        addEvent('فشل في فتح النافذة');
                        updateStatus('فشل في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('❌ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function manualEdit() {
            addLog('🧪 محاولة تعديل يدوي...');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                addLog('❌ النافذة غير مفتوحة');
                updateStatus('يرجى فتح النافذة أولاً', 'error');
                return;
            }
            
            // البحث عن حقل إدخال
            const inputField = modal.querySelector('input[onchange*="updateRecordField"]');
            if (!inputField) {
                addLog('❌ لم يتم العثور على حقول إدخال');
                updateStatus('لم يتم العثور على حقول إدخال', 'error');
                return;
            }
            
            addLog('✓ تم العثور على حقل إدخال');
            addEvent('بدء التعديل اليدوي');
            
            // تعديل القيمة
            const originalValue = inputField.value;
            const newValue = originalValue + ' - معدل';
            
            addLog(`🔄 تغيير من "${originalValue}" إلى "${newValue}"`);
            
            inputField.value = newValue;
            inputField.dispatchEvent(new Event('change', { bubbles: true }));
            inputField.dispatchEvent(new Event('input', { bubbles: true }));
            
            addEvent(`تم تعديل القيمة إلى: ${newValue}`);
            
            setTimeout(() => {
                updateDebugPanels();
                const changesCount = Object.keys(recordsChanges || {}).length;
                
                if (changesCount > 0) {
                    addLog(`✅ تم تسجيل ${changesCount} تغيير`);
                    addEvent(`تم تسجيل ${changesCount} تغيير`);
                    updateStatus('✅ التعديل اليدوي نجح!', 'success');
                } else {
                    addLog('❌ لم يتم تسجيل أي تغيير');
                    addEvent('فشل في تسجيل التغيير');
                    updateStatus('❌ التعديل اليدوي فشل', 'error');
                }
            }, 500);
        }

        function forceSave() {
            addLog('💾 محاولة حفظ إجباري...');
            addEvent('بدء الحفظ الإجباري');
            
            try {
                // تجاوز تأكيد الحفظ
                const originalConfirm = window.confirm;
                window.confirm = () => {
                    addEvent('تم تأكيد الحفظ');
                    return true;
                };
                
                saveAllRecordsChanges(111);
                
                // استعادة confirm الأصلي
                window.confirm = originalConfirm;
                
                addLog('✅ تم تنفيذ الحفظ الإجباري');
                addEvent('تم تنفيذ الحفظ الإجباري');
                updateStatus('تم تنفيذ الحفظ الإجباري!', 'success');
                
            } catch (error) {
                addLog('❌ فشل الحفظ الإجباري: ' + error.message);
                addEvent('فشل الحفظ الإجباري');
                updateStatus('فشل الحفظ الإجباري: ' + error.message, 'error');
            }
        }

        function runUltimateTest() {
            updateStatus('🚀 بدء التشخيص الشامل...', 'warning');
            addLog('=== بدء التشخيص الشامل ===');
            addEvent('بدء التشخيص الشامل');
            
            // خطوة 1: تفعيل المراقبة
            setTimeout(() => {
                interceptFunctions();
            }, 500);
            
            // خطوة 2: إنشاء البيانات
            setTimeout(() => {
                createData();
            }, 1500);
            
            // خطوة 3: فتح النافذة
            setTimeout(() => {
                openWindow();
            }, 3000);
            
            // خطوة 4: التعديل اليدوي
            setTimeout(() => {
                manualEdit();
            }, 5000);
            
            // خطوة 5: الحفظ الإجباري
            setTimeout(() => {
                forceSave();
            }, 7000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء التشخيص الشامل ===');
                addEvent('انتهاء التشخيص الشامل');
                updateDebugPanels();
                
                const changesCount = Object.keys(recordsChanges || {}).length;
                if (changesCount === 0) {
                    updateStatus('🎉 التشخيص مكتمل - تحقق من سجل الأحداث', 'info');
                } else {
                    updateStatus(`⚠️ يوجد ${changesCount} تغيير لم يتم حفظه`, 'warning');
                }
            }, 9000);
        }

        // تحديث دوري
        setInterval(updateDebugPanels, 3000);

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('أداة التشخيص النهائية جاهزة!', 'info');
            addLog('تم تحميل أداة التشخيص النهائية');
            updateDebugPanels();
        });
    </script>
</body>
</html>
