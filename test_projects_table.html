<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض المشروعات - جدول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .projects-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            max-height: 600px;
            overflow-y: auto;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 5px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .log {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-table"></i> اختبار عرض المشروعات - جدول</h1>
            <p>اختبار العرض الجديد للمشروعات في شكل جدول بدلاً من البطاقات</p>
        </div>

        <div class="content">
            <!-- قسم الاختبارات -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> اختبارات العرض</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="openProjectsModal()">
                        <i class="fas fa-external-link-alt"></i> فتح نافذة المشروعات
                    </button>
                    
                    <button class="test-button" onclick="testTableDisplay()">
                        <i class="fas fa-table"></i> اختبار عرض الجدول
                    </button>
                    
                    <button class="test-button warning" onclick="addTestProjects()">
                        <i class="fas fa-plus"></i> إضافة مشروعات تجريبية
                    </button>
                    
                    <button class="test-button danger" onclick="clearProjects()">
                        <i class="fas fa-trash"></i> مسح جميع المشروعات
                    </button>
                    
                    <button class="test-button" onclick="window.open('/', '_blank')">
                        <i class="fas fa-home"></i> التطبيق الأصلي
                    </button>
                    
                    <button class="test-button" onclick="previewTable()">
                        <i class="fas fa-eye"></i> معاينة الجدول هنا
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div class="test-section">
                <h3><i class="fas fa-info-circle"></i> حالة النظام</h3>
                <div>
                    <span class="status-indicator" id="projectsCount">جاري التحقق...</span>
                    <span class="status-indicator" id="tableStatus">جاري التحقق...</span>
                    <span class="status-indicator" id="functionsStatus">جاري التحقق...</span>
                </div>
            </div>

            <!-- معاينة الجدول -->
            <div class="test-section">
                <h3><i class="fas fa-table"></i> معاينة جدول المشروعات</h3>
                <div class="projects-preview" id="projectsPreview">
                    <p style="text-align: center; color: #666; padding: 40px;">
                        <i class="fas fa-table" style="font-size: 48px; opacity: 0.3; margin-bottom: 15px; display: block;"></i>
                        انقر على "معاينة الجدول هنا" لعرض الجدول
                    </p>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="log" id="log">
                <div class="log-entry log-info">[تحميل] جاهز لاختبار عرض الجدول...</div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-indicator status-${status}`;
        }

        function testTableDisplay() {
            log('🔍 اختبار عرض الجدول...', 'info');
            
            try {
                // اختبار وجود الدوال
                if (typeof createProjectsTable === 'function') {
                    log('✅ دالة createProjectsTable موجودة', 'success');
                } else {
                    log('❌ دالة createProjectsTable غير موجودة', 'error');
                }
                
                if (typeof createProjectTableRow === 'function') {
                    log('✅ دالة createProjectTableRow موجودة', 'success');
                } else {
                    log('❌ دالة createProjectTableRow غير موجودة', 'error');
                }
                
                if (typeof populateProjectsList === 'function') {
                    log('✅ دالة populateProjectsList موجودة', 'success');
                } else {
                    log('❌ دالة populateProjectsList غير موجودة', 'error');
                }
                
                updateStatus('functionsStatus', 'success', 'دوال الجدول متاحة');
                
            } catch (error) {
                log('❌ خطأ في اختبار الجدول: ' + error.message, 'error');
                updateStatus('functionsStatus', 'error', 'خطأ في الدوال');
            }
        }

        function addTestProjects() {
            log('➕ إضافة مشروعات تجريبية...', 'info');
            
            const testProjects = [
                {
                    id: Date.now() + 1,
                    name: 'مشروع تطوير الموقع الإلكتروني',
                    description: 'تطوير موقع إلكتروني متجاوب وحديث للشركة',
                    po: 'PO-2024-003',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    name: 'مشروع تحديث النظام المحاسبي',
                    description: 'تحديث وتطوير النظام المحاسبي الحالي',
                    po: 'PO-2024-004',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    name: 'مشروع التدريب والتطوير',
                    description: 'برنامج تدريبي شامل لتطوير مهارات الموظفين',
                    po: 'PO-2024-005',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ];
            
            testProjects.forEach(project => {
                projects.push(project);
                log(`✅ تم إضافة: ${project.name}`, 'success');
            });
            
            saveProjectsToLocalStorage();
            updateProjectsCount();
            
            log(`🎉 تم إضافة ${testProjects.length} مشروع تجريبي`, 'success');
        }

        function clearProjects() {
            if (confirm('هل أنت متأكد من حذف جميع المشروعات؟')) {
                log('🗑️ مسح جميع المشروعات...', 'warning');
                projects.length = 0;
                saveProjectsToLocalStorage();
                updateProjectsCount();
                
                // مسح المعاينة
                document.getElementById('projectsPreview').innerHTML = `
                    <p style="text-align: center; color: #666; padding: 40px;">
                        <i class="fas fa-table" style="font-size: 48px; opacity: 0.3; margin-bottom: 15px; display: block;"></i>
                        لا توجد مشروعات لعرضها
                    </p>
                `;
                
                log('✅ تم مسح جميع المشروعات', 'success');
            }
        }

        function previewTable() {
            log('👁️ معاينة الجدول...', 'info');
            
            const previewContainer = document.getElementById('projectsPreview');
            
            if (projects.length === 0) {
                previewContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">📋</div>
                        <h4 style="margin: 0 0 10px 0; color: #495057;">لا توجد مشروعات</h4>
                        <p style="margin: 0; font-size: 14px;">أضف مشروعات تجريبية أولاً</p>
                    </div>
                `;
                log('⚠️ لا توجد مشروعات لعرضها', 'warning');
                return;
            }
            
            try {
                const table = createProjectsTable();
                previewContainer.innerHTML = '';
                previewContainer.appendChild(table);
                
                log(`✅ تم عرض ${projects.length} مشروع في الجدول`, 'success');
                updateStatus('tableStatus', 'success', 'الجدول يعمل بنجاح');
                
            } catch (error) {
                log('❌ خطأ في إنشاء الجدول: ' + error.message, 'error');
                updateStatus('tableStatus', 'error', 'خطأ في الجدول');
            }
        }

        function updateProjectsCount() {
            const count = projects ? projects.length : 0;
            updateStatus('projectsCount', count > 0 ? 'success' : 'info', `${count} مشروع`);
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة اختبار الجدول', 'success');
            
            setTimeout(() => {
                log('🔍 تشغيل اختبار تلقائي...', 'info');
                updateProjectsCount();
                testTableDisplay();
                
                if (projects && projects.length > 0) {
                    previewTable();
                }
            }, 1000);
        });
    </script>
</body>
</html>
