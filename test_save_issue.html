<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة عدم حفظ التغييرات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .diagnostic-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .diagnostic-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .diagnostic-item:last-child {
            border-bottom: none;
        }
        
        .diagnostic-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .diagnostic-icon.pass {
            background: #28a745;
        }
        
        .diagnostic-icon.fail {
            background: #dc3545;
        }
        
        .diagnostic-icon.pending {
            background: #6c757d;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .step-by-step {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e3f2fd;
        }
        
        .step-number {
            background: #1976d2;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص مشكلة عدم حفظ التغييرات</h1>
            <p>فحص شامل لنظام حفظ التغييرات في سجل الأيام</p>
        </div>

        <div class="step-by-step">
            <h4><i class="fas fa-list-ol"></i> خطوات التشخيص:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>فحص وجود الدوال</strong> - التحقق من وجود جميع دوال الحفظ
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>اختبار localStorage</strong> - فحص قدرة المتصفح على الحفظ
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>محاكاة التعديل</strong> - تجربة تعديل سجل وحفظه
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>فحص البيانات</strong> - التحقق من حفظ البيانات فعلياً
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button danger" onclick="runFullDiagnostic()">
                <i class="fas fa-play"></i> تشغيل التشخيص الشامل
            </button>
            
            <button class="test-button" onclick="testSaveFunctions()">
                <i class="fas fa-cogs"></i> فحص دوال الحفظ
            </button>
            
            <button class="test-button warning" onclick="simulateEditAndSave()">
                <i class="fas fa-edit"></i> محاكاة التعديل والحفظ
            </button>
            
            <button class="test-button success" onclick="openOriginalApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
            </button>
        </div>

        <div class="diagnostic-list">
            <h4><i class="fas fa-clipboard-check"></i> قائمة التشخيص:</h4>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check1">1</div>
                <div>
                    <strong>دالة updateRecordField موجودة</strong>
                    <br><small>تحديث حقول السجل في الذاكرة</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check2">2</div>
                <div>
                    <strong>دالة saveAllRecordsChanges موجودة</strong>
                    <br><small>حفظ جميع التغييرات</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check3">3</div>
                <div>
                    <strong>دالة saveDaysDataToLocalStorage موجودة</strong>
                    <br><small>حفظ البيانات في localStorage</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check4">4</div>
                <div>
                    <strong>localStorage يعمل بشكل صحيح</strong>
                    <br><small>المتصفح يدعم حفظ البيانات</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check5">5</div>
                <div>
                    <strong>متغير recordsChanges يعمل</strong>
                    <br><small>تتبع التغييرات قبل الحفظ</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check6">6</div>
                <div>
                    <strong>البيانات تحفظ فعلياً</strong>
                    <br><small>التغييرات تظهر بعد إعادة التحميل</small>
                </div>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء التشخيص...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [DIAGNOSTIC] جاهز لبدء تشخيص مشكلة عدم حفظ التغييرات...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function updateDiagnosticItem(itemNumber, status) {
            const icon = document.getElementById(`check${itemNumber}`);
            if (icon) {
                if (status === 'pass') {
                    icon.className = 'diagnostic-icon pass';
                    icon.innerHTML = '✓';
                } else if (status === 'fail') {
                    icon.className = 'diagnostic-icon fail';
                    icon.innerHTML = '✗';
                } else {
                    icon.className = 'diagnostic-icon pending';
                    icon.innerHTML = itemNumber;
                }
            }
        }

        function testSaveFunctions() {
            addLog('🔍 بدء فحص دوال الحفظ...');
            
            // فحص 1: updateRecordField
            if (typeof updateRecordField === 'function') {
                updateDiagnosticItem(1, 'pass');
                addLog('✓ دالة updateRecordField موجودة');
            } else {
                updateDiagnosticItem(1, 'fail');
                addLog('✗ دالة updateRecordField مفقودة');
            }
            
            // فحص 2: saveAllRecordsChanges
            if (typeof saveAllRecordsChanges === 'function') {
                updateDiagnosticItem(2, 'pass');
                addLog('✓ دالة saveAllRecordsChanges موجودة');
            } else {
                updateDiagnosticItem(2, 'fail');
                addLog('✗ دالة saveAllRecordsChanges مفقودة');
            }
            
            // فحص 3: saveDaysDataToLocalStorage
            if (typeof saveDaysDataToLocalStorage === 'function') {
                updateDiagnosticItem(3, 'pass');
                addLog('✓ دالة saveDaysDataToLocalStorage موجودة');
            } else {
                updateDiagnosticItem(3, 'fail');
                addLog('✗ دالة saveDaysDataToLocalStorage مفقودة');
            }
            
            // فحص 4: localStorage
            try {
                localStorage.setItem('test_save', 'test_value');
                const retrieved = localStorage.getItem('test_save');
                if (retrieved === 'test_value') {
                    updateDiagnosticItem(4, 'pass');
                    addLog('✓ localStorage يعمل بشكل صحيح');
                    localStorage.removeItem('test_save');
                } else {
                    updateDiagnosticItem(4, 'fail');
                    addLog('✗ localStorage لا يعمل بشكل صحيح');
                }
            } catch (error) {
                updateDiagnosticItem(4, 'fail');
                addLog('✗ خطأ في localStorage: ' + error.message);
            }
            
            // فحص 5: متغير recordsChanges
            if (typeof recordsChanges !== 'undefined') {
                updateDiagnosticItem(5, 'pass');
                addLog('✓ متغير recordsChanges موجود');
            } else {
                updateDiagnosticItem(5, 'fail');
                addLog('✗ متغير recordsChanges مفقود');
            }
        }

        function simulateEditAndSave() {
            addLog('🧪 بدء محاكاة التعديل والحفظ...');
            
            // إنشاء بيانات تجريبية
            if (!employees.find(emp => emp.id === 999)) {
                employees.push({
                    id: 999,
                    name: 'موظف اختبار الحفظ',
                    position: 'مطور',
                    employeeCode: 'SAVE999',
                    employmentType: 'monthly',
                    basicSalary: 5000
                });
                addLog('✓ تم إنشاء موظف تجريبي');
            }
            
            // إنشاء سجل أيام تجريبي
            const testRecord = {
                id: Date.now(),
                employeeId: 999,
                projectName: 'مشروع اختبار الحفظ',
                startDate: '2024-01-01',
                endDate: '2024-01-31',
                calculatedDays: 30,
                absenceDays: 2,
                overtimeHours: 10,
                createdAt: new Date().toISOString()
            };
            
            // إضافة السجل للبيانات
            if (!employeeDaysData.find(r => r.id === testRecord.id)) {
                employeeDaysData.push(testRecord);
                addLog('✓ تم إنشاء سجل أيام تجريبي');
            }
            
            // محاكاة التعديل
            try {
                addLog('🔄 محاكاة تعديل السجل...');
                
                // تعديل اسم المشروع
                if (typeof updateRecordField === 'function') {
                    updateRecordField(testRecord.id, 'projectName', 'مشروع اختبار الحفظ - معدل');
                    addLog('✓ تم تعديل اسم المشروع');
                } else {
                    addLog('✗ لا يمكن تعديل السجل - دالة updateRecordField مفقودة');
                    return;
                }
                
                // محاكاة الحفظ
                if (typeof saveAllRecordsChanges === 'function') {
                    // تجاوز تأكيد الحفظ للاختبار
                    const originalConfirm = window.confirm;
                    window.confirm = () => true;
                    
                    saveAllRecordsChanges(999);
                    addLog('✓ تم استدعاء دالة الحفظ');
                    
                    // استعادة confirm الأصلي
                    window.confirm = originalConfirm;
                    
                    // فحص البيانات المحفوظة
                    setTimeout(() => {
                        const savedData = localStorage.getItem('oscoEmployeeDaysData');
                        if (savedData) {
                            const parsedData = JSON.parse(savedData);
                            const savedRecord = parsedData.find(r => r.id === testRecord.id);
                            
                            if (savedRecord && savedRecord.projectName === 'مشروع اختبار الحفظ - معدل') {
                                updateDiagnosticItem(6, 'pass');
                                addLog('✅ البيانات حُفظت بنجاح في localStorage');
                                updateStatus('✅ نظام الحفظ يعمل بشكل صحيح!', 'success');
                            } else {
                                updateDiagnosticItem(6, 'fail');
                                addLog('❌ البيانات لم تحفظ بشكل صحيح');
                                updateStatus('❌ مشكلة في حفظ البيانات', 'error');
                            }
                        } else {
                            updateDiagnosticItem(6, 'fail');
                            addLog('❌ لا توجد بيانات محفوظة في localStorage');
                            updateStatus('❌ فشل في حفظ البيانات', 'error');
                        }
                    }, 1000);
                    
                } else {
                    addLog('✗ لا يمكن حفظ التغييرات - دالة saveAllRecordsChanges مفقودة');
                }
                
            } catch (error) {
                addLog('❌ خطأ في محاكاة التعديل: ' + error.message);
                updateStatus('❌ خطأ في نظام التعديل والحفظ', 'error');
            }
        }

        function runFullDiagnostic() {
            updateStatus('🔍 بدء التشخيص الشامل...', 'warning');
            addLog('=== بدء التشخيص الشامل لمشكلة عدم حفظ التغييرات ===');
            
            // إعادة تعيين جميع الفحوصات
            for (let i = 1; i <= 6; i++) {
                updateDiagnosticItem(i, 'pending');
            }
            
            // تشغيل الفحوصات بالتتابع
            setTimeout(() => {
                testSaveFunctions();
            }, 500);
            
            setTimeout(() => {
                simulateEditAndSave();
            }, 2000);
            
            setTimeout(() => {
                addLog('=== انتهاء التشخيص الشامل ===');
                
                // حساب النتيجة النهائية
                let passedTests = 0;
                for (let i = 1; i <= 6; i++) {
                    const icon = document.getElementById(`check${i}`);
                    if (icon && icon.classList.contains('pass')) {
                        passedTests++;
                    }
                }
                
                const percentage = (passedTests / 6) * 100;
                addLog(`📊 النتيجة النهائية: ${passedTests}/6 اختبار نجح (${percentage.toFixed(0)}%)`);
                
                if (percentage >= 100) {
                    updateStatus('🎉 جميع الاختبارات نجحت! نظام الحفظ يعمل بشكل مثالي', 'success');
                } else if (percentage >= 80) {
                    updateStatus(`⚠️ معظم الاختبارات نجحت (${percentage.toFixed(0)}%) - قد تكون هناك مشكلة بسيطة`, 'warning');
                } else {
                    updateStatus(`❌ فشل في ${6 - passedTests} اختبار - هناك مشكلة في نظام الحفظ`, 'error');
                }
            }, 4000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة التشخيص - جاهز للبدء', 'info');
            addLog('تم تحميل صفحة تشخيص مشكلة عدم حفظ التغييرات');
            
            // فحص سريع تلقائي
            setTimeout(() => {
                addLog('--- فحص سريع تلقائي ---');
                
                if (typeof saveAllRecordsChanges === 'function') {
                    addLog('✓ دالة الحفظ الرئيسية متاحة');
                } else {
                    addLog('✗ دالة الحفظ الرئيسية غير متاحة');
                }
                
                if (typeof employeeDaysData !== 'undefined') {
                    addLog(`✓ بيانات الأيام متاحة (${employeeDaysData.length} سجل)`);
                } else {
                    addLog('✗ بيانات الأيام غير متاحة');
                }
            }, 1000);
        });
    </script>
</body>
</html>
