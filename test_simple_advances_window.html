<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار نافذة السلف البسيطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        .feature-icon {
            position: absolute;
            top: -15px;
            right: 20px;
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }
        .big-button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        .big-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .big-button.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .result {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: center;
        }
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .old-design {
            background: #fff3cd;
            color: #856404;
        }
        .new-design {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-window-restore"></i> نافذة السلف البسيطة</h1>
            <p style="font-size: 1.2em; color: #666;">تصميم جديد بسيط وصغير مثل نافذة أيام الحضور</p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">✨</div>
            <h3>الميزات الجديدة:</h3>
            <ul style="text-align: right; margin: 10px 0;">
                <li><strong>حجم صغير:</strong> عرض 500px بدلاً من 800px</li>
                <li><strong>تصميم مدمج:</strong> الحقول في صفوف متجاورة</li>
                <li><strong>رأس أنيق:</strong> خلفية متدرجة زرقاء</li>
                <li><strong>جدول مختصر:</strong> أعمدة أصغر وخط أصغر</li>
                <li><strong>أزرار صغيرة:</strong> أيقونات بدلاً من نص طويل</li>
                <li><strong>معلومات مختصرة:</strong> سطر واحد لمعلومات الموظف</li>
            </ul>
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>الخاصية</th>
                    <th class="old-design">التصميم القديم</th>
                    <th class="new-design">التصميم الجديد</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>العرض</strong></td>
                    <td class="old-design">800px</td>
                    <td class="new-design">500px</td>
                </tr>
                <tr>
                    <td><strong>الحقول</strong></td>
                    <td class="old-design">3 صفوف منفصلة</td>
                    <td class="new-design">صفين مدمجين</td>
                </tr>
                <tr>
                    <td><strong>معلومات الموظف</strong></td>
                    <td class="old-design">مربع كبير</td>
                    <td class="new-design">سطر واحد مختصر</td>
                </tr>
                <tr>
                    <td><strong>الجدول</strong></td>
                    <td class="old-design">خط 14px</td>
                    <td class="new-design">خط 12px مدمج</td>
                </tr>
                <tr>
                    <td><strong>أزرار الحذف</strong></td>
                    <td class="old-design">نص "حذف"</td>
                    <td class="new-design">أيقونة 🗑️</td>
                </tr>
            </tbody>
        </table>

        <div style="text-align: center;">
            <button class="big-button success" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
            </button>
            
            <button class="big-button" onclick="openSimpleWindow()">
                <i class="fas fa-window-restore"></i> فتح النافذة البسيطة
            </button>
        </div>

        <div id="result" class="result"></div>

        <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>التحسينات المطبقة:</h3>
            <ul style="text-align: right; margin: 10px 0;">
                <li>تقليل حجم النافذة بنسبة 37.5%</li>
                <li>تجميع الحقول في صفوف لتوفير المساحة</li>
                <li>استخدام خطوط أصغر للمحتوى</li>
                <li>تبسيط معلومات الموظف</li>
                <li>استخدام أيقونات بدلاً من النصوص الطويلة</li>
                <li>تحسين التباعد والهوامش</li>
            </ul>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'exclamation-triangle'}"></i> ${message}`;
        }

        function createTestEmployee() {
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 12345,
                    name: 'أحمد محمد السلف',
                    position: 'محاسب',
                    employeeCode: 'ACC12345',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 12345);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء بعض سجلات السلف التجريبية
                const testAdvances = [
                    {
                        id: Date.now() + 1,
                        employeeId: 12345,
                        type: 'advance',
                        month: 11,
                        year: 2024,
                        amount: 1000,
                        description: 'سلفة نوفمبر',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 12345,
                        type: 'deduction',
                        month: 10,
                        year: 2024,
                        amount: 200,
                        description: 'خصم تأخير',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                if (typeof advancesDeductionsData !== 'undefined') {
                    advancesDeductionsData = advancesDeductionsData.filter(record => record.employeeId !== 12345);
                    testAdvances.forEach(record => {
                        advancesDeductionsData.push(record);
                    });
                } else {
                    window.advancesDeductionsData = testAdvances;
                }
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                    saveAdvancesDeductionsToLocalStorage();
                }
                
                showResult(`✅ تم إنشاء الموظف "${testEmployee.name}" مع ${testAdvances.length} سجل سلف/خصم`, 'success');
                
            } catch (error) {
                showResult('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openSimpleWindow() {
            try {
                if (!employees.find(emp => emp.id === 12345)) {
                    showResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // فتح النافذة البسيطة الجديدة
                viewAdvancesDeductions(12345);
                
                showResult('🎉 تم فتح نافذة السلف البسيطة! لاحظ الحجم الصغير والتصميم المدمج', 'success');
                
                // فحص النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        const modalContent = modal.querySelector('.modal-content');
                        if (modalContent) {
                            const width = modalContent.style.width;
                            showResult(`✅ النافذة مفتوحة بعرض ${width} - تصميم بسيط ومدمج!`, 'success');
                        }
                    }
                }, 1000);
                
            } catch (error) {
                showResult('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewAdvancesDeductions === 'function') {
                    showResult('✅ النظام جاهز! ابدأ بإنشاء الموظف التجريبي', 'success');
                } else {
                    showResult('❌ خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
