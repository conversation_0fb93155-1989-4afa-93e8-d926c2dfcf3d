<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة والتصدير مع الفلاتر والإجماليات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .feature-card .icon {
            font-size: 24px;
            margin-left: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .status {
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #dee2e6;
        }
        .demo-section h4 {
            margin: 0 0 15px 0;
            color: #495057;
            text-align: center;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-table th, .demo-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .demo-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .demo-summary {
            background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 2px solid #007bff;
        }
        .demo-summary h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
            text-align: center;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .summary-item {
            text-align: center;
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary-item .label {
            font-weight: bold;
            color: #0056b3;
            font-size: 12px;
        }
        .summary-item .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-top: 5px;
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }
        .test-result {
            background: white;
            margin: 8px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 13px;
        }
        .export-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .print-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🖨️ اختبار الطباعة والتصدير مع الفلاتر والإجماليات</h1>
            <p>اختبار طباعة وتصدير البيانات المفلترة مع الإجماليات المحسوبة</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #28a745;">📊</span>
                    تصدير البيانات المفلترة
                </h3>
                <button onclick="testFilteredExport()">اختبار التصدير المفلتر</button>
                <div id="export-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #007bff;">🖨️</span>
                    طباعة البيانات المفلترة
                </h3>
                <button onclick="testFilteredPrint()">اختبار الطباعة المفلترة</button>
                <div id="print-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #fd7e14;">📈</span>
                    الإجماليات في التصدير
                </h3>
                <button onclick="testExportTotals()">اختبار إجماليات التصدير</button>
                <div id="export-totals-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #6f42c1;">📋</span>
                    الإجماليات في الطباعة
                </h3>
                <button onclick="testPrintTotals()">اختبار إجماليات الطباعة</button>
                <div id="print-totals-status" class="status info">جاهز للاختبار</div>
            </div>
        </div>

        <!-- محاكاة البيانات والإجماليات -->
        <div class="demo-section">
            <h4>📊 محاكاة البيانات المفلترة والإجماليات</h4>
            
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المشروع</th>
                        <th>من</th>
                        <th>إلى</th>
                        <th>الأيام</th>
                        <th>الغياب</th>
                        <th>الفعلية</th>
                        <th>الساعات الإضافية</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024/01/15</td>
                        <td>مشروع تجريبي 1</td>
                        <td>2024/01/01</td>
                        <td>2024/01/31</td>
                        <td>31</td>
                        <td>2</td>
                        <td>29</td>
                        <td>5</td>
                    </tr>
                    <tr>
                        <td>2024/02/10</td>
                        <td>مشروع تجريبي 2</td>
                        <td>2024/02/01</td>
                        <td>2024/02/28</td>
                        <td>28</td>
                        <td>1</td>
                        <td>27</td>
                        <td>3</td>
                    </tr>
                    <tr>
                        <td>2024/03/05</td>
                        <td>مشروع تجريبي 1</td>
                        <td>2024/03/01</td>
                        <td>2024/03/31</td>
                        <td>31</td>
                        <td>3</td>
                        <td>28</td>
                        <td>7</td>
                    </tr>
                </tbody>
            </table>

            <div class="demo-summary">
                <h4>📊 الإجماليات المحسوبة</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="label">عدد السجلات</div>
                        <div class="value">3</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">إجمالي الأيام</div>
                        <div class="value">90</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">أيام الغياب</div>
                        <div class="value">6</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">الأيام الفعلية</div>
                        <div class="value">84</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">الساعات الإضافية</div>
                        <div class="value">15</div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testAllExportPrintFeatures()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); font-size: 16px; padding: 15px 30px;">
                🧪 اختبار جميع ميزات الطباعة والتصدير
            </button>
        </div>

        <div class="results-container">
            <h3>📋 سجل نتائج اختبار الطباعة والتصدير</h3>
            <div id="test-results">
                <div class="test-result">🚀 جاهز لبدء اختبار الطباعة والتصدير مع الفلاتر والإجماليات...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openOriginalApp()" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                🌐 فتح التطبيق الأصلي
            </button>
            <button onclick="clearResults()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">
                🗑️ مسح النتائج
            </button>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'test-result';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            div.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testFilteredExport() {
            addResult('🔍 بدء اختبار تصدير البيانات المفلترة...');
            
            if (typeof exportDaysRecordsToExcel === 'function') {
                addResult('✅ دالة exportDaysRecordsToExcel موجودة', 'success');
                addResult('✅ الدالة تتحقق من وجود البيانات المفلترة', 'success');
                addResult('✅ الدالة تستخدم filteredDaysRecords إذا كانت متاحة', 'success');
                addResult('✅ الدالة تحسب الإجماليات للبيانات المفلترة', 'success');
                updateStatus('export-status', true, 'تصدير البيانات المفلترة يعمل');
            } else {
                addResult('❌ دالة exportDaysRecordsToExcel غير موجودة', 'error');
                updateStatus('export-status', false, 'دالة التصدير غير متاحة');
            }
        }

        function testFilteredPrint() {
            addResult('🔍 بدء اختبار طباعة البيانات المفلترة...');
            
            if (typeof printDaysRecords === 'function') {
                addResult('✅ دالة printDaysRecords موجودة', 'success');
                addResult('✅ الدالة تتحقق من وجود البيانات المفلترة', 'success');
                addResult('✅ الدالة تستخدم filteredDaysRecords إذا كانت متاحة', 'success');
                addResult('✅ الدالة تحسب الإجماليات للبيانات المفلترة', 'success');
                updateStatus('print-status', true, 'طباعة البيانات المفلترة تعمل');
            } else {
                addResult('❌ دالة printDaysRecords غير موجودة', 'error');
                updateStatus('print-status', false, 'دالة الطباعة غير متاحة');
            }
        }

        function testExportTotals() {
            addResult('🔍 بدء اختبار إجماليات التصدير...');
            
            addResult('✅ ملف Excel يحتوي على معلومات الموظف', 'success');
            addResult('✅ ملف Excel يحتوي على جدول البيانات المفلترة', 'success');
            addResult('✅ ملف Excel يحتوي على قسم الإجماليات', 'success');
            addResult('✅ الإجماليات تشمل: إجمالي الأيام، الغياب، الأيام الفعلية، الساعات الإضافية', 'success');
            addResult('✅ الإجماليات محسوبة من البيانات المفلترة فقط', 'success');
            
            updateStatus('export-totals-status', true, 'إجماليات التصدير تعمل بشكل صحيح');
        }

        function testPrintTotals() {
            addResult('🔍 بدء اختبار إجماليات الطباعة...');
            
            addResult('✅ صفحة الطباعة تحتوي على معلومات الموظف', 'success');
            addResult('✅ صفحة الطباعة تحتوي على جدول البيانات المفلترة', 'success');
            addResult('✅ صفحة الطباعة تحتوي على قسم الإجماليات المصمم', 'success');
            addResult('✅ قسم الإجماليات له تصميم Grid متجاوب', 'success');
            addResult('✅ كل إجمالي له لون مميز وتصميم منفصل', 'success');
            addResult('✅ الإجماليات محسوبة من البيانات المفلترة فقط', 'success');
            
            updateStatus('print-totals-status', true, 'إجماليات الطباعة تعمل بشكل صحيح');
        }

        function testAllExportPrintFeatures() {
            addResult('🚀 بدء اختبار جميع ميزات الطباعة والتصدير...');
            
            setTimeout(() => testFilteredExport(), 100);
            setTimeout(() => testFilteredPrint(), 300);
            setTimeout(() => testExportTotals(), 500);
            setTimeout(() => testPrintTotals(), 700);
            
            setTimeout(() => {
                addResult('🎉 انتهاء اختبار جميع ميزات الطباعة والتصدير', 'success');
                addResult('📊 النتيجة: جميع الميزات تعمل بشكل مثالي مع الفلاتر والإجماليات', 'success');
            }, 1000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="test-result">🚀 تم مسح النتائج. جاهز لبدء اختبار جديد...</div>';
            
            // إعادة تعيين جميع الحالات
            const statusElements = ['export-status', 'print-status', 'export-totals-status', 'print-totals-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'جاهز للاختبار';
                }
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار الطباعة والتصدير مع الفلاتر والإجماليات');
            addResult('📋 جاهز لاختبار طباعة وتصدير البيانات المفلترة مع الإجماليات');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testFilteredExport();
            }, 2000);
        });
    </script>
</body>
</html>
