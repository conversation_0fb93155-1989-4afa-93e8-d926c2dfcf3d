<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - نافذة الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            color: #28a745;
        }

        .result-item.error {
            color: #dc3545;
        }

        .result-item.warning {
            color: #ffc107;
        }

        .result-item.info {
            color: #17a2b8;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-trophy"></i> اختبار نافذة الحوافز والمكافآت</h1>
            <p>اختبار بسيط وسريع لفتح نافذة الحوافز والمكافآت</p>
        </div>

        <div class="status" id="status">
            جاهز للاختبار...
        </div>

        <div class="content">
            <div style="text-align: center; margin-bottom: 30px;">
                <button class="test-button" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                
                <button class="test-button success" onclick="testIncentivesWindow()">
                    <i class="fas fa-trophy"></i> اختبار نافذة الحوافز
                </button>
                
                <button class="test-button danger" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            const timestamp = new Date().toLocaleTimeString();
            resultItem.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('🔄 بدء إنشاء موظف تجريبي', 'info');

            try {
                // التحقق من تحميل النظام
                if (typeof employees === 'undefined') {
                    addResult('❌ النظام غير محمل - متغير employees مفقود', 'error');
                    updateStatus('❌ فشل - النظام غير محمل', 'error');
                    return;
                }

                const testEmployee = {
                    id: 88888,
                    name: "موظف تجريبي للحوافز",
                    position: "مختبر النظام",
                    employeeCode: "INCENTIVE_TEST",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/28a745/ffffff?text=TEST",
                    employmentType: "monthly",
                    salary: {
                        basic: 5000,
                        total: 5000
                    },
                    dateAdded: new Date().toISOString()
                };

                // التحقق من وجود الموظف
                const existingEmployee = employees.find(emp => emp.id === 88888);
                if (existingEmployee) {
                    addResult('⚠️ الموظف التجريبي موجود بالفعل', 'warning');
                    updateStatus('✅ الموظف التجريبي جاهز', 'success');
                } else {
                    employees.push(testEmployee);
                    
                    // حفظ البيانات
                    if (typeof saveEmployeesToLocalStorage === 'function') {
                        saveEmployeesToLocalStorage();
                        addResult('✅ تم حفظ الموظف التجريبي', 'success');
                    }
                    
                    // تحديث العرض
                    if (typeof populateEmployees === 'function') {
                        populateEmployees();
                        addResult('🔄 تم تحديث قائمة الموظفين', 'info');
                    }
                    
                    addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    updateStatus('✅ تم إنشاء الموظف التجريبي', 'success');
                }

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف', 'error');
            }
        }

        // اختبار نافذة الحوافز والمكافآت
        function testIncentivesWindow() {
            updateStatus('جاري اختبار نافذة الحوافز...', 'warning');
            addResult('🪟 بدء اختبار فتح نافذة الحوافز والمكافآت', 'info');

            try {
                // التحقق من وجود الموظف التجريبي
                const testEmployee = employees.find(emp => emp.id === 88888);
                if (!testEmployee) {
                    addResult('❌ الموظف التجريبي غير موجود', 'error');
                    addResult('💡 يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    updateStatus('❌ فشل - الموظف غير موجود', 'error');
                    return;
                }

                addResult('✅ تم العثور على الموظف التجريبي', 'success');

                // التحقق من وجود الدالة
                if (typeof viewIncentivesRewards !== 'function') {
                    addResult('❌ دالة viewIncentivesRewards غير موجودة', 'error');
                    updateStatus('❌ فشل - الدالة مفقودة', 'error');
                    return;
                }

                addResult('✅ دالة viewIncentivesRewards موجودة', 'success');

                // محاولة فتح النافذة
                addResult('🔄 محاولة فتح النافذة...', 'info');
                viewIncentivesRewards(88888);
                addResult('✅ تم استدعاء دالة فتح النافذة', 'success');

                // التحقق من وجود النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('incentivesRewardsModal');
                    if (modal) {
                        addResult('🎉 النافذة مفتوحة ومرئية!', 'success');
                        addResult('👀 يمكنك الآن رؤية نافذة الحوافز والمكافآت', 'info');
                        updateStatus('✅ نجح فتح النافذة!', 'success');
                    } else {
                        addResult('❌ النافذة لم تظهر في الصفحة', 'error');
                        updateStatus('❌ فشل في فتح النافذة', 'error');
                    }
                }, 500);

            } catch (error) {
                addResult(`❌ خطأ في اختبار النافذة: ${error.message}`, 'error');
                updateStatus('❌ حدث خطأ في الاختبار', 'error');
                console.error('خطأ في اختبار النافذة:', error);
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🔍 بدء الفحص التلقائي...', 'info');
                
                // فحص تحميل النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }

                // فحص دالة الحوافز
                if (typeof viewIncentivesRewards === 'function') {
                    addResult('✅ دالة الحوافز موجودة', 'success');
                } else {
                    addResult('❌ دالة الحوافز مفقودة', 'error');
                }

                // فحص دالة getMonthName
                if (typeof getMonthName === 'function') {
                    addResult('✅ دالة getMonthName موجودة', 'success');
                } else {
                    addResult('❌ دالة getMonthName مفقودة', 'error');
                }

                addResult('✅ انتهى الفحص التلقائي', 'success');
                updateStatus('✅ النظام جاهز للاختبار', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
