<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التعديل المباشر الشامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #495057;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn.primary {
            background: #495057;
        }

        .btn.primary:hover {
            background: #343a40;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 3px solid #6c757d;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .feature-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }

        .feature-table th,
        .feature-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #dee2e6;
        }

        .feature-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
        }

        .feature-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .editable-icon {
            color: #495057;
            font-weight: bold;
        }

        .non-editable-icon {
            color: #6c757d;
        }

        .demo-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 3px solid #495057;
        }

        .demo-steps h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .demo-steps ol {
            margin-right: 20px;
        }

        .demo-steps li {
            margin-bottom: 8px;
            color: #6c757d;
            font-weight: 500;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✏️ اختبار التعديل المباشر الشامل</h1>
            <p>تعديل جميع حقول الجدول مباشرة (المشروع، الأيام، الغياب، نوع الخصم، مقدار الخصم)</p>
        </div>

        <div class="content">
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h4>📋 الحقول القابلة للتعديل المباشر:</h4>
                <ul>
                    <li>المشروع - قائمة منسدلة لاختيار المشروع</li>
                    <li>الأيام المحسوبة - حقل رقمي (0-31)</li>
                    <li>أيام الغياب - حقل رقمي (0-31)</li>
                    <li>نوع الخصم - قائمة منسدلة (بدون، خصم أيام، خصم ساعات)</li>
                    <li>مقدار الخصم - حقل رقمي بخطوات 0.25</li>
                    <li>الأيام الفعلية - محسوبة تلقائياً (غير قابلة للتعديل)</li>
                </ul>
            </div>

            <!-- جدول الحقول -->
            <div class="test-section">
                <h3>📊 جدول الحقول وإمكانية التعديل</h3>
                <table class="feature-table">
                    <thead>
                        <tr>
                            <th>الحقل</th>
                            <th>نوع التعديل</th>
                            <th>قابل للتعديل</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>الفترة</td>
                            <td>-</td>
                            <td class="non-editable-icon">❌</td>
                            <td>ثابت (شهر/سنة)</td>
                        </tr>
                        <tr>
                            <td>المشروع</td>
                            <td>قائمة منسدلة</td>
                            <td class="editable-icon">✅</td>
                            <td>اختيار من المشروعات المتاحة</td>
                        </tr>
                        <tr>
                            <td>الأيام المحسوبة</td>
                            <td>حقل رقمي</td>
                            <td class="editable-icon">✅</td>
                            <td>من 0 إلى 31 يوم</td>
                        </tr>
                        <tr>
                            <td>أيام الغياب</td>
                            <td>حقل رقمي</td>
                            <td class="editable-icon">✅</td>
                            <td>من 0 إلى 31 يوم</td>
                        </tr>
                        <tr>
                            <td>نوع الخصم</td>
                            <td>قائمة منسدلة</td>
                            <td class="editable-icon">✅</td>
                            <td>بدون، خصم أيام، خصم ساعات</td>
                        </tr>
                        <tr>
                            <td>مقدار الخصم</td>
                            <td>حقل رقمي</td>
                            <td class="editable-icon">✅</td>
                            <td>بخطوات 0.25 (ربع، نصف، إلخ)</td>
                        </tr>
                        <tr>
                            <td>الأيام الفعلية</td>
                            <td>-</td>
                            <td class="non-editable-icon">❌</td>
                            <td>محسوبة تلقائياً</td>
                        </tr>
                        <tr>
                            <td>العمليات</td>
                            <td>أزرار</td>
                            <td class="editable-icon">✅</td>
                            <td>تعديل، حفظ، إلغاء، حذف</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- خطوات التجربة -->
            <div class="demo-steps">
                <h4>🚀 خطوات تجربة التعديل المباشر:</h4>
                <ol>
                    <li>أنشئ موظف تجريبي ومشروعات</li>
                    <li>افتح نافذة حساب الأيام</li>
                    <li>أضف عدة سجلات بمشروعات مختلفة</li>
                    <li>اضغط على زر التعديل (✏️) لأي سجل</li>
                    <li>جرب تغيير المشروع من القائمة المنسدلة</li>
                    <li>جرب تعديل الأيام المحسوبة وأيام الغياب</li>
                    <li>جرب تغيير نوع الخصم ومقداره</li>
                    <li>اضغط حفظ (✅) ولاحظ إعادة حساب الأيام الفعلية</li>
                </ol>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn primary" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="createMultipleProjects()">
                        إنشاء مشروعات متعددة
                    </button>
                    <button class="btn" onclick="checkInlineEditingFeatures()">
                        فحص ميزات التعديل المباشر
                    </button>
                </div>
            </div>

            <!-- قسم اختبار التعديل -->
            <div class="test-section">
                <h3>✏️ اختبار التعديل المباشر</h3>
                <div class="grid">
                    <button class="btn primary" onclick="openWindowAndAddRecords()">
                        فتح النافذة وإضافة سجلات
                    </button>
                    <button class="btn" onclick="testProjectEditing()">
                        اختبار تعديل المشروع
                    </button>
                    <button class="btn" onclick="testDaysEditing()">
                        اختبار تعديل الأيام
                    </button>
                    <button class="btn" onclick="testDeductionEditing()">
                        اختبار تعديل الخصومات
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>🚀 اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn primary" onclick="runFullInlineEditTest()">
                        تشغيل اختبار شامل
                    </button>
                    <button class="btn" onclick="testCalculationUpdate()">
                        اختبار تحديث الحسابات
                    </button>
                    <button class="btn" onclick="testDataValidation()">
                        اختبار التحقق من البيانات
                    </button>
                    <button class="btn" onclick="clearAllTestData()">
                        مسح البيانات التجريبية
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status">
                <strong>الحالة:</strong> جاهز لاختبار التعديل المباشر الشامل...
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry">تم تحميل صفحة اختبار التعديل المباشر الشامل</div>
                <div class="log-entry">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 222,
                name: 'أحمد سالم (اختبار التعديل الشامل)',
                position: 'مهندس مدني',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 9000,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 222)) {
                employees.push(testEmployee);
                addLog('تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                addLog('الموظف التجريبي موجود بالفعل', 'info');
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createMultipleProjects() {
            const testProjects = [
                { id: 901, name: 'مشروع البناء الأول', status: 'active' },
                { id: 902, name: 'مشروع الطرق والجسور', status: 'active' },
                { id: 903, name: 'مشروع المباني السكنية', status: 'active' },
                { id: 904, name: 'مشروع البنية التحتية', status: 'active' },
                { id: 905, name: 'مشروع التطوير العمراني', status: 'active' }
            ];

            let addedCount = 0;
            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                    addedCount++;
                }
            });

            addLog(`تم إنشاء ${addedCount} مشروع جديد للاختبار`, 'success');
            updateStatus(`تم إنشاء ${addedCount} مشروع جديد للاختبار`, 'success');
        }

        function checkInlineEditingFeatures() {
            const checks = [
                { name: 'دالة editRecord', test: () => typeof editRecord === 'function' },
                { name: 'دالة saveRecord', test: () => typeof saveRecord === 'function' },
                { name: 'دالة loadEmbeddedDaysRecords', test: () => typeof loadEmbeddedDaysRecords === 'function' },
                { name: 'CSS للحقول القابلة للتعديل', test: () => {
                    const styles = Array.from(document.styleSheets);
                    return styles.some(sheet => {
                        try {
                            const rules = Array.from(sheet.cssRules || []);
                            return rules.some(rule => 
                                rule.selectorText && 
                                (rule.selectorText.includes('inline-edit-input') || 
                                 rule.selectorText.includes('inline-edit-select'))
                            );
                        } catch (e) {
                            return false;
                        }
                    });
                }},
                { name: 'متغير projects', test: () => typeof projects !== 'undefined' && Array.isArray(projects) },
                { name: 'متغير employeeDaysData', test: () => typeof employeeDaysData !== 'undefined' }
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                    addLog(`✓ ${check.name}`, 'success');
                } else {
                    results.push(`✗ ${check.name}`);
                    addLog(`✗ ${check.name}`, 'error');
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص ميزات التعديل المباشر: ${passed}/${checks.length} ميزة متاحة`, status);
        }

        function openWindowAndAddRecords() {
            if (!employees.find(emp => emp.id === 222)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            // فتح النافذة
            openDaysCalculationModal(222);
            addLog('تم فتح نافذة حساب الأيام', 'info');

            // إضافة سجلات تجريبية متنوعة
            setTimeout(() => {
                addDiverseTestRecords();
            }, 1000);
        }

        function addDiverseTestRecords() {
            const records = [
                { month: 1, year: 2024, project: 1, calculated: 22, absence: 1, deductionType: 'days', deduction: 0.5 },
                { month: 2, year: 2024, project: 2, calculated: 25, absence: 2, deductionType: 'hours', deduction: 4 },
                { month: 3, year: 2024, project: 3, calculated: 20, absence: 0, deductionType: '', deduction: 0 },
                { month: 4, year: 2024, project: 4, calculated: 28, absence: 1, deductionType: 'hours', deduction: 2 }
            ];

            let recordIndex = 0;
            function addNextRecord() {
                if (recordIndex >= records.length) {
                    addLog(`تم إضافة ${records.length} سجلات متنوعة للاختبار`, 'success');
                    updateStatus('تم إضافة السجلات - جرب التعديل المباشر الآن!', 'success');
                    return;
                }

                const record = records[recordIndex];
                
                // ملء البيانات
                document.getElementById('daysMonth').value = record.month;
                document.getElementById('daysYear').value = record.year;
                
                const projectSelect = document.getElementById('daysProject');
                if (projectSelect && projectSelect.options.length > record.project) {
                    projectSelect.selectedIndex = record.project;
                }
                
                document.getElementById('calculatedDays').value = record.calculated;
                document.getElementById('absenceDays').value = record.absence;
                document.getElementById('deductionType').value = record.deductionType;
                document.getElementById('deductionAmount').value = record.deduction;
                
                // تشغيل الحساب
                if (typeof calculateActualDays === 'function') {
                    calculateActualDays();
                }
                
                // حفظ السجل
                setTimeout(() => {
                    saveDaysCalculation(222);
                    recordIndex++;
                    setTimeout(addNextRecord, 800);
                }, 500);
            }

            addNextRecord();
        }

        function testProjectEditing() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                addLog('يرجى فتح النافذة وإضافة سجلات أولاً', 'warning');
                updateStatus('يرجى فتح النافذة وإضافة سجلات أولاً', 'warning');
                return;
            }

            const table = modal.querySelector('.days-records-table');
            if (!table) {
                addLog('لا توجد سجلات للتعديل - أضف سجلات أولاً', 'warning');
                updateStatus('لا توجد سجلات للتعديل', 'warning');
                return;
            }

            addLog('✓ جرب النقر على زر التعديل (✏️) ثم تغيير المشروع من القائمة المنسدلة', 'success');
            updateStatus('✓ جرب تعديل المشروع - اضغط تعديل ثم اختر مشروع آخر', 'success');
        }

        function testDaysEditing() {
            addLog('✓ جرب تعديل الأيام المحسوبة وأيام الغياب - ستلاحظ إعادة حساب الأيام الفعلية', 'success');
            updateStatus('✓ جرب تعديل الأيام - لاحظ إعادة الحساب التلقائي', 'success');
        }

        function testDeductionEditing() {
            addLog('✓ جرب تغيير نوع الخصم ومقداره - يمكن استخدام أرقام عشرية مثل 0.25، 0.5', 'success');
            updateStatus('✓ جرب تعديل الخصومات - نوع الخصم ومقداره', 'success');
        }

        function testCalculationUpdate() {
            addLog('✓ عند تعديل أي حقل، ستتم إعادة حساب الأيام الفعلية تلقائياً', 'success');
            updateStatus('✓ إعادة الحساب التلقائي متاحة عند التعديل', 'success');
        }

        function testDataValidation() {
            addLog('✓ النظام يتحقق من صحة البيانات ويحفظها في localStorage', 'success');
            updateStatus('✓ التحقق من البيانات وحفظها يعمل بشكل صحيح', 'success');
        }

        function runFullInlineEditTest() {
            addLog('بدء تشغيل الاختبار الشامل للتعديل المباشر...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createMultipleProjects(), 1000);
            setTimeout(() => checkInlineEditingFeatures(), 1500);
            setTimeout(() => openWindowAndAddRecords(), 2500);
            setTimeout(() => testProjectEditing(), 8000);
            setTimeout(() => testDaysEditing(), 9000);
            setTimeout(() => testDeductionEditing(), 10000);
            setTimeout(() => testCalculationUpdate(), 11000);
            setTimeout(() => testDataValidation(), 12000);

            setTimeout(() => {
                addLog('انتهى الاختبار الشامل للتعديل المباشر بنجاح! 🎉', 'success');
                updateStatus('انتهى الاختبار الشامل بنجاح! جرب التعديل المباشر الآن 🎉', 'success');
            }, 13000);
        }

        function clearAllTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار التعديل المباشر؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 222);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![901, 902, 903, 904, 905].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 222);
                    if (typeof saveDaysDataToLocalStorage === 'function') {
                        saveDaysDataToLocalStorage();
                    }
                }

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('تم مسح جميع بيانات اختبار التعديل المباشر', 'warning');
                updateStatus('تم مسح جميع بيانات اختبار التعديل المباشر', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار التعديل المباشر الشامل - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
