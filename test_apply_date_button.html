<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر تطبيق التاريخ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .bulk-activation {
            display: flex !important;
            align-items: center;
            gap: 10px;
            visibility: visible !important;
            opacity: 1 !important;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        /* أنماط checkbox تنشيط الكل - تصميم مصغر وبسيط */
        .activate-all-container {
            display: flex !important;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            user-select: none;
            font-size: 13px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.2s ease;
            padding: 6px 10px;
            border-radius: 20px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            visibility: visible !important;
            opacity: 1 !important;
            min-width: fit-content;
        }

        .activate-all-container:hover {
            color: #28a745;
            background: #e8f5e8;
            border-color: #c3e6cb;
        }

        .activate-all-container input[type="checkbox"] {
            width: 14px;
            height: 14px;
            cursor: pointer;
            accent-color: #28a745;
            margin: 0;
        }

        .activate-all-container i {
            font-size: 12px;
            color: inherit;
            transition: color 0.2s ease;
        }

        .activate-all-text {
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
        }

        /* أنماط زر تطبيق التاريخ على الكل */
        .apply-date-all-btn {
            display: flex !important;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            user-select: none;
            font-size: 13px;
            font-weight: 600;
            color: white !important;
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            border: 2px solid #0056b3 !important;
            visibility: visible !important;
            opacity: 1 !important;
            min-width: fit-content;
            box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
        }

        .apply-date-all-btn:hover {
            background: linear-gradient(135deg, #0056b3, #004085) !important;
            border-color: #004085 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
        }

        .apply-date-all-btn i {
            font-size: 12px;
            color: white !important;
        }

        .apply-date-all-btn span {
            font-size: 13px;
            font-weight: 600;
            white-space: nowrap;
            color: white !important;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار زر تطبيق التاريخ على الكل</h1>
        
        <div id="status" class="status info">جاري التحميل...</div>
        
        <!-- محاكاة رأس قائمة الموظفين -->
        <div class="employees-header">
            <h3>قائمة الموظفين</h3>
            <div class="bulk-activation">
                <label class="activate-all-container">
                    <input type="checkbox" id="activateAllEmployees" onchange="testToggleAll()">
                    <i class="fas fa-check-circle"></i>
                    <span class="activate-all-text">تنشيط الكل</span>
                </label>
                
                <!-- زر تطبيق التاريخ على الكل -->
                <button class="apply-date-all-btn" id="applyDateAllBtn" onclick="testApplyDate()">
                    <i class="fas fa-calendar-check"></i>
                    <span>تطبيق التاريخ على الكل</span>
                </button>
            </div>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>اختبارات:</h3>
            <button onclick="testButtonExists()" style="margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                فحص وجود الزر
            </button>
            <button onclick="testButtonFunction()" style="margin: 5px; padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                اختبار الدالة
            </button>
            <button onclick="testStyles()" style="margin: 5px; padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 5px; cursor: pointer;">
                فحص الأنماط
            </button>
        </div>
        
        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        function testToggleAll() {
            const checkbox = document.getElementById('activateAllEmployees');
            const isChecked = checkbox.checked;
            
            console.log('تم تغيير حالة Checkbox:', isChecked);
            addResult(`تم ${isChecked ? 'تفعيل' : 'إلغاء تفعيل'} Checkbox`, 'success');
        }

        function testApplyDate() {
            console.log('تم النقر على زر تطبيق التاريخ');
            addResult('✅ تم النقر على زر تطبيق التاريخ على الكل!', 'success');
            alert('تم النقر على زر تطبيق التاريخ على الكل!');
        }

        function testButtonExists() {
            const button = document.getElementById('applyDateAllBtn');
            if (button) {
                addResult('✅ زر تطبيق التاريخ موجود ويعمل', 'success');
                console.log('Button element:', button);
            } else {
                addResult('❌ زر تطبيق التاريخ غير موجود', 'error');
            }
        }

        function testButtonFunction() {
            const button = document.getElementById('applyDateAllBtn');
            if (button) {
                // تشغيل الدالة برمجياً
                button.click();
                addResult('✅ تم تشغيل الدالة برمجياً', 'success');
            } else {
                addResult('❌ لا يمكن اختبار الدالة - الزر غير موجود', 'error');
            }
        }

        function testStyles() {
            const button = document.getElementById('applyDateAllBtn');
            if (button) {
                const styles = window.getComputedStyle(button);
                addResult(`✅ الأنماط مطبقة - اللون: ${styles.backgroundColor}`, 'success');
                console.log('Button styles:', styles);
            } else {
                addResult('❌ لا يمكن العثور على الزر', 'error');
            }
        }

        // تشغيل اختبار أولي
        window.addEventListener('load', function() {
            showStatus('✅ تم تحميل الصفحة بنجاح', 'success');
            testButtonExists();
        });
    </script>
</body>
</html>
