<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البدلات اليومية - الإصلاح النهائي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .result.warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار البدلات اليومية - الإصلاح النهائي</h1>
        <p>هذه الصفحة لاختبار أن البدلات اليومية تظهر بشكل صحيح في نافذة حساب الراتب</p>

        <div class="test-section">
            <h3>🔍 اختبارات سريعة</h3>
            <button class="test-button" onclick="testBasicFunctions()">فحص الدوال الأساسية</button>
            <button class="test-button" onclick="createTestEmployee()">إنشاء موظف تجريبي</button>
            <button class="test-button" onclick="addDailyAllowances()">إضافة البدلات اليومية</button>
            <button class="test-button" onclick="testSalaryCalculation()">اختبار حساب الراتب</button>
            <button class="test-button" onclick="clearResults()">مسح النتائج</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- تحميل ملف التطبيق الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // فحص الدوال الأساسية
        function testBasicFunctions() {
            addResult('🔍 بدء فحص الدوال الأساسية...', 'info');

            const requiredFunctions = [
                'openSalaryCalculator',
                'createSalaryCalculatorModal',
                'addDailyAllowancesToAllEmployees',
                'closeSalaryCalculatorModal'
            ];

            let passed = 0;
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    passed++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            // فحص المتغيرات
            if (typeof employees !== 'undefined') {
                addResult(`✅ متغير employees موجود (${employees.length} موظف)`, 'success');
            } else {
                addResult('❌ متغير employees مفقود', 'error');
            }

            addResult(`📊 نتيجة الفحص: ${passed}/${requiredFunctions.length} دالة متاحة`, 
                passed === requiredFunctions.length ? 'success' : 'warning');
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('👤 إنشاء موظف تجريبي...', 'info');

            try {
                // التأكد من وجود مصفوفة الموظفين
                if (typeof employees === 'undefined') {
                    window.employees = [];
                }

                // حذف الموظف التجريبي إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 99999);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    addResult('🗑️ تم حذف الموظف التجريبي السابق', 'info');
                }

                // إنشاء موظف تجريبي جديد
                const testEmployee = {
                    id: 99999,
                    name: 'موظف تجريبي للبدلات',
                    employeeCode: 'TEST-99999',
                    position: 'مطور',
                    employmentType: 'monthly',
                    basicSalary: 10000,
                    salary: {
                        basic: 10000,
                        total: 10000,
                        dailyAllowances: {
                            expatDaily: 75,      // بدل اغتراب يومي
                            transportDaily: 45   // بدل انتقالات يومي
                        }
                    },
                    active: true,
                    createdAt: new Date().toISOString()
                };

                employees.push(testEmployee);

                // حفظ البيانات
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }

                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                addResult(`💰 الراتب الأساسي: ${testEmployee.salary.basic} ج.م`, 'info');
                addResult(`🌍 بدل الاغتراب اليومي: ${testEmployee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                addResult(`🚗 بدل الانتقالات اليومي: ${testEmployee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف التجريبي: ${error.message}`, 'error');
            }
        }

        // إضافة البدلات اليومية لجميع الموظفين
        function addDailyAllowances() {
            addResult('🔧 إضافة البدلات اليومية لجميع الموظفين...', 'info');

            try {
                if (typeof addDailyAllowancesToAllEmployees === 'function') {
                    const updatedCount = addDailyAllowancesToAllEmployees();
                    addResult(`✅ تم تحديث ${updatedCount} موظف بالبدلات اليومية`, 'success');
                } else {
                    addResult('❌ دالة addDailyAllowancesToAllEmployees غير موجودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة البدلات: ${error.message}`, 'error');
            }
        }

        // اختبار حساب الراتب
        function testSalaryCalculation() {
            addResult('💰 اختبار حساب الراتب مع البدلات اليومية...', 'info');

            try {
                // التحقق من وجود الموظف التجريبي
                const testEmployee = employees.find(emp => emp.id === 99999);
                if (!testEmployee) {
                    addResult('❌ الموظف التجريبي غير موجود، يرجى إنشاؤه أولاً', 'error');
                    return;
                }

                // إضافة بيانات أيام عمل تجريبية
                if (typeof employeeDaysData === 'undefined') {
                    window.employeeDaysData = [];
                }

                // حذف البيانات السابقة للموظف التجريبي
                window.employeeDaysData = employeeDaysData.filter(record => 
                    parseInt(record.employeeId) !== 99999
                );

                // إضافة بيانات أيام عمل جديدة
                const workDaysRecord = {
                    id: Date.now(),
                    employeeId: 99999,
                    month: new Date().getMonth() + 1,
                    year: new Date().getFullYear(),
                    calculatedDays: 22,
                    absenceDays: 2,
                    actualDays: 20,
                    overtimeHours: 15,
                    project: 'مشروع تجريبي',
                    createdAt: new Date().toISOString()
                };

                employeeDaysData.push(workDaysRecord);

                addResult('✅ تم إضافة بيانات أيام العمل التجريبية', 'success');
                addResult(`📊 أيام العمل الفعلية: ${workDaysRecord.actualDays} يوم`, 'info');

                // حساب البدلات المتوقعة
                const expatAllowance = testEmployee.salary.dailyAllowances.expatDaily * workDaysRecord.actualDays;
                const transportAllowance = testEmployee.salary.dailyAllowances.transportDaily * workDaysRecord.actualDays;
                const totalDailyAllowances = expatAllowance + transportAllowance;

                addResult(`🌍 بدل الاغتراب المتوقع: ${expatAllowance} ج.م`, 'info');
                addResult(`🚗 بدل الانتقالات المتوقع: ${transportAllowance} ج.م`, 'info');
                addResult(`💰 إجمالي البدلات اليومية: ${totalDailyAllowances} ج.م`, 'success');

                // فتح نافذة حساب الراتب
                addResult('🖥️ فتح نافذة حساب الراتب...', 'info');
                openSalaryCalculator(99999);

                // التحقق من النافذة بعد ثانية
                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (modal) {
                        addResult('✅ تم فتح نافذة حساب الراتب بنجاح', 'success');
                        
                        // البحث عن البدلات اليومية في النافذة
                        const modalContent = modal.innerHTML;
                        if (modalContent.includes('البدلات اليومية')) {
                            addResult('✅ البدلات اليومية موجودة في النافذة!', 'success');
                        } else {
                            addResult('❌ البدلات اليومية غير موجودة في النافذة', 'error');
                        }

                        if (modalContent.includes('بدل الاغتراب')) {
                            addResult('✅ بدل الاغتراب موجود في النافذة', 'success');
                        } else {
                            addResult('❌ بدل الاغتراب غير موجود في النافذة', 'error');
                        }

                        if (modalContent.includes('بدل الانتقالات')) {
                            addResult('✅ بدل الانتقالات موجود في النافذة', 'success');
                        } else {
                            addResult('❌ بدل الانتقالات غير موجود في النافذة', 'error');
                        }

                        addResult('🎉 اختبار البدلات اليومية مكتمل!', 'success');
                        
                    } else {
                        addResult('❌ فشل في فتح نافذة حساب الراتب', 'error');
                    }
                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في اختبار حساب الراتب: ${error.message}`, 'error');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            addResult('تم مسح النتائج', 'info');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار البدلات اليومية', 'info');
            addResult('جاهز لبدء الاختبارات...', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع ---', 'info');
                testBasicFunctions();
            }, 500);
        });
    </script>
</body>
</html>
