<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر تعديل الموظف</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #17a2b8;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .test-button {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }

        .result-item.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .result-item.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .result-item.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }

        .employee-demo {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }

        .demo-button {
            padding: 8px 15px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }

        .demo-button:hover {
            background: #138496;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> اختبار زر تعديل الموظف</h1>
            <p>اختبار شامل لوظيفة تعديل بيانات الموظفين</p>
        </div>

        <div class="content">
            <div class="test-grid">
                <div class="test-card">
                    <h3><i class="fas fa-cogs"></i> اختبارات النظام الأساسي</h3>
                    <button class="test-button" onclick="testSystemLoad()">
                        <i class="fas fa-power-off"></i> اختبار تحميل النظام
                    </button>
                    <button class="test-button" onclick="testEditFunctions()">
                        <i class="fas fa-code"></i> اختبار دوال التعديل
                    </button>
                    <button class="test-button" onclick="testEmployeeData()">
                        <i class="fas fa-database"></i> اختبار بيانات الموظفين
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-edit"></i> اختبارات التعديل</h3>
                    <button class="test-button" onclick="testEditButton()">
                        <i class="fas fa-mouse-pointer"></i> اختبار زر التعديل
                    </button>
                    <button class="test-button" onclick="testFormFilling()">
                        <i class="fas fa-wpforms"></i> اختبار ملء النموذج
                    </button>
                    <button class="test-button" onclick="testUpdateFunction()">
                        <i class="fas fa-save"></i> اختبار دالة التحديث
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-user-check"></i> اختبارات التحقق</h3>
                    <button class="test-button" onclick="testValidation()">
                        <i class="fas fa-shield-alt"></i> اختبار التحقق من البيانات
                    </button>
                    <button class="test-button" onclick="testDuplicateCode()">
                        <i class="fas fa-copy"></i> اختبار تكرار الكود
                    </button>
                    <button class="test-button" onclick="testRequiredFields()">
                        <i class="fas fa-asterisk"></i> اختبار الحقول المطلوبة
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-tools"></i> اختبارات متقدمة</h3>
                    <button class="test-button" onclick="createTestEmployee()">
                        <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                    </button>
                    <button class="test-button" onclick="testEditTestEmployee()">
                        <i class="fas fa-user-edit"></i> تعديل الموظف التجريبي
                    </button>
                    <button class="test-button" onclick="runFullEditTest()">
                        <i class="fas fa-play"></i> اختبار شامل للتعديل
                    </button>
                </div>
            </div>

            <!-- عرض توضيحي للموظف -->
            <div class="employee-demo">
                <h4><i class="fas fa-user"></i> موظف تجريبي للاختبار</h4>
                <div id="demoEmployee">
                    <p><strong>الكود:</strong> <span id="demoCode">TEST001</span></p>
                    <p><strong>الاسم:</strong> <span id="demoName">أحمد محمد علي</span></p>
                    <p><strong>الوظيفة:</strong> <span id="demoPosition">مطور برمجيات</span></p>
                    <button class="demo-button" onclick="editEmployee(999)">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="demo-button" onclick="showEmployeeCard()">
                        <i class="fas fa-eye"></i> عرض البطاقة
                    </button>
                </div>
            </div>

            <div class="test-grid">
                <div class="test-card">
                    <h3><i class="fas fa-broom"></i> أدوات التنظيف</h3>
                    <button class="test-button warning" onclick="clearResults()">
                        <i class="fas fa-eraser"></i> مسح النتائج
                    </button>
                    <button class="test-button danger" onclick="clearTestData()">
                        <i class="fas fa-trash-alt"></i> مسح البيانات التجريبية
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-external-link-alt"></i> روابط سريعة</h3>
                    <button class="test-button success" onclick="openMainApp()">
                        <i class="fas fa-home"></i> فتح التطبيق الرئيسي
                    </button>
                    <button class="test-button" onclick="openNewEmployeeSystem()">
                        <i class="fas fa-cog"></i> نظام الموظف الجديد
                    </button>
                </div>
            </div>

            <div class="results" id="results">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="resultsList">
                    <p>جاهز لبدء اختبارات زر التعديل...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testResults = [];
        let testEmployeeId = 999;

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            testResults.push({ message, type, timestamp });
            
            const resultsList = document.getElementById('resultsList');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `
                <span class="status-indicator status-${type}"></span>
                <strong>[${timestamp}]</strong> ${message}
            `;
            
            resultsList.appendChild(resultItem);
            resultsList.scrollTop = resultsList.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            document.getElementById('resultsList').innerHTML = '<p>تم مسح النتائج...</p>';
            addResult('تم مسح جميع النتائج', 'warning');
        }

        function testSystemLoad() {
            addResult('🔍 بدء اختبار تحميل النظام...', 'info');
            
            // اختبار المتغيرات العامة
            if (typeof employees !== 'undefined') {
                addResult('✅ متغير employees محمل بنجاح', 'success');
                addResult(`📊 عدد الموظفين الحالي: ${employees.length}`, 'info');
            } else {
                addResult('❌ متغير employees غير محمل', 'error');
            }

            // اختبار دوال النظام الأساسي
            const basicFunctions = ['populateEmployees', 'saveEmployeesToLocalStorage', 'showTemporaryMessage'];
            basicFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            addResult('✅ اكتمل اختبار تحميل النظام', 'success');
        }

        function testEditFunctions() {
            addResult('🔍 بدء اختبار دوال التعديل...', 'info');
            
            const editFunctions = [
                'editEmployee',
                'fillEmployeeForm', 
                'updateEmployee',
                'resetEmployeeFormToAddMode'
            ];

            let functionsFound = 0;
            
            editFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            const percentage = Math.round((functionsFound / editFunctions.length) * 100);
            addResult(`📊 نسبة دوال التعديل المتاحة: ${percentage}%`, percentage >= 80 ? 'success' : 'warning');
        }

        function testEmployeeData() {
            addResult('🔍 اختبار بيانات الموظفين...', 'info');
            
            if (typeof employees !== 'undefined' && Array.isArray(employees)) {
                addResult(`📊 عدد الموظفين: ${employees.length}`, 'info');
                
                if (employees.length > 0) {
                    const firstEmployee = employees[0];
                    const requiredFields = ['id', 'name', 'employeeCode'];
                    
                    requiredFields.forEach(field => {
                        if (firstEmployee.hasOwnProperty(field)) {
                            addResult(`✅ حقل ${field}: موجود`, 'success');
                        } else {
                            addResult(`❌ حقل ${field}: مفقود`, 'error');
                        }
                    });
                } else {
                    addResult('⚠️ لا توجد بيانات موظفين للاختبار', 'warning');
                }
            } else {
                addResult('❌ مصفوفة الموظفين غير صحيحة', 'error');
            }
        }

        function testEditButton() {
            addResult('🔍 اختبار زر التعديل...', 'info');
            
            try {
                if (typeof editEmployee === 'function') {
                    addResult('✅ دالة editEmployee متاحة', 'success');
                    
                    // محاولة استدعاء الدالة مع معرف وهمي
                    try {
                        editEmployee(testEmployeeId);
                        addResult('✅ تم استدعاء دالة editEmployee بنجاح', 'success');
                    } catch (error) {
                        addResult(`❌ خطأ في استدعاء editEmployee: ${error.message}`, 'error');
                    }
                } else {
                    addResult('❌ دالة editEmployee غير متاحة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار زر التعديل: ${error.message}`, 'error');
            }
        }

        function testFormFilling() {
            addResult('🔍 اختبار ملء النموذج...', 'info');
            
            // إنشاء موظف تجريبي
            const testEmployee = {
                id: testEmployeeId,
                employeeCode: 'TEST001',
                name: 'أحمد محمد علي',
                position: 'مطور برمجيات',
                nationalId: '12345678901234',
                phone: '01000000000',
                address: 'القاهرة، مصر',
                joinDate: '2023-01-01'
            };

            try {
                if (typeof fillEmployeeForm === 'function') {
                    fillEmployeeForm(testEmployee);
                    addResult('✅ تم استدعاء دالة ملء النموذج', 'success');
                    
                    // التحقق من ملء الحقول
                    setTimeout(() => {
                        const codeField = document.getElementById('newEmployeeCode');
                        if (codeField && codeField.value === testEmployee.employeeCode) {
                            addResult('✅ تم ملء حقل الكود بنجاح', 'success');
                        } else {
                            addResult('❌ فشل في ملء حقل الكود', 'error');
                        }
                    }, 200);
                } else {
                    addResult('❌ دالة fillEmployeeForm غير متاحة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار ملء النموذج: ${error.message}`, 'error');
            }
        }

        function testUpdateFunction() {
            addResult('🔍 اختبار دالة التحديث...', 'info');
            
            if (typeof updateEmployee === 'function') {
                addResult('✅ دالة updateEmployee متاحة', 'success');
            } else {
                addResult('❌ دالة updateEmployee غير متاحة', 'error');
            }
        }

        function testValidation() {
            addResult('🔍 اختبار التحقق من البيانات...', 'info');
            
            // اختبار التحقق من الحقول المطلوبة
            addResult('📝 اختبار التحقق من الحقول المطلوبة...', 'info');
            
            // اختبار التحقق من تكرار الكود
            addResult('🔄 اختبار التحقق من تكرار الكود...', 'info');
            
            addResult('✅ اكتمل اختبار التحقق من البيانات', 'success');
        }

        function testDuplicateCode() {
            addResult('🔍 اختبار تكرار الكود...', 'info');
            
            if (typeof employees !== 'undefined' && employees.length > 0) {
                const codes = employees.map(emp => emp.employeeCode);
                const uniqueCodes = [...new Set(codes)];
                
                if (codes.length === uniqueCodes.length) {
                    addResult('✅ لا توجد أكواد مكررة', 'success');
                } else {
                    addResult('⚠️ توجد أكواد مكررة في النظام', 'warning');
                }
            } else {
                addResult('⚠️ لا توجد بيانات للاختبار', 'warning');
            }
        }

        function testRequiredFields() {
            addResult('🔍 اختبار الحقول المطلوبة...', 'info');
            
            const requiredFields = [
                'newEmployeeCode',
                'newEmployeeName',
                'newEmployeePosition',
                'newEmployeeNationalId',
                'newEmployeePhone',
                'newEmployeeJoinDate'
            ];

            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    addResult(`✅ حقل ${fieldId}: موجود`, 'success');
                } else {
                    addResult(`❌ حقل ${fieldId}: مفقود`, 'error');
                }
            });
        }

        function createTestEmployee() {
            addResult('🔍 إنشاء موظف تجريبي...', 'info');
            
            const testEmployee = {
                id: testEmployeeId,
                employeeCode: 'TEST001',
                name: 'أحمد محمد علي',
                position: 'مطور برمجيات',
                nationalId: '12345678901234',
                phone: '01000000000',
                address: 'القاهرة، مصر',
                joinDate: '2023-01-01',
                photo: 'https://via.placeholder.com/150/17a2b8/ffffff?text=TEST',
                employmentType: 'monthly',
                basicSalary: 5000,
                totalSalary: 5000,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم وجود موظف بنفس المعرف
            const existingIndex = employees.findIndex(emp => emp.id === testEmployeeId);
            if (existingIndex !== -1) {
                employees.splice(existingIndex, 1);
                addResult('🗑️ تم حذف الموظف التجريبي السابق', 'warning');
            }

            employees.push(testEmployee);
            
            if (typeof saveEmployeesToLocalStorage === 'function') {
                saveEmployeesToLocalStorage();
            }
            
            if (typeof populateEmployees === 'function') {
                populateEmployees();
            }

            addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
            addResult(`👤 الاسم: ${testEmployee.name}`, 'info');
            addResult(`🏷️ الكود: ${testEmployee.employeeCode}`, 'info');
        }

        function testEditTestEmployee() {
            addResult('🔍 اختبار تعديل الموظف التجريبي...', 'info');
            
            const testEmployee = employees.find(emp => emp.id === testEmployeeId);
            if (testEmployee) {
                try {
                    editEmployee(testEmployeeId);
                    addResult('✅ تم فتح نافذة تعديل الموظف التجريبي', 'success');
                } catch (error) {
                    addResult(`❌ خطأ في تعديل الموظف التجريبي: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ الموظف التجريبي غير موجود', 'error');
                addResult('💡 قم بإنشاء موظف تجريبي أولاً', 'info');
            }
        }

        function runFullEditTest() {
            addResult('🚀 بدء الاختبار الشامل لزر التعديل...', 'info');
            
            clearResults();
            
            setTimeout(() => testSystemLoad(), 500);
            setTimeout(() => testEditFunctions(), 1000);
            setTimeout(() => testEmployeeData(), 1500);
            setTimeout(() => createTestEmployee(), 2000);
            setTimeout(() => testEditButton(), 2500);
            setTimeout(() => testFormFilling(), 3000);
            setTimeout(() => testUpdateFunction(), 3500);
            setTimeout(() => testValidation(), 4000);
            setTimeout(() => testDuplicateCode(), 4500);
            setTimeout(() => testRequiredFields(), 5000);
            
            setTimeout(() => {
                addResult('🎉 اكتمل الاختبار الشامل لزر التعديل!', 'success');
                
                const successCount = testResults.filter(r => r.type === 'success').length;
                const errorCount = testResults.filter(r => r.type === 'error').length;
                const warningCount = testResults.filter(r => r.type === 'warning').length;
                
                addResult(`📊 ملخص النتائج:`, 'info');
                addResult(`✅ نجح: ${successCount}`, 'success');
                addResult(`❌ فشل: ${errorCount}`, errorCount > 0 ? 'error' : 'info');
                addResult(`⚠️ تحذيرات: ${warningCount}`, warningCount > 0 ? 'warning' : 'info');
                
                const successRate = Math.round((successCount / (successCount + errorCount + warningCount)) * 100);
                addResult(`🎯 معدل النجاح: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
            }, 5500);
        }

        function clearTestData() {
            addResult('🗑️ مسح البيانات التجريبية...', 'warning');
            
            const initialCount = employees.length;
            employees = employees.filter(emp => emp.id !== testEmployeeId);
            const removedCount = initialCount - employees.length;
            
            if (removedCount > 0) {
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                
                if (typeof populateEmployees === 'function') {
                    populateEmployees();
                }
                
                addResult(`✅ تم حذف ${removedCount} موظف تجريبي`, 'success');
            } else {
                addResult('ℹ️ لا توجد بيانات تجريبية للحذف', 'info');
            }
        }

        function openMainApp() {
            addResult('🔗 فتح التطبيق الرئيسي...', 'info');
            window.open('index.html', '_blank');
        }

        function openNewEmployeeSystem() {
            addResult('🔗 فتح نظام الموظف الجديد...', 'info');
            window.open('test_new_employee_system.html', '_blank');
        }

        function showEmployeeCard() {
            addResult('👁️ عرض بطاقة الموظف التجريبي...', 'info');
            
            const testEmployee = employees.find(emp => emp.id === testEmployeeId);
            if (testEmployee) {
                addResult(`📋 بيانات الموظف:`, 'info');
                addResult(`🏷️ الكود: ${testEmployee.employeeCode}`, 'info');
                addResult(`👤 الاسم: ${testEmployee.name}`, 'info');
                addResult(`💼 الوظيفة: ${testEmployee.position}`, 'info');
                addResult(`📞 الهاتف: ${testEmployee.phone}`, 'info');
            } else {
                addResult('❌ الموظف التجريبي غير موجود', 'error');
            }
        }

        // تحميل تلقائي عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 تم تحميل صفحة اختبار زر التعديل', 'success');
            addResult('🚀 جاهز لبدء الاختبارات...', 'info');
            
            // اختبار سريع تلقائي
            setTimeout(() => {
                if (typeof editEmployee === 'function') {
                    addResult('✅ دالة تعديل الموظف محملة ومتاحة', 'success');
                } else {
                    addResult('❌ دالة تعديل الموظف غير محملة', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
