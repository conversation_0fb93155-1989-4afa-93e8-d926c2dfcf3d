<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص أزرار التعديل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.secondary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .debug-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 20px 0;
        }
        
        .debug-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .step {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص أزرار التعديل</h1>
            <p>أداة تشخيص متقدمة لحل مشكلة أزرار التعديل المفقودة</p>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-tools"></i> خطوات التشخيص:</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء البيانات التجريبية</strong>
                <button class="test-button" onclick="createTestData()" style="float: left;">
                    <i class="fas fa-plus"></i> إنشاء
                </button>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة السجلات</strong>
                <button class="test-button secondary" onclick="openRecordsWindow()" style="float: left;">
                    <i class="fas fa-table"></i> فتح
                </button>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>فحص حالة التعديل المباشر</strong>
                <button class="test-button warning" onclick="checkInlineMode()" style="float: left;">
                    <i class="fas fa-search"></i> فحص
                </button>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>تبديل وضع التعديل</strong>
                <button class="test-button" onclick="toggleEditMode()" style="float: left;">
                    <i class="fas fa-exchange-alt"></i> تبديل
                </button>
            </div>
        </div>

        <div id="status" class="status info">
            <i class="fas fa-info-circle"></i> جاهز لبدء التشخيص...
        </div>

        <div class="debug-section">
            <h4><i class="fas fa-terminal"></i> سجل التشخيص:</h4>
            <div id="debugOutput" class="debug-output">
                [DEBUG] نظام التشخيص جاهز...<br>
            </div>
            <button onclick="clearDebugLog()" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                مسح السجل
            </button>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
            <h4><i class="fas fa-lightbulb"></i> نصائح التشخيص:</h4>
            <ul style="text-align: right; margin: 15px 0;">
                <li><strong>تحقق من Console:</strong> افتح Developer Tools (F12) وراقب رسائل console.log</li>
                <li><strong>فحص العناصر:</strong> استخدم Inspect Element للتحقق من وجود الأزرار في HTML</li>
                <li><strong>حالة المتغيرات:</strong> تحقق من قيمة inlineEditModeEnabled</li>
                <li><strong>البيانات:</strong> تأكد من وجود سجلات أيام للموظف</li>
            </ul>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function debugLog(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
            debugLog(`STATUS: ${message}`);
        }

        function createTestData() {
            debugLog('بدء إنشاء البيانات التجريبية...');
            updateStatus('جاري إنشاء البيانات التجريبية...', 'info');
            
            try {
                // التحقق من وجود المصفوفات
                if (typeof employees === 'undefined') {
                    debugLog('ERROR: مصفوفة employees غير موجودة!');
                    updateStatus('خطأ: مصفوفة employees غير موجودة', 'error');
                    return;
                }
                
                if (typeof employeeDaysData === 'undefined') {
                    debugLog('ERROR: مصفوفة employeeDaysData غير موجودة!');
                    updateStatus('خطأ: مصفوفة employeeDaysData غير موجودة', 'error');
                    return;
                }
                
                debugLog(`عدد الموظفين الحالي: ${employees.length}`);
                debugLog(`عدد سجلات الأيام الحالي: ${employeeDaysData.length}`);
                
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 999)) {
                    employees.push({
                        id: 999,
                        name: 'أحمد محمد علي - تجريبي',
                        position: 'مهندس برمجيات',
                        employeeCode: 'TEST001',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                    debugLog('تم إنشاء موظف تجريبي بمعرف 999');
                } else {
                    debugLog('الموظف التجريبي موجود مسبقاً');
                }
                
                // إنشاء سجلات أيام تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 999,
                        projectName: 'مشروع تطوير النظام',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 999,
                        projectName: 'مشروع الصيانة',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 1,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                let addedRecords = 0;
                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                        addedRecords++;
                        debugLog(`تم إضافة سجل: ${record.projectName} (ID: ${record.id})`);
                    }
                });
                
                debugLog(`تم إضافة ${addedRecords} سجل جديد`);
                debugLog(`إجمالي السجلات الآن: ${employeeDaysData.length}`);
                
                updateStatus(`تم إنشاء البيانات بنجاح! (${addedRecords} سجل جديد)`, 'success');
                
            } catch (error) {
                debugLog(`ERROR: ${error.message}`);
                updateStatus('خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            debugLog('محاولة فتح نافذة السجلات...');
            
            try {
                // التحقق من وجود البيانات
                if (!employees.find(emp => emp.id === 999)) {
                    debugLog('ERROR: الموظف التجريبي غير موجود');
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                // التحقق من وجود دالة viewDaysRecords
                if (typeof viewDaysRecords !== 'function') {
                    debugLog('ERROR: دالة viewDaysRecords غير موجودة');
                    updateStatus('خطأ: دالة viewDaysRecords غير موجودة', 'error');
                    return;
                }
                
                debugLog('فتح نافذة السجلات للموظف 999...');
                viewDaysRecords(999);
                
                // التحقق من فتح النافذة
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        debugLog('✓ تم فتح نافذة السجلات بنجاح');
                        updateStatus('تم فتح نافذة السجلات بنجاح!', 'success');
                    } else {
                        debugLog('ERROR: فشل في فتح نافذة السجلات');
                        updateStatus('فشل في فتح نافذة السجلات', 'error');
                    }
                }, 500);
                
            } catch (error) {
                debugLog(`ERROR: ${error.message}`);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function checkInlineMode() {
            debugLog('فحص حالة التعديل المباشر...');
            
            try {
                // فحص المتغير العام
                if (typeof inlineEditModeEnabled !== 'undefined') {
                    debugLog(`قيمة inlineEditModeEnabled: ${inlineEditModeEnabled}`);
                } else {
                    debugLog('ERROR: متغير inlineEditModeEnabled غير موجود');
                }
                
                // فحص checkbox
                const checkbox = document.getElementById('inlineEditMode');
                if (checkbox) {
                    debugLog(`حالة checkbox: ${checkbox.checked}`);
                    debugLog(`checkbox موجود في النافذة`);
                } else {
                    debugLog('WARNING: checkbox غير موجود (النافذة مغلقة؟)');
                }
                
                // فحص الجدول
                const table = document.getElementById('recordsTable');
                if (table) {
                    const editButtons = table.querySelectorAll('.btn-edit');
                    const inputFields = table.querySelectorAll('input[onchange*="updateRecordField"]');
                    
                    debugLog(`عدد أزرار التعديل الموجودة: ${editButtons.length}`);
                    debugLog(`عدد حقول الإدخال الموجودة: ${inputFields.length}`);
                    
                    if (editButtons.length > 0) {
                        updateStatus(`تم العثور على ${editButtons.length} زر تعديل!`, 'success');
                    } else if (inputFields.length > 0) {
                        updateStatus(`الوضع المباشر مفعل (${inputFields.length} حقل إدخال)`, 'info');
                    } else {
                        updateStatus('لم يتم العثور على أزرار تعديل أو حقول إدخال', 'warning');
                    }
                } else {
                    debugLog('WARNING: جدول السجلات غير موجود');
                    updateStatus('جدول السجلات غير موجود - افتح النافذة أولاً', 'warning');
                }
                
            } catch (error) {
                debugLog(`ERROR: ${error.message}`);
                updateStatus('خطأ في فحص الحالة: ' + error.message, 'error');
            }
        }

        function toggleEditMode() {
            debugLog('محاولة تبديل وضع التعديل...');
            
            try {
                const checkbox = document.getElementById('inlineEditMode');
                if (!checkbox) {
                    debugLog('ERROR: checkbox غير موجود - افتح النافذة أولاً');
                    updateStatus('افتح نافذة السجلات أولاً', 'warning');
                    return;
                }
                
                const oldState = checkbox.checked;
                checkbox.checked = !checkbox.checked;
                debugLog(`تغيير حالة checkbox من ${oldState} إلى ${checkbox.checked}`);
                
                // استدعاء دالة التبديل
                if (typeof toggleInlineEditMode === 'function') {
                    toggleInlineEditMode();
                    debugLog('تم استدعاء دالة toggleInlineEditMode');
                    
                    // فحص النتيجة بعد التبديل
                    setTimeout(() => {
                        checkInlineMode();
                    }, 500);
                } else {
                    debugLog('ERROR: دالة toggleInlineEditMode غير موجودة');
                    updateStatus('خطأ: دالة التبديل غير موجودة', 'error');
                }
                
            } catch (error) {
                debugLog(`ERROR: ${error.message}`);
                updateStatus('خطأ في تبديل الوضع: ' + error.message, 'error');
            }
        }

        function clearDebugLog() {
            document.getElementById('debugOutput').innerHTML = '[DEBUG] تم مسح السجل...<br>';
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('تم تحميل صفحة التشخيص');
            updateStatus('نظام التشخيص جاهز - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
