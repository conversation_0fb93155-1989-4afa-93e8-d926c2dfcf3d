<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الإجازات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #6c757d;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 13px;
        }

        .table td {
            font-size: 13px;
        }

        .table tbody tr:nth-child(even) {
            background: #f9f9f9;
        }

        .table tbody tr:hover {
            background: #e9ecef;
        }

        .hidden {
            display: none;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .vacation-type-btn {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .vacation-type-btn:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }

        .vacation-type-btn.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }

        .vacation-type-btn .icon {
            font-size: 2em;
            margin-bottom: 8px;
        }

        .vacation-type-btn .title {
            font-weight: 600;
            color: #495057;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-calendar-alt"></i> نظام إدارة الإجازات</h1>
            <p>إدارة الإجازات المستحقة للموظفين</p>
        </div>

        <!-- Employee Selection -->
        <div class="card">
            <h3><i class="fas fa-user"></i> اختيار الموظف</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>الموظف:</label>
                    <select id="employeeSelect" class="form-control" onchange="selectEmployee()">
                        <option value="">اختر الموظف</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-primary" onclick="addEmployee()">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Employee Info -->
        <div id="employeeInfo" class="card hidden">
            <h3><i class="fas fa-user-circle"></i> معلومات الموظف</h3>
            <div id="employeeDetails"></div>
        </div>

        <!-- Vacation Types -->
        <div id="vacationTypes" class="card hidden">
            <h3><i class="fas fa-list"></i> أنواع الإجازات</h3>
            <div class="grid">
                <div class="vacation-type-btn" onclick="selectVacationType('annual')">
                    <div class="icon">📅</div>
                    <div class="title">إجازة سنوية</div>
                </div>
                <div class="vacation-type-btn" onclick="selectVacationType('official')">
                    <div class="icon">🎉</div>
                    <div class="title">عطلات رسمية</div>
                </div>
            </div>
        </div>

        <!-- Vacation Form -->
        <div id="vacationForm" class="card hidden">
            <h3><i class="fas fa-plus-circle"></i> إضافة إجازة مستحقة</h3>
            <div id="formContent"></div>
        </div>

        <!-- Vacation Records -->
        <div id="vacationRecords" class="card hidden">
            <h3><i class="fas fa-table"></i> سجلات الإجازات المستحقة</h3>
            <div id="recordsTable"></div>
        </div>
    </div>

    <script src="vacations_new.js"></script>
</body>
</html>
