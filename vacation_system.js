// نظام إدارة الإجازات - JavaScript

// متغيرات النظام
let employees = [];
let vacationsData = [];
let customHolidays = [];
let selectedEmployee = null;
let selectedVacationType = null;

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    try {
        loadDataFromStorage();
        loadEmployeesList();
        console.log('تم تحميل نظام إدارة الإجازات');

        // إضافة بعض البيانات التجريبية إذا لم تكن موجودة
        if (employees.length === 0) {
            addSampleData();
        }
    } catch (error) {
        console.error('خطأ في تحميل النظام:', error);
        showError('حدث خطأ في تحميل النظام. يرجى إعادة تحميل الصفحة.');
    }
});

// إضافة بيانات تجريبية
function addSampleData() {
    const sampleEmployee = {
        id: 1,
        name: 'أحمد محمد',
        position: 'مطور برمجيات',
        employmentType: 'monthly',
        createdAt: new Date().toISOString()
    };

    employees.push(sampleEmployee);
    saveDataToStorage();
    loadEmployeesList();
    console.log('تم إضافة بيانات تجريبية');
}

// عرض رسالة خطأ
function showError(message) {
    const content = document.getElementById('vacationContent');
    if (content) {
        content.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
            </div>
        `;
    }
}

// تحميل البيانات من localStorage
function loadDataFromStorage() {
    try {
        const savedEmployees = localStorage.getItem('vacationSystem_employees');
        const savedVacations = localStorage.getItem('vacationSystem_vacations');
        const savedHolidays = localStorage.getItem('vacationSystem_holidays');

        employees = savedEmployees ? JSON.parse(savedEmployees) : [];
        vacationsData = savedVacations ? JSON.parse(savedVacations) : [];
        customHolidays = savedHolidays ? JSON.parse(savedHolidays) : [];

        console.log(`تم تحميل ${employees.length} موظف، ${vacationsData.length} سجل إجازة`);
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        employees = [];
        vacationsData = [];
        customHolidays = [];
    }
}

// حفظ البيانات في localStorage
function saveDataToStorage() {
    try {
        localStorage.setItem('vacationSystem_employees', JSON.stringify(employees));
        localStorage.setItem('vacationSystem_vacations', JSON.stringify(vacationsData));
        localStorage.setItem('vacationSystem_holidays', JSON.stringify(customHolidays));
        console.log('تم حفظ البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
    }
}

// تحميل قائمة الموظفين
function loadEmployeesList() {
    const select = document.getElementById('employeeSelect');
    select.innerHTML = '<option value="">اختر الموظف</option>';

    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;
        option.textContent = `${employee.name} - ${employee.position || 'غير محدد'}`;
        select.appendChild(option);
    });
}

// إضافة موظف جديد
function addNewEmployee() {
    const name = prompt('اسم الموظف:');
    if (!name || name.trim() === '') return;

    const position = prompt('المنصب:') || '';
    const employmentType = confirm('هل الموظف شهري؟ (اضغط إلغاء للموظف اليومي)') ? 'monthly' : 'daily';

    const newEmployee = {
        id: Date.now(),
        name: name.trim(),
        position: position.trim(),
        employmentType: employmentType,
        createdAt: new Date().toISOString()
    };

    employees.push(newEmployee);
    saveDataToStorage();
    loadEmployeesList();

    // تحديد الموظف الجديد
    document.getElementById('employeeSelect').value = newEmployee.id;
    loadEmployeeVacations();

    alert('تم إضافة الموظف بنجاح');
}

// تحميل إجازات الموظف المحدد
function loadEmployeeVacations() {
    const employeeId = document.getElementById('employeeSelect').value;
    if (!employeeId) {
        selectedEmployee = null;
        showWelcomeMessage();
        updateQuickStats();
        return;
    }

    selectedEmployee = employees.find(emp => emp.id == employeeId);
    if (!selectedEmployee) return;

    showVacationManagement();
    updateQuickStats();
}

// عرض رسالة الترحيب
function showWelcomeMessage() {
    document.getElementById('vacationContent').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-arrow-right"></i>
            اختر موظف من القائمة لبدء إدارة الإجازات
        </div>
    `;
}

// عرض واجهة إدارة الإجازات
function showVacationManagement() {
    const content = document.getElementById('vacationContent');
    content.innerHTML = `
        <div class="section">
            <h3><i class="fas fa-user-circle"></i> ${selectedEmployee.name}</h3>
            <p><strong>المنصب:</strong> ${selectedEmployee.position || 'غير محدد'}</p>
            <p><strong>نوع التوظيف:</strong> ${selectedEmployee.employmentType === 'monthly' ? 'شهري' : 'يومي'}</p>
        </div>

        <div id="vacationForm" class="section hidden">
            <!-- سيتم ملء النموذج هنا -->
        </div>

        <div class="section">
            <h3><i class="fas fa-table"></i> جدول الإجازات المستحقة</h3>
            <div id="entitlementsTable">
                <!-- سيتم ملء الجدول هنا -->
            </div>
        </div>
    `;

    // استخدام setTimeout للتأكد من تحديث DOM قبل تحميل الجدول
    setTimeout(() => {
        loadEntitlementsTable();
    }, 10);
}

// اختيار نوع الإجازة
function selectVacationType(type) {
    selectedVacationType = type;

    // تحديث المظهر
    document.querySelectorAll('.vacation-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.target.closest('.vacation-type-card').classList.add('selected');

    showVacationForm();
}

// عرض نموذج الإجازة
function showVacationForm() {
    if (!selectedEmployee || !selectedVacationType) return;

    const formContainer = document.getElementById('vacationForm');
    formContainer.classList.remove('hidden');

    let formHTML = `
        <h3><i class="fas fa-plus-circle"></i> إضافة إجازة ${getVacationTypeText(selectedVacationType)}</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div class="form-group">
                <label>السنة:</label>
                <select id="vacationYear" class="form-control">
                    ${generateYearOptions()}
                </select>
            </div>

            <div class="form-group">
                <label>الأيام المستحقة:</label>
                <input type="number" id="vacationDays" class="form-control" min="1" max="365" placeholder="عدد الأيام">
            </div>
        </div>
    `;

    // إضافة حقول خاصة حسب نوع الإجازة
    if (selectedVacationType === 'annual') {
        formHTML += `
            <div class="form-group">
                <label>نوع الإجازة السنوية:</label>
                <select id="annualSubtype" class="form-control">
                    <option value="">اختر النوع</option>
                    <option value="regular">اعتيادي</option>
                    <option value="emergency">عارضة</option>
                </select>
            </div>
        `;
    } else if (selectedVacationType === 'official') {
        formHTML += `
            <div class="form-group">
                <label>العطلة الرسمية:</label>
                <select id="officialHoliday" class="form-control" onchange="handleOfficialHolidayChange()">
                    <option value="">اختر العطلة</option>
                    <option value="new_year">رأس السنة الميلادية</option>
                    <option value="revolution_day">عيد الثورة</option>
                    <option value="labor_day">عيد العمال</option>
                    <option value="eid_fitr">عيد الفطر</option>
                    <option value="eid_adha">عيد الأضحى</option>
                    <option value="add_custom">إضافة عطلة مخصصة</option>
                </select>
            </div>

            <div id="customHolidayInput" class="form-group hidden">
                <label>اسم العطلة المخصصة:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="customHolidayName" class="form-control" placeholder="أدخل اسم العطلة">
                    <button type="button" class="btn btn-success" onclick="addCustomHoliday()">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
        `;
    }

    formHTML += `
        <div class="form-group">
            <label>ملاحظات:</label>
            <textarea id="vacationNotes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."></textarea>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-primary" onclick="saveVacationEntitlement()">
                <i class="fas fa-save"></i> حفظ الإجازة المستحقة
            </button>
            <button class="btn btn-secondary" onclick="resetForm()">
                <i class="fas fa-times"></i> إلغاء
            </button>
        </div>
    `;

    formContainer.innerHTML = formHTML;

    // تحديث قائمة العطلات المخصصة
    updateCustomHolidaysDropdown();
}

// إنشاء خيارات السنوات
function generateYearOptions() {
    let options = '';
    const currentYear = new Date().getFullYear();

    for (let year = 2020; year <= 2035; year++) {
        const selected = year === currentYear ? 'selected' : '';
        options += `<option value="${year}" ${selected}>${year}</option>`;
    }

    return options;
}

// الحصول على نص نوع الإجازة
function getVacationTypeText(type) {
    const types = {
        'annual': 'سنوية',
        'official': 'عطلات رسمية'
    };
    return types[type] || type;
}

// معالجة تغيير العطلة الرسمية
function handleOfficialHolidayChange() {
    const select = document.getElementById('officialHoliday');
    const customInput = document.getElementById('customHolidayInput');

    if (select.value === 'add_custom') {
        customInput.classList.remove('hidden');
    } else {
        customInput.classList.add('hidden');
    }
}

// إضافة عطلة مخصصة
function addCustomHoliday() {
    const nameInput = document.getElementById('customHolidayName');
    const name = nameInput.value.trim();

    if (!name) {
        alert('يرجى إدخال اسم العطلة');
        return;
    }

    // التحقق من عدم وجود العطلة مسبقاً
    if (customHolidays.find(h => h.name.toLowerCase() === name.toLowerCase())) {
        alert('هذه العطلة موجودة بالفعل');
        return;
    }

    const newHoliday = {
        id: Date.now(),
        name: name,
        value: `custom_${Date.now()}`,
        createdAt: new Date().toISOString()
    };

    customHolidays.push(newHoliday);
    saveDataToStorage();

    // تحديث القائمة المنسدلة
    updateCustomHolidaysDropdown();

    // تحديد العطلة الجديدة
    document.getElementById('officialHoliday').value = newHoliday.value;

    // إخفاء حقل الإدخال ومسح النص
    document.getElementById('customHolidayInput').classList.add('hidden');
    nameInput.value = '';

    alert('تم إضافة العطلة بنجاح');
}

// تحديث قائمة العطلات المخصصة
function updateCustomHolidaysDropdown() {
    const select = document.getElementById('officialHoliday');
    if (!select) return;

    // إزالة العطلات المخصصة الموجودة
    const customOptions = select.querySelectorAll('option[value^="custom_"]');
    customOptions.forEach(option => option.remove());

    // إضافة العطلات المخصصة الجديدة
    const addCustomOption = select.querySelector('option[value="add_custom"]');
    customHolidays.forEach(holiday => {
        const option = document.createElement('option');
        option.value = holiday.value;
        option.textContent = holiday.name;
        select.insertBefore(option, addCustomOption);
    });
}

// حفظ الإجازة المستحقة
function saveVacationEntitlement() {
    if (!selectedEmployee || !selectedVacationType) {
        alert('يرجى اختيار الموظف ونوع الإجازة');
        return;
    }

    const year = document.getElementById('vacationYear').value;
    const days = parseInt(document.getElementById('vacationDays').value);
    const notes = document.getElementById('vacationNotes').value;

    if (!year || !days || days <= 0) {
        alert('يرجى إدخال السنة وعدد الأيام');
        return;
    }

    // جمع البيانات الإضافية
    let additionalData = {};

    if (selectedVacationType === 'annual') {
        const subtype = document.getElementById('annualSubtype').value;
        if (!subtype) {
            alert('يرجى اختيار نوع الإجازة السنوية');
            return;
        }
        additionalData.subtype = subtype;
    } else if (selectedVacationType === 'official') {
        const holiday = document.getElementById('officialHoliday').value;
        if (!holiday || holiday === 'add_custom') {
            alert('يرجى اختيار العطلة الرسمية');
            return;
        }
        additionalData.holidayType = holiday;
    }

    // البحث عن سجل موجود
    const existingIndex = vacationsData.findIndex(record =>
        record.employeeId === selectedEmployee.id &&
        record.type === 'entitlement' &&
        record.year === parseInt(year) &&
        record.vacationType === selectedVacationType &&
        JSON.stringify(record.additionalData) === JSON.stringify(additionalData)
    );

    const entitlementRecord = {
        id: existingIndex !== -1 ? vacationsData[existingIndex].id : Date.now(),
        employeeId: selectedEmployee.id,
        type: 'entitlement',
        vacationType: selectedVacationType,
        year: parseInt(year),
        days: days,
        notes: notes,
        additionalData: additionalData,
        createdAt: existingIndex !== -1 ? vacationsData[existingIndex].createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    if (existingIndex !== -1) {
        vacationsData[existingIndex] = entitlementRecord;
        alert('تم تحديث الإجازة المستحقة بنجاح');
    } else {
        vacationsData.push(entitlementRecord);
        alert('تم حفظ الإجازة المستحقة بنجاح');
    }

    saveDataToStorage();
    loadEntitlementsTable();
    resetForm();
    updateQuickStats();
}

// إعادة تعيين النموذج
function resetForm() {
    selectedVacationType = null;

    // إزالة التحديد من بطاقات نوع الإجازة
    document.querySelectorAll('.vacation-type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // إخفاء النموذج
    document.getElementById('vacationForm').classList.add('hidden');
}

// تحميل جدول الإجازات المستحقة
function loadEntitlementsTable() {
    if (!selectedEmployee) {
        console.warn('لا يوجد موظف محدد');
        return;
    }

    // التحقق من وجود العنصر مع إعادة المحاولة
    let container = document.getElementById('entitlementsTable');
    let attempts = 0;
    const maxAttempts = 5;

    const checkContainer = () => {
        container = document.getElementById('entitlementsTable');
        attempts++;

        if (!container && attempts < maxAttempts) {
            console.log(`محاولة ${attempts}: البحث عن حاوية الجدول...`);
            setTimeout(checkContainer, 50);
            return;
        }

        if (!container) {
            console.error('لم يتم العثور على حاوية الجدول بعد عدة محاولات');
            const content = document.getElementById('vacationContent');
            if (content) {
                content.innerHTML += `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ في تحميل جدول الإجازات. يرجى إعادة اختيار الموظف.
                    </div>
                `;
            }
            return;
        }

        // تحميل الجدول
        loadTableContent(container);
    };

    checkContainer();
}

// تحميل محتوى الجدول
function loadTableContent(container) {
    try {
        const employeeRecords = vacationsData.filter(record =>
            record.employeeId === selectedEmployee.id && record.type === 'entitlement'
        );

        if (employeeRecords.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    لا توجد إجازات مستحقة مسجلة لهذا الموظف
                </div>
            `;
            return;
        }

    let tableHTML = `
        <table class="table">
            <thead>
                <tr>
                    <th>السنة</th>
                    <th>فئة الإجازة</th>
                    <th>نوع الإجازة</th>
                    <th>الأيام المستحقة</th>
                    <th>ملاحظات</th>
                    <th>تاريخ التسجيل</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
    `;

    employeeRecords.forEach(record => {
        const categoryText = getVacationTypeText(record.vacationType);
        const subtypeText = getVacationSubtypeText(record);
        const notesText = record.notes || '-';
        const dateText = new Date(record.createdAt).toLocaleDateString('ar-EG');

        tableHTML += `
            <tr>
                <td><strong>${record.year}</strong></td>
                <td>${categoryText}</td>
                <td>${subtypeText}</td>
                <td><span style="color: #28a745; font-weight: 600;">${record.days} يوم</span></td>
                <td>${notesText}</td>
                <td>${dateText}</td>
                <td>
                    <button class="btn btn-primary" onclick="editEntitlement(${record.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteEntitlement(${record.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

        container.innerHTML = tableHTML;
        console.log(`تم تحميل ${employeeRecords.length} سجل إجازة للموظف ${selectedEmployee.name}`);

    } catch (error) {
        console.error('خطأ في تحميل جدول الإجازات:', error);
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                حدث خطأ في تحميل جدول الإجازات. يرجى المحاولة مرة أخرى.
            </div>
        `;
    }
}

// الحصول على نص نوع الإجازة الفرعي
function getVacationSubtypeText(record) {
    if (!record.additionalData) return '-';

    if (record.additionalData.subtype) {
        return record.additionalData.subtype === 'regular' ? 'اعتيادي' : 'عارضة';
    }

    if (record.additionalData.holidayType) {
        const holidayNames = {
            'new_year': 'رأس السنة الميلادية',
            'revolution_day': 'عيد الثورة',
            'labor_day': 'عيد العمال',
            'eid_fitr': 'عيد الفطر',
            'eid_adha': 'عيد الأضحى'
        };

        if (record.additionalData.holidayType.startsWith('custom_')) {
            const customHoliday = customHolidays.find(h => h.value === record.additionalData.holidayType);
            return customHoliday ? customHoliday.name : 'عطلة مخصصة';
        }

        return holidayNames[record.additionalData.holidayType] || record.additionalData.holidayType;
    }

    return '-';
}

// تعديل الإجازة المستحقة
function editEntitlement(recordId) {
    const record = vacationsData.find(r => r.id === recordId);
    if (!record) {
        alert('السجل غير موجود');
        return;
    }

    // تحديد نوع الإجازة وإظهار النموذج
    selectedVacationType = record.vacationType;

    // تحديث المظهر للبطاقة المحددة
    document.querySelectorAll('.vacation-type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // العثور على البطاقة المناسبة وتحديدها
    const targetCard = document.querySelector(`[onclick="selectVacationType('${record.vacationType}')"]`);
    if (targetCard) {
        targetCard.classList.add('selected');
    }

    // إظهار النموذج
    showVacationForm();

    // ملء الحقول بعد إنشاء النموذج
    setTimeout(() => {
        const yearField = document.getElementById('vacationYear');
        const daysField = document.getElementById('vacationDays');
        const notesField = document.getElementById('vacationNotes');

        if (yearField) yearField.value = record.year;
        if (daysField) daysField.value = record.days;
        if (notesField) notesField.value = record.notes || '';

        if (record.additionalData) {
            if (record.additionalData.subtype) {
                const subtypeField = document.getElementById('annualSubtype');
                if (subtypeField) subtypeField.value = record.additionalData.subtype;
            }
            if (record.additionalData.holidayType) {
                const holidayField = document.getElementById('officialHoliday');
                if (holidayField) holidayField.value = record.additionalData.holidayType;
            }
        }

        // التمرير إلى النموذج
        const formContainer = document.getElementById('vacationForm');
        if (formContainer) {
            formContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, 200);

    alert('تم تحميل البيانات للتعديل');
}

// حذف الإجازة المستحقة
function deleteEntitlement(recordId) {
    const record = vacationsData.find(r => r.id === recordId);
    if (!record) {
        alert('السجل غير موجود');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف إجازة ${getVacationTypeText(record.vacationType)} لسنة ${record.year}؟`)) {
        vacationsData = vacationsData.filter(r => r.id !== recordId);
        saveDataToStorage();
        loadEntitlementsTable();
        updateQuickStats();
        alert('تم حذف السجل بنجاح');
    }
}

// تحديث الإحصائيات السريعة
function updateQuickStats() {
    const container = document.getElementById('quickStats');

    if (!selectedEmployee) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                اختر موظف لعرض الإحصائيات
            </div>
        `;
        return;
    }

    const employeeRecords = vacationsData.filter(record =>
        record.employeeId === selectedEmployee.id && record.type === 'entitlement'
    );

    const totalDays = employeeRecords.reduce((sum, record) => sum + record.days, 0);
    const currentYear = new Date().getFullYear();
    const currentYearRecords = employeeRecords.filter(record => record.year === currentYear);
    const currentYearDays = currentYearRecords.reduce((sum, record) => sum + record.days, 0);

    container.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon" style="color: #007bff;">📊</div>
                <div class="number">${employeeRecords.length}</div>
                <div class="label">إجمالي السجلات</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #28a745;">📅</div>
                <div class="number">${totalDays}</div>
                <div class="label">إجمالي الأيام</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #ffc107;">🗓️</div>
                <div class="number">${currentYearDays}</div>
                <div class="label">أيام ${currentYear}</div>
            </div>
        </div>
    `;
}
