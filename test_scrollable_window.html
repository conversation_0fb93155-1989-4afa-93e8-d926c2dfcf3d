<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة مع شريط التمرير</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn.success:hover {
            background: #1e7e34;
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .btn.warning:hover {
            background: #e0a800;
            box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
        }

        .btn.danger {
            background: #dc3545;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .btn.danger:hover {
            background: #c82333;
            box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .instructions {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .feature-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }

        .feature-box h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .feature-box ul {
            margin-right: 20px;
        }

        .feature-box li {
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📜 اختبار النافذة مع شريط التمرير</h1>
            <p>اختبار ميزة شريط التمرير في نافذة حساب الأيام القابلة للحركة</p>
        </div>

        <div class="content">
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h4>📋 تعليمات الاستخدام:</h4>
                <ul>
                    <li>اضغط على "فتح النافذة مع شريط التمرير" لفتح النافذة</li>
                    <li>لاحظ شريط التمرير الأزرق على الجانب الأيمن</li>
                    <li>استخدم شريط التمرير للتنقل عبر محتوى النافذة</li>
                    <li>يمكنك أيضاً سحب النافذة من شريط العنوان</li>
                    <li>شريط التمرير يظهر فقط عند الحاجة (عندما يكون المحتوى أطول من النافذة)</li>
                </ul>
            </div>

            <!-- مميزات شريط التمرير -->
            <div class="feature-box">
                <h4>🎨 مميزات شريط التمرير المخصص:</h4>
                <ul>
                    <li>لون أزرق متناسق مع تصميم النظام</li>
                    <li>عرض 12 بكسل مناسب للاستخدام</li>
                    <li>حواف مدورة لمظهر عصري</li>
                    <li>تأثير hover عند التمرير فوقه</li>
                    <li>يظهر على الجانب الأيمن (مناسب للغة العربية)</li>
                    <li>تصميم رفيع وأنيق</li>
                </ul>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn success" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="createTestProjects()">
                        إنشاء مشروعات تجريبية
                    </button>
                    <button class="btn warning" onclick="checkScrollSystem()">
                        فحص نظام التمرير
                    </button>
                </div>
            </div>

            <!-- قسم اختبار شريط التمرير -->
            <div class="test-section">
                <h3>📜 اختبار شريط التمرير</h3>
                <div class="grid">
                    <button class="btn" onclick="openScrollableWindow()">
                        فتح النافذة مع شريط التمرير
                    </button>
                    <button class="btn" onclick="fillWindowWithData()">
                        ملء النافذة بالبيانات
                    </button>
                    <button class="btn warning" onclick="testScrollFunctionality()">
                        اختبار وظائف التمرير
                    </button>
                    <button class="btn danger" onclick="closeWindow()">
                        إغلاق النافذة
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>🚀 اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn success" onclick="runAllScrollTests()">
                        تشغيل جميع اختبارات التمرير
                    </button>
                    <button class="btn" onclick="testDragAndScroll()">
                        اختبار السحب والتمرير معاً
                    </button>
                    <button class="btn warning" onclick="testResponsiveScroll()">
                        اختبار التمرير المتجاوب
                    </button>
                    <button class="btn danger" onclick="clearTestData()">
                        مسح البيانات التجريبية
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار النافذة مع شريط التمرير...
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 666,
                name: 'فاطمة أحمد (اختبار التمرير)',
                position: 'مصممة جرافيك',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 5500,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 666)) {
                employees.push(testEmployee);
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createTestProjects() {
            const testProjects = [
                { id: 501, name: 'مشروع اختبار التمرير 1', status: 'active' },
                { id: 502, name: 'مشروع اختبار التمرير 2', status: 'active' }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                }
            });

            updateStatus('تم إنشاء مشروعات اختبار التمرير بنجاح', 'success');
        }

        function checkScrollSystem() {
            const checks = [
                { name: 'نافذة حساب الأيام', test: () => document.getElementById('daysCalculationModal') !== null },
                { name: 'عنصر modal-body', test: () => {
                    const modal = document.getElementById('daysCalculationModal');
                    return modal && modal.querySelector('.modal-body') !== null;
                }},
                { name: 'CSS شريط التمرير', test: () => {
                    const styles = document.styleSheets;
                    for (let i = 0; i < styles.length; i++) {
                        try {
                            const rules = styles[i].cssRules || styles[i].rules;
                            for (let j = 0; j < rules.length; j++) {
                                if (rules[j].selectorText && rules[j].selectorText.includes('modal-body::-webkit-scrollbar')) {
                                    return true;
                                }
                            }
                        } catch (e) {
                            // تجاهل أخطاء CORS
                        }
                    }
                    return false;
                }},
                { name: 'خاصية overflow-y', test: () => {
                    const modal = document.getElementById('daysCalculationModal');
                    const modalBody = modal && modal.querySelector('.modal-body');
                    return modalBody && getComputedStyle(modalBody).overflowY === 'auto';
                }}
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                } else {
                    results.push(`✗ ${check.name}`);
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص نظام التمرير: ${passed}/${checks.length} عناصر موجودة`, status);
            
            console.log('نتائج فحص نظام التمرير:');
            results.forEach(result => console.log(result));
        }

        function openScrollableWindow() {
            if (!employees.find(emp => emp.id === 666)) {
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            openDaysCalculationModal(666);
            updateStatus('تم فتح النافذة مع شريط التمرير - لاحظ شريط التمرير على اليمين!', 'success');
        }

        function fillWindowWithData() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            // ملء البيانات لجعل النافذة تحتاج للتمرير
            const projectSelect = document.getElementById('daysProject');
            if (projectSelect && projectSelect.options.length > 1) {
                projectSelect.selectedIndex = 1;
            }
            
            document.getElementById('calculatedDays').value = '25';
            document.getElementById('absenceDays').value = '2';
            document.getElementById('deductionType').value = 'hours';
            document.getElementById('deductionAmount').value = '4';
            document.getElementById('overtimeHours').value = '8';
            
            // تشغيل الحساب
            if (typeof calculateActualDays === 'function') {
                calculateActualDays();
            }

            updateStatus('تم ملء النافذة بالبيانات - جرب التمرير الآن!', 'success');
        }

        function testScrollFunctionality() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const modalBody = modal.querySelector('.modal-body');
            if (modalBody) {
                // فحص إذا كان التمرير متاح
                const hasScroll = modalBody.scrollHeight > modalBody.clientHeight;
                
                if (hasScroll) {
                    updateStatus('✓ شريط التمرير متاح ويعمل بشكل صحيح', 'success');
                    
                    // تحريك التمرير تلقائياً للتوضيح
                    modalBody.scrollTop = modalBody.scrollHeight / 2;
                    
                    setTimeout(() => {
                        modalBody.scrollTop = 0;
                    }, 1000);
                } else {
                    updateStatus('⚠️ المحتوى لا يحتاج للتمرير - جرب إضافة المزيد من البيانات', 'warning');
                }
            } else {
                updateStatus('خطأ: لم يتم العثور على منطقة التمرير', 'error');
            }
        }

        function testDragAndScroll() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            updateStatus('✓ يمكنك الآن سحب النافذة من شريط العنوان والتمرير داخل المحتوى', 'success');
            
            // إضافة تأثير بصري
            const header = modal.querySelector('.draggable-header');
            const modalBody = modal.querySelector('.modal-body');
            
            if (header) {
                header.style.animation = 'pulse 2s infinite';
                setTimeout(() => {
                    header.style.animation = '';
                }, 3000);
            }
            
            if (modalBody) {
                modalBody.style.border = '2px solid #007bff';
                setTimeout(() => {
                    modalBody.style.border = '';
                }, 3000);
            }
        }

        function testResponsiveScroll() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const modalContent = modal.querySelector('.draggable-modal');
            if (modalContent) {
                // تغيير حجم النافذة لاختبار التجاوب
                modalContent.style.height = '400px';
                
                setTimeout(() => {
                    modalContent.style.height = '600px';
                }, 1000);
                
                setTimeout(() => {
                    modalContent.style.height = '';
                }, 2000);
                
                updateStatus('✓ تم اختبار التمرير المتجاوب - لاحظ تغيير شريط التمرير', 'success');
            }
        }

        function runAllScrollTests() {
            updateStatus('جاري تشغيل جميع اختبارات التمرير...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => checkScrollSystem(), 1500);
            setTimeout(() => openScrollableWindow(), 2500);
            setTimeout(() => fillWindowWithData(), 3500);
            setTimeout(() => testScrollFunctionality(), 4500);
            setTimeout(() => testDragAndScroll(), 5500);
            setTimeout(() => testResponsiveScroll(), 6500);

            setTimeout(() => {
                updateStatus('انتهت جميع اختبارات التمرير بنجاح! 🎉', 'success');
            }, 8000);
        }

        function closeWindow() {
            const modal = document.getElementById('daysCalculationModal');
            if (modal) {
                modal.style.display = 'none';
                updateStatus('تم إغلاق النافذة', 'info');
            }
        }

        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار التمرير؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 666);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![501, 502].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 666);
                }

                updateStatus('تم مسح جميع بيانات اختبار التمرير', 'warning');
            }
        }

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
                100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
            }
        `;
        document.head.appendChild(style);

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار النافذة مع شريط التمرير - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
