<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة الحوافز والمكافآت المبسطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .feature-card h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list .icon {
            color: #17a2b8;
            font-size: 16px;
        }

        .test-button {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 14px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(23,162,184,0.3);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23,162,184,0.4);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
            box-shadow: 0 2px 8px rgba(40,167,69,0.3);
        }

        .test-button.success:hover {
            box-shadow: 0 4px 12px rgba(40,167,69,0.4);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            box-shadow: 0 2px 8px rgba(255,193,7,0.3);
        }

        .test-button.warning:hover {
            box-shadow: 0 4px 12px rgba(255,193,7,0.4);
        }

        .demo-employee {
            background: linear-gradient(135deg, #e9ecef, #f8f9fa);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 20px;
            border: 2px solid #e9ecef;
        }

        .demo-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(23,162,184,0.3);
        }

        .demo-info {
            flex: 1;
        }

        .demo-info h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 18px;
        }

        .demo-info p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }

        .results-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-result {
            padding: 12px 16px;
            margin: 8px 0;
            border-radius: 8px;
            font-size: 14px;
            border-right: 4px solid;
            transition: all 0.2s ease;
        }

        .test-result:hover {
            transform: translateX(-2px);
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border-color: #28a745;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
        }

        .grid-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight-box h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 18px;
        }

        .highlight-box p {
            margin: 0;
            color: #856404;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-gift"></i> نافذة الحوافز والمكافآت المبسطة</h1>
            <p>واجهة بسيطة وسهلة الاستخدام مع زر حفظ وسجل كامل</p>
        </div>

        <div class="content">
            <!-- الميزات الجديدة -->
            <div class="feature-card">
                <h2><i class="fas fa-star"></i> الميزات الجديدة</h2>

                <ul class="feature-list">
                    <li><i class="fas fa-check icon"></i> <strong>واجهة مبسطة:</strong> تصميم نظيف وسهل الاستخدام</li>
                    <li><i class="fas fa-check icon"></i> <strong>حقل اختيار النوع:</strong> حافز 🏆 أو مكافأة 🎁</li>
                    <li><i class="fas fa-check icon"></i> <strong>زر حفظ واضح:</strong> زر كبير وبارز للحفظ</li>
                    <li><i class="fas fa-check icon"></i> <strong>سجل كامل:</strong> عرض جميع السجلات في جدول منظم</li>
                    <li><i class="fas fa-check icon"></i> <strong>ألوان مميزة:</strong> أخضر للحوافز، أزرق للمكافآت</li>
                    <li><i class="fas fa-check icon"></i> <strong>تاريخ تلقائي:</strong> الشهر والسنة الحاليين كافتراضي</li>
                </ul>

                <div class="highlight-box">
                    <h3>🎯 التحسينات الرئيسية</h3>
                    <p>تم تبسيط النافذة لتكون أكثر سهولة في الاستخدام مع الحفاظ على جميع الوظائف المطلوبة</p>
                </div>
            </div>

            <!-- موظف تجريبي -->
            <div class="feature-card">
                <h2><i class="fas fa-user-tie"></i> موظف تجريبي للاختبار</h2>

                <div class="demo-employee">
                    <div class="demo-photo">سم</div>
                    <div class="demo-info">
                        <h4>سارة محمد أحمد</h4>
                        <p><strong>الكود:</strong> EMP002 | <strong>المنصب:</strong> محاسبة | <strong>النوع:</strong> شهري</p>
                        <p><strong>الراتب الأساسي:</strong> 7,500 ج.م</p>
                    </div>
                    <div>
                        <button class="test-button success" onclick="createTestEmployee()">
                            <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                        </button>
                    </div>
                </div>
            </div>

            <!-- اختبارات النافذة -->
            <div class="feature-card">
                <h2><i class="fas fa-flask"></i> اختبارات النافذة</h2>

                <div class="grid-buttons">
                    <button class="test-button" onclick="testIncentivesWindow()">
                        <i class="fas fa-window-maximize"></i> فتح النافذة
                    </button>
                    <button class="test-button success" onclick="addTestData()">
                        <i class="fas fa-database"></i> إضافة بيانات تجريبية
                    </button>
                    <button class="test-button warning" onclick="testSaveFunction()">
                        <i class="fas fa-save"></i> اختبار الحفظ
                    </button>
                    <button class="test-button" onclick="openOriginalApp()">
                        <i class="fas fa-external-link-alt"></i> التطبيق الأصلي
                    </button>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="test-button success" onclick="runFullTest()" style="font-size: 18px; padding: 16px 32px;">
                        <i class="fas fa-rocket"></i> تشغيل اختبار شامل
                    </button>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">🚀 جاهز لاختبار نافذة الحوافز والمكافآت المبسطة...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-clock"></i> ${new Date().toLocaleTimeString('ar-EG')} - ${message}`;
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 8888,
                name: 'سارة محمد أحمد',
                position: 'محاسبة',
                employeeCode: 'EMP002',
                nationalId: '29876543210987',
                phone: '01098765432',
                employmentType: 'monthly',
                basicSalary: 7500,
                photo: 'https://randomuser.me/api/portraits/women/25.jpg'
            };

            // إضافة الموظف إلى القائمة
            const existingIndex = employees.findIndex(emp => emp.id === 8888);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي الموجود', 'warning');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء موظف تجريبي جديد', 'success');
            }

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult(`✅ تم حفظ الموظف: ${testEmployee.name}`, 'success');
        }

        // دالة لاختبار نافذة الحوافز والمكافآت
        function testIncentivesWindow() {
            addResult('🔍 اختبار فتح نافذة الحوافز والمكافآت...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 8888);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(8888);
                addResult('✅ تم فتح نافذة الحوافز والمكافآت بنجاح', 'success');
                addResult('👀 تحقق من التصميم المبسط والواجهة السهلة', 'info');
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        // دالة لإضافة بيانات تجريبية
        function addTestData() {
            addResult('📊 إضافة بيانات تجريبية...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 8888);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                // إضافة حوافز ومكافآت تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 8888,
                        type: 'incentive',
                        month: 10,
                        year: 2024,
                        amount: 1000,
                        description: 'حافز الأداء المتميز',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 8888,
                        type: 'reward',
                        month: 11,
                        year: 2024,
                        amount: 1500,
                        description: 'مكافأة إنجاز المشروع',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 8888,
                        type: 'incentive',
                        month: 12,
                        year: 2024,
                        amount: 800,
                        description: 'حافز نهاية العام',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];

                // إضافة السجلات
                testRecords.forEach(record => {
                    incentivesRewardsData.push(record);
                });

                // حفظ البيانات
                saveIncentivesRewardsToLocalStorage();

                addResult(`✅ تم إضافة ${testRecords.length} سجل تجريبي`, 'success');
                addResult('🎯 يمكنك الآن فتح النافذة لرؤية البيانات', 'info');

            } catch (error) {
                addResult(`❌ خطأ في إضافة البيانات: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار وظيفة الحفظ
        function testSaveFunction() {
            addResult('💾 اختبار وظيفة الحفظ...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 8888);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            // فتح النافذة أولاً
            try {
                viewIncentivesRewards(8888);
                addResult('✅ تم فتح النافذة للاختبار', 'success');

                setTimeout(() => {
                    addResult('📝 تعليمات الاختبار:', 'info');
                    addResult('1. اختر نوع العملية (حافز أو مكافأة)', 'info');
                    addResult('2. أدخل المبلغ والوصف', 'info');
                    addResult('3. اضغط على زر "💾 حفظ"', 'info');
                    addResult('4. تحقق من ظهور السجل في الجدول', 'info');
                }, 500);

            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        // دالة لفتح التطبيق الأصلي
        function openOriginalApp() {
            addResult('🌐 فتح التطبيق الأصلي...', 'info');
            window.open('index.html', '_blank');
            addResult('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
        }

        // دالة لتشغيل اختبار شامل
        function runFullTest() {
            addResult('🚀 بدء الاختبار الشامل...', 'info');

            // مسح النتائج السابقة
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';

            // الخطوة 1: إنشاء موظف تجريبي
            setTimeout(() => {
                addResult('الخطوة 1: إنشاء موظف تجريبي', 'info');
                createTestEmployee();
            }, 500);

            // الخطوة 2: إضافة بيانات تجريبية
            setTimeout(() => {
                addResult('الخطوة 2: إضافة بيانات تجريبية', 'info');
                addTestData();
            }, 1500);

            // الخطوة 3: فتح النافذة
            setTimeout(() => {
                addResult('الخطوة 3: فتح نافذة الحوافز والمكافآت', 'info');
                testIncentivesWindow();
            }, 2500);

            // الخطوة 4: تعليمات الاختبار اليدوي
            setTimeout(() => {
                addResult('الخطوة 4: اختبار يدوي', 'warning');
                addResult('🎯 تحقق من الميزات التالية:', 'info');
                addResult('• التصميم المبسط والواضح', 'info');
                addResult('• حقل اختيار النوع (حافز/مكافأة)', 'info');
                addResult('• زر الحفظ الواضح والبارز', 'info');
                addResult('• عرض السجلات في جدول منظم', 'info');
                addResult('• الألوان المميزة للحوافز والمكافآت', 'info');
                addResult('• إمكانية حذف السجلات', 'info');
                addResult('✅ الاختبار الشامل مكتمل!', 'success');
            }, 3500);
        }

        // تشغيل فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎉 مرحباً بك في اختبار نافذة الحوافز والمكافآت المبسطة', 'success');
            addResult('📋 هذه النافذة تتميز بالبساطة وسهولة الاستخدام', 'info');

            // فحص سريع للنظام
            setTimeout(() => {
                addResult('🔍 فحص سريع للنظام...', 'info');

                // فحص المتغيرات والدوال الأساسية
                const checks = [
                    { name: 'employees', check: () => typeof employees !== 'undefined' },
                    { name: 'incentivesRewardsData', check: () => typeof incentivesRewardsData !== 'undefined' },
                    { name: 'viewIncentivesRewards', check: () => typeof viewIncentivesRewards === 'function' },
                    { name: 'saveIncentiveReward', check: () => typeof saveIncentiveReward === 'function' },
                    { name: 'deleteIncentiveReward', check: () => typeof deleteIncentiveReward === 'function' }
                ];

                let allPassed = true;
                checks.forEach(check => {
                    if (check.check()) {
                        addResult(`✅ ${check.name}: متاح`, 'success');
                    } else {
                        addResult(`❌ ${check.name}: غير متاح`, 'error');
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    addResult('🎯 النظام جاهز للاختبار!', 'success');
                    addResult('💡 ابدأ بإنشاء موظف تجريبي ثم فتح النافذة', 'info');
                } else {
                    addResult('⚠️ هناك مشاكل في النظام', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
