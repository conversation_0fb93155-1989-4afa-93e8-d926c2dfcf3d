<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة حساب الأيام الجديدة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #007bff;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-top: 20px;
        }

        .stats h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .stats .number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calendar-check"></i> اختبار نافذة حساب الأيام الجديدة</h1>
            <p>اختبار شامل للنافذة المحسنة مع التصميم الجديد والوظائف المطورة</p>
        </div>

        <div class="content">
            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3><i class="fas fa-cog"></i> إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn" onclick="createTestEmployee()">
                        <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                    </button>
                    <button class="btn secondary" onclick="createTestProjects()">
                        <i class="fas fa-project-diagram"></i> إنشاء مشروعات تجريبية
                    </button>
                    <button class="btn warning" onclick="checkSystemStatus()">
                        <i class="fas fa-heartbeat"></i> فحص حالة النظام
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات -->
            <div class="test-section">
                <h3><i class="fas fa-play"></i> اختبارات النافذة</h3>
                <div class="grid">
                    <button class="btn" onclick="testOpenWindow()">
                        <i class="fas fa-window-maximize"></i> اختبار فتح النافذة
                    </button>
                    <button class="btn secondary" onclick="testFillData()">
                        <i class="fas fa-edit"></i> اختبار ملء البيانات
                    </button>
                    <button class="btn warning" onclick="testSaveFunction()">
                        <i class="fas fa-save"></i> اختبار دالة الحفظ
                    </button>
                    <button class="btn danger" onclick="testViewRecords()">
                        <i class="fas fa-list"></i> اختبار عرض السجلات
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3><i class="fas fa-rocket"></i> اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn" onclick="runFullTest()">
                        <i class="fas fa-play-circle"></i> تشغيل الاختبار الشامل
                    </button>
                    <button class="btn secondary" onclick="testCalculations()">
                        <i class="fas fa-calculator"></i> اختبار الحسابات
                    </button>
                    <button class="btn warning" onclick="testValidation()">
                        <i class="fas fa-check-circle"></i> اختبار التحقق
                    </button>
                    <button class="btn danger" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong><i class="fas fa-info-circle"></i> الحالة:</strong>
                <span>جاهز لبدء الاختبارات...</span>
            </div>

            <!-- إحصائيات -->
            <div class="stats">
                <h4><i class="fas fa-chart-bar"></i> إحصائيات الاختبار</h4>
                <div class="grid">
                    <div>
                        <div class="number" id="testsRun">0</div>
                        <div>اختبارات تم تشغيلها</div>
                    </div>
                    <div>
                        <div class="number" id="testsSuccess">0</div>
                        <div>اختبارات ناجحة</div>
                    </div>
                    <div>
                        <div class="number" id="testsFailed">0</div>
                        <div>اختبارات فاشلة</div>
                    </div>
                    <div>
                        <div class="number" id="successRate">0%</div>
                        <div>معدل النجاح</div>
                    </div>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry info">🚀 تم تحميل صفحة اختبار نافذة حساب الأيام الجديدة</div>
                <div class="log-entry info">📋 جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong><i class="fas fa-${getStatusIcon(type)}"></i> الحالة:</strong> <span>${message}</span>`;
        }

        function getStatusIcon(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-triangle',
                warning: 'exclamation-circle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsSuccess').textContent = testsSuccess;
            document.getElementById('testsFailed').textContent = testsFailed;
            
            const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function runTest(testName, testFunction) {
            testsRun++;
            addLog(`🧪 بدء اختبار: ${testName}`, 'info');
            
            try {
                const result = testFunction();
                if (result !== false) {
                    testsSuccess++;
                    addLog(`✅ نجح اختبار: ${testName}`, 'success');
                } else {
                    testsFailed++;
                    addLog(`❌ فشل اختبار: ${testName}`, 'error');
                }
            } catch (error) {
                testsFailed++;
                addLog(`❌ خطأ في اختبار ${testName}: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // دوال الاختبار
        function createTestEmployee() {
            runTest('إنشاء موظف تجريبي', () => {
                const testEmployee = {
                    id: 12345,
                    name: 'أحمد محمد التجريبي',
                    position: 'مهندس مدني',
                    employmentType: 'monthly',
                    nationalId: '12345678901234',
                    phone: '01234567890',
                    basicSalary: 5000,
                    isActive: true
                };

                if (!employees.find(emp => emp.id === 12345)) {
                    employees.push(testEmployee);
                    addLog('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                    return true;
                } else {
                    addLog('ℹ️ الموظف التجريبي موجود بالفعل', 'info');
                    updateStatus('الموظف التجريبي موجود بالفعل', 'info');
                    return true;
                }
            });
        }

        function createTestProjects() {
            runTest('إنشاء مشروعات تجريبية', () => {
                const testProjects = [
                    { id: 101, name: 'مشروع البناء التجريبي', status: 'in-progress' },
                    { id: 102, name: 'مشروع الطرق التجريبي', status: 'planning' },
                    { id: 103, name: 'مشروع المجمع التجريبي', status: 'active' }
                ];

                testProjects.forEach(project => {
                    if (!projects.find(p => p.id === project.id)) {
                        projects.push(project);
                    }
                });

                addLog('✅ تم إنشاء المشروعات التجريبية بنجاح', 'success');
                updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');
                return true;
            });
        }

        function checkSystemStatus() {
            runTest('فحص حالة النظام', () => {
                const checks = [
                    { name: 'وجود دالة openDaysCalculationModal', test: () => typeof openDaysCalculationModal === 'function' },
                    { name: 'وجود دالة saveDaysCalculation', test: () => typeof saveDaysCalculation === 'function' },
                    { name: 'وجود دالة viewDaysRecords', test: () => typeof viewDaysRecords === 'function' },
                    { name: 'وجود دالة calculateActualDays', test: () => typeof calculateActualDays === 'function' },
                    { name: 'وجود نافذة حساب الأيام', test: () => document.getElementById('daysCalculationModal') !== null },
                    { name: 'وجود موظفين', test: () => employees && employees.length > 0 },
                    { name: 'وجود مشروعات', test: () => projects && projects.length > 0 }
                ];

                let passed = 0;
                checks.forEach(check => {
                    if (check.test()) {
                        addLog(`✅ ${check.name}`, 'success');
                        passed++;
                    } else {
                        addLog(`❌ ${check.name}`, 'error');
                    }
                });

                const status = passed === checks.length ? 'success' : 'warning';
                updateStatus(`فحص النظام: ${passed}/${checks.length} اختبارات نجحت`, status);
                return passed === checks.length;
            });
        }

        function testOpenWindow() {
            runTest('فتح نافذة حساب الأيام', () => {
                if (!employees.find(emp => emp.id === 12345)) {
                    addLog('⚠️ يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return false;
                }

                openDaysCalculationModal(12345);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysCalculationModal');
                    if (modal && modal.style.display === 'block') {
                        addLog('✅ تم فتح النافذة بنجاح', 'success');
                        updateStatus('تم فتح نافذة حساب الأيام بنجاح', 'success');
                    } else {
                        addLog('❌ فشل في فتح النافذة', 'error');
                        updateStatus('فشل في فتح نافذة حساب الأيام', 'error');
                    }
                }, 500);

                return true;
            });
        }

        function testFillData() {
            runTest('ملء البيانات التجريبية', () => {
                const modal = document.getElementById('daysCalculationModal');
                if (!modal || modal.style.display !== 'block') {
                    addLog('⚠️ يرجى فتح النافذة أولاً', 'warning');
                    return false;
                }

                // ملء البيانات
                const projectSelect = document.getElementById('daysProject');
                const calculatedDaysInput = document.getElementById('calculatedDays');
                const absenceDaysInput = document.getElementById('absenceDays');
                const overtimeHoursInput = document.getElementById('overtimeHours');

                if (projectSelect && projectSelect.options.length > 1) {
                    projectSelect.selectedIndex = 1;
                }
                if (calculatedDaysInput) calculatedDaysInput.value = '22';
                if (absenceDaysInput) absenceDaysInput.value = '2';
                if (overtimeHoursInput) overtimeHoursInput.value = '8';

                // تشغيل حساب الأيام الفعلية
                if (typeof calculateActualDays === 'function') {
                    calculateActualDays();
                }

                addLog('✅ تم ملء البيانات التجريبية', 'success');
                updateStatus('تم ملء البيانات التجريبية في النافذة', 'success');
                return true;
            });
        }

        function testSaveFunction() {
            runTest('اختبار دالة الحفظ', () => {
                const modal = document.getElementById('daysCalculationModal');
                if (!modal || modal.style.display !== 'block') {
                    addLog('⚠️ يرجى فتح النافذة وملء البيانات أولاً', 'warning');
                    return false;
                }

                const result = saveDaysCalculation(12345);
                if (result !== false) {
                    addLog('✅ تم حفظ البيانات بنجاح', 'success');
                    updateStatus('تم اختبار دالة الحفظ بنجاح', 'success');
                    return true;
                } else {
                    addLog('❌ فشل في حفظ البيانات', 'error');
                    updateStatus('فشل في اختبار دالة الحفظ', 'error');
                    return false;
                }
            });
        }

        function testViewRecords() {
            runTest('اختبار عرض السجلات', () => {
                if (!employees.find(emp => emp.id === 12345)) {
                    addLog('⚠️ يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return false;
                }

                // فتح النافذة أولاً
                openDaysCalculationModal(12345);
                
                setTimeout(() => {
                    viewDaysRecords();
                    addLog('✅ تم اختبار عرض السجلات', 'success');
                    updateStatus('تم اختبار عرض السجلات بنجاح', 'success');
                }, 500);

                return true;
            });
        }

        function testCalculations() {
            runTest('اختبار الحسابات', () => {
                // اختبار دالة حساب الأيام الفعلية
                if (typeof calculateActualDays !== 'function') {
                    addLog('❌ دالة calculateActualDays غير موجودة', 'error');
                    return false;
                }

                // إنشاء حقول وهمية للاختبار
                const testContainer = document.createElement('div');
                testContainer.innerHTML = `
                    <input type="number" id="calculatedDays" value="25">
                    <input type="number" id="absenceDays" value="3">
                    <input type="number" id="actualDaysDisplay" readonly>
                `;
                document.body.appendChild(testContainer);

                calculateActualDays();

                const actualDaysField = document.getElementById('actualDaysDisplay');
                const expectedResult = 22; // 25 - 3
                const actualResult = parseInt(actualDaysField.value);

                document.body.removeChild(testContainer);

                if (actualResult === expectedResult) {
                    addLog(`✅ حساب الأيام الفعلية صحيح: ${actualResult}`, 'success');
                    updateStatus('اختبار الحسابات نجح', 'success');
                    return true;
                } else {
                    addLog(`❌ خطأ في حساب الأيام الفعلية: توقع ${expectedResult} لكن حصل على ${actualResult}`, 'error');
                    updateStatus('اختبار الحسابات فشل', 'error');
                    return false;
                }
            });
        }

        function testValidation() {
            runTest('اختبار التحقق من البيانات', () => {
                // اختبار التحقق من البيانات الناقصة
                const result = saveDaysCalculation(); // بدون معرف موظف
                
                if (result === false) {
                    addLog('✅ التحقق من البيانات يعمل بشكل صحيح', 'success');
                    updateStatus('اختبار التحقق نجح', 'success');
                    return true;
                } else {
                    addLog('❌ التحقق من البيانات لا يعمل بشكل صحيح', 'error');
                    updateStatus('اختبار التحقق فشل', 'error');
                    return false;
                }
            });
        }

        function runFullTest() {
            addLog('🚀 بدء الاختبار الشامل...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => checkSystemStatus(), 1500);
            setTimeout(() => testOpenWindow(), 2000);
            setTimeout(() => testFillData(), 3000);
            setTimeout(() => testSaveFunction(), 4000);
            setTimeout(() => testViewRecords(), 5000);
            setTimeout(() => testCalculations(), 6000);
            setTimeout(() => testValidation(), 7000);

            setTimeout(() => {
                addLog('🎉 انتهى الاختبار الشامل', 'success');
                const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 0;
                updateStatus(`انتهى الاختبار الشامل - معدل النجاح: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
            }, 8000);
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح الموظفين التجريبيين
                employees = employees.filter(emp => emp.id !== 12345);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![101, 102, 103].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 12345);
                }

                // إعادة تعيين الإحصائيات
                testsRun = 0;
                testsSuccess = 0;
                testsFailed = 0;
                updateStats();

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('🗑️ تم مسح جميع البيانات التجريبية', 'warning');
                updateStatus('تم مسح جميع البيانات التجريبية', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('📋 تم تحميل صفحة اختبار نافذة حساب الأيام الجديدة', 'info');
            updateStatus('جاهز لبدء الاختبارات - اضغط على الأزرار أعلاه', 'info');
        });
    </script>
</body>
</html>
