<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر صافي المرتب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #ddd;
        }

        .header {
            background: #f8f9fa;
            color: #333;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }

        .header h1 {
            font-size: 20px;
            margin-bottom: 8px;
            color: #333;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .content {
            padding: 20px;
        }

        .test-section {
            background: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-button {
            background: #333;
            color: white;
            border: 1px solid #333;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            margin: 4px;
            transition: background 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .test-button:hover {
            background: #555;
        }

        .test-button.secondary {
            background: #666;
            border-color: #666;
        }

        .test-button.secondary:hover {
            background: #777;
        }

        .results-container {
            background: white;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #ddd;
            max-height: 350px;
            overflow-y: auto;
        }

        .test-result {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 3px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-result.success {
            background: #f0f8f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .test-result.error {
            background: #f8f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .test-result.info {
            background: #f0f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .status {
            padding: 6px 10px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            margin: 4px 0;
        }

        .status.success {
            background: #f0f8f0;
            color: #333;
        }

        .status.error {
            background: #f8f0f0;
            color: #333;
        }

        .employee-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-width: 300px;
        }

        .employee-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .employee-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
        }

        .net-salary-btn {
            width: 100%;
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .net-salary-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>اختبار زر صافي المرتب</h1>
            <p>اختبار شامل لزر صافي المرتب الجديد مع نافذة التحكم في الأعمدة</p>
        </div>

        <div class="content">
            <!-- قسم إنشاء البيانات التجريبية -->
            <div class="test-section">
                <h3>إعداد البيانات التجريبية</h3>
                <button class="test-button" onclick="createTestEmployee()">
                    إنشاء موظف شهري
                </button>
                <button class="test-button" onclick="createDailyEmployee()">
                    إنشاء موظف يومي
                </button>
                <button class="test-button" onclick="addTestWorkDays()">
                    إضافة أيام عمل
                </button>
                <button class="test-button" onclick="addTestIncentives()">
                    إضافة حوافز
                </button>
                <button class="test-button" onclick="addTestAdvances()">
                    إضافة سلف وخصومات
                </button>
            </div>

            <!-- قسم اختبار الزر -->
            <div class="test-section">
                <h3>اختبار زر صافي المرتب</h3>
                <button class="test-button" onclick="testNetSalaryButton()">
                    اختبار الزر
                </button>
                <button class="test-button secondary" onclick="testColumnToggle()">
                    اختبار إخفاء الأعمدة
                </button>
                <button class="test-button secondary" onclick="testDailyWageSource()">
                    اختبار مصدر الأجر اليومي
                </button>
                <button class="test-button secondary" onclick="testAllowancesCalculation()">
                    اختبار حساب البدلات
                </button>
                <button class="test-button secondary" onclick="debugSalaryData()">
                    طباعة بيانات الراتب
                </button>
                <button class="test-button secondary" onclick="fixEmployeeAllowances()">
                    إصلاح البدلات مباشرة
                </button>
                <button class="test-button secondary" onclick="deepDiagnosis()">
                    تشخيص عميق للمشكلة
                </button>
                <button class="test-button secondary" onclick="clearResults()">
                    مسح النتائج
                </button>
            </div>

            <!-- معاينة بطاقة الموظف -->
            <div class="test-section">
                <h3>معاينة بطاقة الموظف</h3>
                <div id="employee-preview" class="employee-preview" style="display: none;">
                    <div class="employee-name" id="preview-name">اسم الموظف</div>
                    <div class="employee-info" id="preview-info">معلومات الموظف</div>
                    <button class="net-salary-btn" onclick="testNetSalaryModal()">
                        <span>صافي المرتب</span>
                    </button>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3>سجل نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">جاهز لبدء اختبار زر صافي المرتب...</div>
                </div>
            </div>

            <!-- حالة الاختبارات -->
            <div style="margin-top: 15px;">
                <div id="employee-status" class="status">في انتظار إنشاء الموظف التجريبي...</div>
                <div id="data-status" class="status">في انتظار إضافة البيانات...</div>
                <div id="button-status" class="status">في انتظار اختبار الزر...</div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // متغير لحفظ معرف الموظف التجريبي
        let testEmployeeId = null;

        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            const icon = type === 'success' ? '✓' : 
                        type === 'error' ? '✗' : '•';
            
            resultDiv.innerHTML = `${icon} ${message}`;
            resultsContainer.appendChild(resultDiv);
            
            // التمرير إلى أسفل
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لتحديث حالة الاختبار
        function updateStatus(statusId, success, message) {
            const statusElement = document.getElementById(statusId);
            if (statusElement) {
                statusElement.className = `status ${success ? 'success' : 'error'}`;
                statusElement.textContent = message;
            }
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 9998,
                name: 'سارة أحمد محمد',
                position: 'محاسبة',
                employeeCode: 'ACC-002',
                employmentType: 'monthly',
                basicSalary: 6000,
                salary: {
                    basic: 6000,
                    total: 6000,
                    allowances: {
                        total: 800,
                        housing: 500,
                        transport: 200,
                        food: 100,
                        other: 0
                    },
                    dailyAllowances: {
                        expatDaily: 40,
                        transportDaily: 25
                    }
                },
                isActive: true,
                dateAdded: new Date().toISOString()
            };

            // إضافة الموظف إلى المصفوفة
            const existingIndex = employees.findIndex(emp => emp.id === 9998);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي', 'success');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء الموظف التجريبي بنجاح', 'success');
            }

            testEmployeeId = 9998;
            updateStatus('employee-status', true, 'تم إنشاء الموظف التجريبي');

            // عرض معاينة البطاقة
            showEmployeePreview(testEmployee);

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult('تم حفظ بيانات الموظف', 'success');
        }

        // دالة لعرض معاينة بطاقة الموظف
        function showEmployeePreview(employee) {
            const preview = document.getElementById('employee-preview');
            const nameElement = document.getElementById('preview-name');
            const infoElement = document.getElementById('preview-info');

            nameElement.textContent = employee.name;
            infoElement.innerHTML = `
                كود: ${employee.employeeCode} | ${employee.position}<br>
                ${employee.employmentType === 'daily' ? 'يومي' : 'شهري'}
            `;

            preview.style.display = 'block';
        }

        // دالة لإنشاء موظف يومي تجريبي
        function createDailyEmployee() {
            addResult('إنشاء موظف يومي تجريبي...', 'info');

            const testEmployee = {
                id: 9999,
                name: 'محمد علي حسن',
                position: 'عامل بناء',
                employeeCode: 'WORK-003',
                employmentType: 'daily',
                dailyWage: 180, // للتوافق مع النظام القديم
                salary: {
                    dailyWage: 180, // الأجر اليومي من نافذة إدارة الراتب
                    basic: 180,
                    total: 180
                },
                isActive: true
            };

            // إضافة الموظف إلى المصفوفة
            const existingIndex = employees.findIndex(emp => emp.id === 9999);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف اليومي التجريبي', 'success');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء الموظف اليومي التجريبي بنجاح', 'success');
            }

            testEmployeeId = 9999;
            updateStatus('employee-status', true, 'تم إنشاء الموظف اليومي التجريبي');

            // عرض معاينة البطاقة
            showEmployeePreview(testEmployee);

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult(`تم حفظ بيانات الموظف اليومي - أجر يومي من نافذة الراتب: ${testEmployee.salary.dailyWage} ج.م`, 'success');
        }

        // دالة لإضافة أيام عمل تجريبية
        function addTestWorkDays() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة أيام عمل تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            // إضافة أيام عمل لمشروعات مختلفة
            const workDaysRecords = [
                {
                    id: Date.now() + 1,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    projectId: 'proj1',
                    projectName: 'مشروع المكاتب الإدارية',
                    scheduledDays: 22,
                    actualDays: 20,
                    overtimeHours: 12,
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    projectId: 'proj2',
                    projectName: 'مشروع المجمع السكني',
                    scheduledDays: 8,
                    actualDays: 8,
                    overtimeHours: 6,
                    createdAt: new Date().toISOString()
                }
            ];

            // إضافة السجلات
            workDaysRecords.forEach(record => {
                const existingIndex = employeeDaysData.findIndex(r =>
                    r.employeeId === record.employeeId &&
                    r.projectId === record.projectId &&
                    r.month === record.month &&
                    r.year === record.year
                );

                if (existingIndex !== -1) {
                    employeeDaysData[existingIndex] = record;
                } else {
                    employeeDaysData.push(record);
                }
            });

            addResult('تم إضافة أيام العمل لمشروعين', 'success');
            addResult(`المشروع الأول: ${workDaysRecords[0].actualDays} يوم + ${workDaysRecords[0].overtimeHours} ساعة إضافية`, 'info');
            addResult(`المشروع الثاني: ${workDaysRecords[1].actualDays} يوم + ${workDaysRecords[1].overtimeHours} ساعة إضافية`, 'info');

            updateStatus('data-status', true, 'تم إضافة أيام العمل');
        }

        // دالة لإضافة حوافز تجريبية
        function addTestIncentives() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة حوافز ومكافآت تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const incentiveRecords = [
                {
                    id: Date.now() + 1,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    category: 'incentive',
                    type: 'performance',
                    amount: 1200,
                    status: 'active',
                    description: 'حافز أداء ممتاز',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    category: 'bonus',
                    type: 'project',
                    amount: 500,
                    status: 'active',
                    description: 'مكافأة إنجاز مشروع',
                    createdAt: new Date().toISOString()
                }
            ];

            incentiveRecords.forEach(record => incentivesData.push(record));
            addResult('تم إضافة حافزين بقيمة إجمالية 1700 ج.م', 'success');
        }

        // دالة لإضافة سلف وخصومات تجريبية
        function addTestAdvances() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة سلف وخصومات تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const records = [
                {
                    id: Date.now() + 1,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    type: 'advance',
                    amount: 1500,
                    date: currentDate.toISOString().split('T')[0],
                    notes: 'سلفة شهرية',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    type: 'deduction',
                    deductionSubtype: 'insurance',
                    amount: 300,
                    date: currentDate.toISOString().split('T')[0],
                    notes: 'خصم تأمين',
                    createdAt: new Date().toISOString()
                }
            ];

            records.forEach(record => advancesDeductionsData.push(record));
            addResult('تم إضافة سلفة 1500 ج.م وخصم 300 ج.م', 'success');
        }

        // دالة لاختبار زر صافي المرتب
        function testNetSalaryButton() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('اختبار زر صافي المرتب...', 'info');

            try {
                // فتح النافذة
                openNetSalaryModal(testEmployeeId);
                addResult('تم فتح نافذة صافي المرتب بنجاح', 'success');

                // التحقق من وجود النافذة
                setTimeout(() => {
                    const modal = document.getElementById('netSalaryModal');
                    if (modal) {
                        addResult('النافذة موجودة ومعروضة بشكل صحيح', 'success');
                        addResult('تم عرض جدول تفاصيل المرتب مع جميع الأعمدة', 'success');
                        updateStatus('button-status', true, 'الزر يعمل بشكل صحيح');
                    } else {
                        addResult('فشل في العثور على النافذة', 'error');
                        updateStatus('button-status', false, 'فشل في فتح النافذة');
                    }
                }, 500);

            } catch (error) {
                addResult(`خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('button-status', false, 'خطأ في الزر');
            }
        }

        // دالة لاختبار إخفاء الأعمدة
        function testColumnToggle() {
            addResult('اختبار وظيفة إخفاء الأعمدة...', 'info');

            setTimeout(() => {
                const modal = document.getElementById('netSalaryModal');
                if (!modal) {
                    addResult('يرجى فتح نافذة صافي المرتب أولاً', 'error');
                    return;
                }

                // اختبار إخفاء عمود الحوافز
                const incentivesCheckbox = document.getElementById('showIncentives');
                if (incentivesCheckbox) {
                    incentivesCheckbox.checked = false;
                    toggleColumn('incentives');
                    addResult('تم إخفاء عمود الحوافز والمكافآت', 'success');

                    // إعادة إظهاره
                    setTimeout(() => {
                        incentivesCheckbox.checked = true;
                        toggleColumn('incentives');
                        addResult('تم إعادة إظهار عمود الحوافز والمكافآت', 'success');
                    }, 1000);
                } else {
                    addResult('لم يتم العثور على checkbox الحوافز', 'error');
                }
            }, 1000);
        }

        // دالة لاختبار النافذة من معاينة البطاقة
        function testNetSalaryModal() {
            if (testEmployeeId) {
                openNetSalaryModal(testEmployeeId);
                addResult('تم فتح النافذة من معاينة البطاقة', 'success');
            } else {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
            }
        }

        // دالة لاختبار مصدر الأجر اليومي
        function testDailyWageSource() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('اختبار مصدر الأجر اليومي...', 'info');

            const employee = employees.find(emp => emp.id === testEmployeeId);
            if (!employee) {
                addResult('لم يتم العثور على الموظف', 'error');
                return;
            }

            // التحقق من نوع الموظف
            if (employee.employmentType !== 'daily') {
                addResult('هذا الاختبار مخصص للموظفين اليوميين فقط', 'error');
                return;
            }

            // اختبار مصادر الأجر اليومي
            addResult('فحص مصادر الأجر اليومي:', 'info');

            // المصدر الأول: employee.salary.dailyWage (من نافذة إدارة الراتب)
            if (employee.salary && employee.salary.dailyWage) {
                addResult(`✅ employee.salary.dailyWage: ${employee.salary.dailyWage} ج.م (من نافذة إدارة الراتب)`, 'success');
            } else {
                addResult('❌ employee.salary.dailyWage: غير موجود', 'error');
            }

            // المصدر الثاني: employee.dailyWage (للتوافق مع النظام القديم)
            if (employee.dailyWage) {
                addResult(`⚠️ employee.dailyWage: ${employee.dailyWage} ج.م (للتوافق مع النظام القديم)`, 'info');
            } else {
                addResult('❌ employee.dailyWage: غير موجود', 'error');
            }

            // اختبار الدالة الفعلية
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const salaryData = calculateEmployeeSalaryData(testEmployeeId, currentMonth, currentYear);
            if (salaryData) {
                addResult(`🎯 الأجر اليومي المستخدم في الحساب: ${salaryData.dailyWage} ج.م`, 'success');
                addResult(`📊 المرتب الأساسي المحسوب: ${salaryData.basicSalary} ج.م`, 'success');
                addResult(`📅 أيام الحضور الفعلية: ${salaryData.totalWorkDays} يوم`, 'info');

                // التحقق من صحة حساب المرتب الأساسي
                const expectedSalary = salaryData.totalWorkDays * salaryData.dailyWage;
                if (salaryData.basicSalary === expectedSalary) {
                    addResult('✅ حساب المرتب الأساسي صحيح: أيام الحضور × الأجر اليومي', 'success');
                } else {
                    addResult(`❌ خطأ في حساب المرتب الأساسي: متوقع ${expectedSalary} ج.م`, 'error');
                }

                // اختبار حساب الساعة الإضافية
                if (salaryData.totalOvertimeHours > 0) {
                    addResult('اختبار حساب الساعة الإضافية:', 'info');
                    addResult(`⏰ الساعات الإضافية: ${salaryData.totalOvertimeHours} ساعة`, 'info');
                    addResult(`💰 قيمة الساعة الإضافية: ${salaryData.overtimeRate.toFixed(2)} ج.م (${salaryData.dailyWage}÷8)`, 'success');
                    addResult(`💵 إجمالي العمل الإضافي: ${salaryData.overtimePay.toFixed(2)} ج.م`, 'success');

                    // التحقق من صحة حساب الساعة الإضافية
                    const expectedOvertimeRate = salaryData.dailyWage / 8;
                    const expectedOvertimePay = salaryData.totalOvertimeHours * expectedOvertimeRate;

                    if (Math.abs(salaryData.overtimeRate - expectedOvertimeRate) < 0.01) {
                        addResult('✅ حساب قيمة الساعة الإضافية صحيح: الأجر اليومي ÷ 8', 'success');
                    } else {
                        addResult(`❌ خطأ في حساب قيمة الساعة الإضافية: متوقع ${expectedOvertimeRate.toFixed(2)} ج.م`, 'error');
                    }

                    if (Math.abs(salaryData.overtimePay - expectedOvertimePay) < 0.01) {
                        addResult('✅ حساب إجمالي العمل الإضافي صحيح', 'success');
                    } else {
                        addResult(`❌ خطأ في حساب إجمالي العمل الإضافي: متوقع ${expectedOvertimePay.toFixed(2)} ج.م`, 'error');
                    }
                } else {
                    addResult('ℹ️ لا توجد ساعات إضافية للاختبار', 'info');
                }
            } else {
                addResult('❌ فشل في حساب بيانات الراتب', 'error');
            }
        }

        // دالة لاختبار حساب البدلات
        function testAllowancesCalculation() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('اختبار حساب البدلات...', 'info');

            const employee = employees.find(emp => emp.id === testEmployeeId);
            if (!employee) {
                addResult('لم يتم العثور على الموظف', 'error');
                return;
            }

            // فحص بيانات البدلات في الموظف
            addResult('فحص بيانات البدلات في الموظف:', 'info');

            // البدلات الثابتة
            if (employee.salary && employee.salary.allowances) {
                addResult(`✅ البدلات الثابتة موجودة:`, 'success');
                addResult(`   • إجمالي البدلات: ${employee.salary.allowances.total || 0} ج.م`, 'info');
                if (employee.salary.allowances.housing) {
                    addResult(`   • بدل سكن: ${employee.salary.allowances.housing} ج.م`, 'info');
                }
                if (employee.salary.allowances.transport) {
                    addResult(`   • بدل مواصلات: ${employee.salary.allowances.transport} ج.م`, 'info');
                }
                if (employee.salary.allowances.food) {
                    addResult(`   • بدل طعام: ${employee.salary.allowances.food} ج.م`, 'info');
                }
            } else {
                addResult('❌ البدلات الثابتة غير موجودة', 'error');
            }

            // البدلات اليومية
            if (employee.salary && employee.salary.dailyAllowances) {
                addResult(`✅ البدلات اليومية موجودة:`, 'success');
                addResult(`   • بدل اغتراب يومي: ${employee.salary.dailyAllowances.expatDaily || 0} ج.م`, 'info');
                addResult(`   • بدل مواصلات يومي: ${employee.salary.dailyAllowances.transportDaily || 0} ج.م`, 'info');
            } else {
                addResult('❌ البدلات اليومية غير موجودة', 'error');
            }

            // اختبار الحساب الفعلي
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const salaryData = calculateEmployeeSalaryData(testEmployeeId, currentMonth, currentYear);
            if (salaryData) {
                addResult('نتائج حساب البدلات:', 'info');
                addResult(`💰 البدلات الثابتة المحسوبة: ${salaryData.allowances} ج.م`, 'success');
                addResult(`📅 البدلات اليومية المحسوبة: ${salaryData.dailyAllowances} ج.م`, 'success');
                addResult(`📊 إجمالي البدلات: ${salaryData.allowances + salaryData.dailyAllowances} ج.م`, 'success');

                // التحقق من صحة حساب البدلات الثابتة
                const expectedFixedAllowances = employee.salary?.allowances?.total || 0;
                if (salaryData.allowances === expectedFixedAllowances) {
                    addResult('✅ حساب البدلات الثابتة صحيح', 'success');
                } else {
                    addResult(`❌ خطأ في حساب البدلات الثابتة: متوقع ${expectedFixedAllowances} ج.م`, 'error');
                }

                // التحقق من صحة حساب البدلات اليومية
                const expatDaily = employee.salary?.dailyAllowances?.expatDaily || 0;
                const transportDaily = employee.salary?.dailyAllowances?.transportDaily || 0;
                const expectedDailyAllowances = (expatDaily + transportDaily) * salaryData.totalWorkDays;

                if (Math.abs(salaryData.dailyAllowances - expectedDailyAllowances) < 0.01) {
                    addResult('✅ حساب البدلات اليومية صحيح', 'success');
                    addResult(`   • ${salaryData.totalWorkDays} يوم × ${expatDaily + transportDaily} ج.م = ${expectedDailyAllowances} ج.م`, 'info');
                } else {
                    addResult(`❌ خطأ في حساب البدلات اليومية: متوقع ${expectedDailyAllowances} ج.م`, 'error');
                }

                // التحقق من تضمين البدلات في صافي المرتب
                const expectedGrossSalary = salaryData.basicSalary + salaryData.allowances + salaryData.dailyAllowances + salaryData.overtimePay + salaryData.totalIncentives;
                if (Math.abs(salaryData.grossSalary - expectedGrossSalary) < 0.01) {
                    addResult('✅ البدلات مُضمنة في الراتب الإجمالي بشكل صحيح', 'success');
                } else {
                    addResult(`❌ البدلات غير مُضمنة في الراتب الإجمالي بشكل صحيح`, 'error');
                    addResult(`   • الراتب الإجمالي الحالي: ${salaryData.grossSalary} ج.م`, 'error');
                    addResult(`   • الراتب الإجمالي المتوقع: ${expectedGrossSalary} ج.م`, 'error');
                }

                const expectedNetSalary = salaryData.grossSalary - salaryData.totalAdvances - salaryData.totalDeductions;
                if (Math.abs(salaryData.netSalary - expectedNetSalary) < 0.01) {
                    addResult('✅ البدلات مُضمنة في صافي المرتب بشكل صحيح', 'success');
                } else {
                    addResult(`❌ البدلات غير مُضمنة في صافي المرتب بشكل صحيح`, 'error');
                }

            } else {
                addResult('❌ فشل في حساب بيانات الراتب', 'error');
            }
        }

        // دالة لطباعة بيانات الراتب للتشخيص
        function debugSalaryData() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            const employee = employees.find(emp => emp.id === testEmployeeId);
            if (!employee) {
                addResult('لم يتم العثور على الموظف', 'error');
                return;
            }

            addResult('=== طباعة بيانات الراتب للتشخيص ===', 'info');

            // طباعة بيانات الموظف
            addResult('بيانات الموظف:', 'info');
            addResult(`JSON: ${JSON.stringify(employee, null, 2)}`, 'info');

            // حساب البيانات
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const salaryData = calculateEmployeeSalaryData(testEmployeeId, currentMonth, currentYear);

            addResult('نتائج الحساب:', 'info');
            addResult(`JSON: ${JSON.stringify(salaryData, null, 2)}`, 'info');

            // طباعة في console للفحص المفصل
            console.log('=== بيانات الموظف ===');
            console.log(employee);
            console.log('=== نتائج حساب الراتب ===');
            console.log(salaryData);

            addResult('تم طباعة البيانات في console المتصفح أيضاً', 'success');
        }

        // دالة لإصلاح البدلات مباشرة
        function fixEmployeeAllowances() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            const employee = employees.find(emp => emp.id === testEmployeeId);
            if (!employee) {
                addResult('لم يتم العثور على الموظف', 'error');
                return;
            }

            addResult('🔧 إصلاح البدلات مباشرة...', 'info');

            // إصلاح هيكل البدلات
            if (!employee.salary) {
                employee.salary = {};
            }

            // إصلاح البدلات الثابتة
            employee.salary.allowances = {
                total: 800,
                housing: 500,
                transport: 200,
                food: 100,
                other: 0
            };

            // إصلاح البدلات اليومية
            employee.salary.dailyAllowances = {
                expatDaily: 40,
                transportDaily: 25
            };

            // حفظ البيانات
            saveEmployeesToLocalStorage();

            addResult('✅ تم إصلاح البدلات بنجاح:', 'success');
            addResult(`💰 البدلات الثابتة: ${employee.salary.allowances.total} ج.م`, 'success');
            addResult(`🏠 بدل سكن: ${employee.salary.allowances.housing} ج.م`, 'info');
            addResult(`🚗 بدل مواصلات: ${employee.salary.allowances.transport} ج.م`, 'info');
            addResult(`🍽️ بدل طعام: ${employee.salary.allowances.food} ج.م`, 'info');
            addResult(`🌍 بدل اغتراب يومي: ${employee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
            addResult(`🚌 بدل مواصلات يومي: ${employee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

            // طباعة البيانات المحدثة
            console.log('البيانات بعد الإصلاح:', employee);
        }

        // دالة للتشخيص العميق
        function deepDiagnosis() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('🔍 بدء التشخيص العميق...', 'info');

            // 1. فحص الموظف
            const employee = employees.find(emp => emp.id === testEmployeeId);
            if (!employee) {
                addResult('❌ لم يتم العثور على الموظف', 'error');
                return;
            }

            addResult(`✅ الموظف موجود: ${employee.name}`, 'success');

            // 2. فحص هيكل البدلات
            addResult('🔍 فحص هيكل البدلات:', 'info');

            if (!employee.salary) {
                addResult('❌ employee.salary غير موجود', 'error');
                return;
            } else {
                addResult('✅ employee.salary موجود', 'success');
            }

            if (!employee.salary.allowances) {
                addResult('❌ employee.salary.allowances غير موجود', 'error');
            } else {
                addResult('✅ employee.salary.allowances موجود', 'success');
                addResult(`   • total: ${employee.salary.allowances.total}`, 'info');
                addResult(`   • housing: ${employee.salary.allowances.housing}`, 'info');
                addResult(`   • transport: ${employee.salary.allowances.transport}`, 'info');
                addResult(`   • food: ${employee.salary.allowances.food}`, 'info');
            }

            if (!employee.salary.dailyAllowances) {
                addResult('❌ employee.salary.dailyAllowances غير موجود', 'error');
            } else {
                addResult('✅ employee.salary.dailyAllowances موجود', 'success');
                addResult(`   • expatDaily: ${employee.salary.dailyAllowances.expatDaily}`, 'info');
                addResult(`   • transportDaily: ${employee.salary.dailyAllowances.transportDaily}`, 'info');
            }

            // 3. اختبار دالة الحساب
            addResult('🔍 اختبار دالة calculateEmployeeSalaryData:', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const salaryData = calculateEmployeeSalaryData(testEmployeeId, currentMonth, currentYear);

            if (!salaryData) {
                addResult('❌ دالة calculateEmployeeSalaryData ترجع null', 'error');
                return;
            }

            addResult('✅ دالة calculateEmployeeSalaryData تعمل', 'success');
            addResult(`   • allowances: ${salaryData.allowances}`, salaryData.allowances > 0 ? 'success' : 'error');
            addResult(`   • dailyAllowances: ${salaryData.dailyAllowances}`, salaryData.dailyAllowances > 0 ? 'success' : 'error');
            addResult(`   • grossSalary: ${salaryData.grossSalary}`, 'info');
            addResult(`   • netSalary: ${salaryData.netSalary}`, 'info');

            // 4. فحص أيام العمل
            addResult('🔍 فحص أيام العمل:', 'info');
            const workDays = employeeDaysData.filter(record => record.employeeId === testEmployeeId);
            addResult(`   • عدد سجلات أيام العمل: ${workDays.length}`, workDays.length > 0 ? 'success' : 'warning');

            if (workDays.length > 0) {
                const totalDays = workDays.reduce((sum, record) => sum + (record.actualDays || 0), 0);
                addResult(`   • إجمالي أيام العمل: ${totalDays}`, 'info');
            }

            // 5. فحص دالة showNetSalaryDetails
            addResult('🔍 اختبار دالة showNetSalaryDetails:', 'info');
            try {
                if (typeof showNetSalaryDetails === 'function') {
                    addResult('✅ دالة showNetSalaryDetails موجودة', 'success');
                } else {
                    addResult('❌ دالة showNetSalaryDetails غير موجودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في فحص دالة showNetSalaryDetails: ${error.message}`, 'error');
            }

            addResult('🎯 انتهى التشخيص العميق', 'info');
        }

        // دالة لمسح النتائج
        function clearResults() {
            document.getElementById('test-results').innerHTML =
                '<div class="test-result info">جاهز لبدء اختبار زر صافي المرتب...</div>';

            // إعادة تعيين حالة الاختبارات
            updateStatus('employee-status', false, 'في انتظار إنشاء الموظف التجريبي...');
            updateStatus('data-status', false, 'في انتظار إضافة البيانات...');
            updateStatus('button-status', false, 'في انتظار اختبار الزر...');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('مرحباً بك في نظام اختبار زر صافي المرتب', 'info');
            addResult('يمكنك الآن إنشاء البيانات التجريبية واختبار الزر', 'info');
        });
    </script>
</body>
</html>
