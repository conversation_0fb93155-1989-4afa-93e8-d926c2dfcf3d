<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة أيام الحضور المبسطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #f8f9fa;
            color: #495057;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            margin: 0;
            font-size: 1.8em;
        }

        .content {
            padding: 20px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .test-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.success:hover {
            background: #1e7e34;
        }

        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }

        .test-button.warning:hover {
            background: #e0a800;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .test-button.danger:hover {
            background: #c82333;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .status-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .status-label {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calendar-check"></i> اختبار نافذة أيام الحضور المبسطة</h1>
            <p>اختبار النافذة بعد إزالة العناصر الإضافية لتوفير مساحة أكبر</p>
        </div>

        <div class="content">
            <!-- قسم الاختبارات الأساسية -->
            <div class="test-section">
                <h3><i class="fas fa-play"></i> الاختبارات الأساسية</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="createTestEmployee()">
                        <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                    </button>
                    
                    <button class="test-button" onclick="openDaysWindow()">
                        <i class="fas fa-calendar-alt"></i> فتح نافذة أيام الحضور
                    </button>
                    
                    <button class="test-button warning" onclick="openRecordsWindow()">
                        <i class="fas fa-list"></i> فتح نافذة السجلات
                    </button>
                    
                    <button class="test-button" onclick="testSimplification()">
                        <i class="fas fa-check-circle"></i> اختبار التبسيط
                    </button>
                    
                    <button class="test-button" onclick="window.open('/', '_blank')">
                        <i class="fas fa-home"></i> التطبيق الأصلي
                    </button>
                    
                    <button class="test-button danger" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> الإحصائيات</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-number" id="employeesCount">0</div>
                        <div class="status-label">عدد الموظفين</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="recordsCount">0</div>
                        <div class="status-label">عدد السجلات</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="testsRun">0</div>
                        <div class="status-label">الاختبارات المنجزة</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="successRate">100%</div>
                        <div class="status-label">معدل النجاح</div>
                    </div>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> سجل الأحداث</h3>
                <div class="log" id="log">
                    <div class="log-entry log-info">[تحميل] جاهز لاختبار النافذة المبسطة...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let testsRun = 0;
        let testsSuccess = 0;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('employeesCount').textContent = employees ? employees.length : 0;
            document.getElementById('recordsCount').textContent = employeeDaysData ? employeeDaysData.length : 0;
            document.getElementById('testsRun').textContent = testsRun;
            
            const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 100;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function createTestEmployee() {
            log('➕ إنشاء موظف تجريبي...', 'info');
            testsRun++;
            
            try {
                const testEmployee = {
                    id: 12345,
                    name: 'أحمد محمد - اختبار التبسيط',
                    position: 'مطور',
                    employeeCode: 'TEST12345',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 12345);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                saveEmployeesToLocalStorage();
                testsSuccess++;
                
                log(`✅ تم إنشاء الموظف: ${testEmployee.name}`, 'success');
                
            } catch (error) {
                log('❌ خطأ في إنشاء الموظف: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function openDaysWindow() {
            log('📅 فتح نافذة أيام الحضور...', 'info');
            testsRun++;
            
            try {
                if (!employees.find(emp => emp.id === 12345)) {
                    log('⚠️ لا يوجد موظف تجريبي، سيتم إنشاؤه...', 'warning');
                    createTestEmployee();
                }
                
                if (typeof createEnhancedDaysCalculator === 'function') {
                    createEnhancedDaysCalculator(12345);
                    testsSuccess++;
                    log('✅ تم فتح نافذة أيام الحضور المبسطة', 'success');
                } else {
                    log('❌ دالة createEnhancedDaysCalculator غير موجودة', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function openRecordsWindow() {
            log('📋 فتح نافذة السجلات...', 'info');
            testsRun++;
            
            try {
                if (!employees.find(emp => emp.id === 12345)) {
                    log('⚠️ لا يوجد موظف تجريبي، سيتم إنشاؤه...', 'warning');
                    createTestEmployee();
                }
                
                if (typeof viewDaysRecords === 'function') {
                    viewDaysRecords(12345);
                    testsSuccess++;
                    log('✅ تم فتح نافذة السجلات المبسطة', 'success');
                } else {
                    log('❌ دالة viewDaysRecords غير موجودة', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function testSimplification() {
            log('🔍 اختبار التبسيط...', 'info');
            testsRun++;
            
            try {
                // فتح النافذة أولاً
                if (!employees.find(emp => emp.id === 12345)) {
                    createTestEmployee();
                }
                
                viewDaysRecords(12345);
                
                // فحص العناصر المحذوفة
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        // فحص عدم وجود العناصر المحذوفة
                        const removedElements = [
                            'خيارات التعديل',
                            'مسح الفلاتر',
                            'الاجماليات المفلتره',
                            'سجلات أيام الحضور المحفوظة'
                        ];
                        
                        let simplificationSuccess = true;
                        removedElements.forEach(element => {
                            if (modal.innerHTML.includes(element)) {
                                log(`⚠️ العنصر "${element}" ما زال موجود`, 'warning');
                                simplificationSuccess = false;
                            } else {
                                log(`✅ تم إزالة "${element}" بنجاح`, 'success');
                            }
                        });
                        
                        if (simplificationSuccess) {
                            testsSuccess++;
                            log('🎉 تم التبسيط بنجاح - النافذة أصبحت أكثر بساطة', 'success');
                        } else {
                            log('⚠️ التبسيط جزئي - بعض العناصر ما زالت موجودة', 'warning');
                        }
                    } else {
                        log('❌ لم يتم فتح النافذة', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                log('❌ خطأ في اختبار التبسيط: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
            testsRun = 0;
            testsSuccess = 0;
            updateStats();
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة اختبار النافذة المبسطة', 'success');
            updateStats();
            
            setTimeout(() => {
                log('🔍 فحص النظام...', 'info');
                if (typeof employees !== 'undefined') {
                    log('✅ متغير employees موجود', 'success');
                } else {
                    log('❌ متغير employees غير موجود', 'error');
                }
                
                if (typeof employeeDaysData !== 'undefined') {
                    log('✅ متغير employeeDaysData موجود', 'success');
                } else {
                    log('❌ متغير employeeDaysData غير موجود', 'error');
                }
                
                updateStats();
            }, 1000);
        });
    </script>
</body>
</html>
