<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #f0f0f0;
        }

        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 16px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .results {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid #e9ecef;
            max-height: 300px;
            overflow-y: auto;
        }

        .result-item {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-item.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-item.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result-item.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .result-item.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }

        .status-indicator.success { background: #28a745; }
        .status-indicator.error { background: #dc3545; }
        .status-indicator.warning { background: #ffc107; }
        .status-indicator.info { background: #17a2b8; }

        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .console-output .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .console-output .log-entry.error {
            color: #e74c3c;
        }

        .console-output .log-entry.success {
            color: #2ecc71;
        }

        .console-output .log-entry.warning {
            color: #f39c12;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-trophy"></i> اختبار نظام الحوافز والمكافآت</h1>
            <p>اختبار شامل لجميع وظائف نظام إدارة الحوافز والمكافآت</p>
        </div>

        <div class="grid">
            <!-- اختبار الدوال الأساسية -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> اختبار الدوال الأساسية</h3>
                <button class="test-button" onclick="testBasicFunctions()">
                    <i class="fas fa-play"></i> اختبار الدوال
                </button>
                <button class="test-button success" onclick="createTestData()">
                    <i class="fas fa-database"></i> إنشاء بيانات تجريبية
                </button>
                <div class="results" id="basicResults"></div>
            </div>

            <!-- اختبار واجهة المستخدم -->
            <div class="test-section">
                <h3><i class="fas fa-desktop"></i> اختبار واجهة المستخدم</h3>
                <button class="test-button" onclick="testIncentivesUI()">
                    <i class="fas fa-window-maximize"></i> اختبار النوافذ
                </button>
                <button class="test-button warning" onclick="testFormValidation()">
                    <i class="fas fa-check-circle"></i> اختبار التحقق
                </button>
                <div class="results" id="uiResults"></div>
            </div>

            <!-- اختبار حفظ البيانات -->
            <div class="test-section">
                <h3><i class="fas fa-save"></i> اختبار حفظ البيانات</h3>
                <button class="test-button" onclick="testDataPersistence()">
                    <i class="fas fa-database"></i> اختبار localStorage
                </button>
                <button class="test-button danger" onclick="clearTestData()">
                    <i class="fas fa-trash"></i> مسح البيانات التجريبية
                </button>
                <div class="results" id="dataResults"></div>
            </div>

            <!-- اختبار العمليات -->
            <div class="test-section">
                <h3><i class="fas fa-tasks"></i> اختبار العمليات</h3>
                <button class="test-button" onclick="testCRUDOperations()">
                    <i class="fas fa-edit"></i> اختبار CRUD
                </button>
                <button class="test-button success" onclick="testCalculations()">
                    <i class="fas fa-calculator"></i> اختبار الحسابات
                </button>
                <div class="results" id="operationsResults"></div>
            </div>
        </div>

        <!-- مخرجات وحدة التحكم -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> مخرجات وحدة التحكم</h3>
            <button class="test-button" onclick="clearConsole()">
                <i class="fas fa-broom"></i> مسح المخرجات
            </button>
            <div class="console-output" id="consoleOutput"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دوال مساعدة للاختبار
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;

            const icon = type === 'success' ? 'fa-check' :
                        type === 'error' ? 'fa-times' :
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info';

            resultItem.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
                <span class="status-indicator ${type}"></span>
            `;

            container.appendChild(resultItem);
            container.scrollTop = container.scrollHeight;
        }

        function addConsoleLog(message, type = 'info') {
            const console = document.getElementById('consoleOutput');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }

        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }

        // اختبار الدوال الأساسية
        function testBasicFunctions() {
            addResult('basicResults', 'بدء اختبار الدوال الأساسية...', 'info');
            addConsoleLog('🔍 فحص الدوال الأساسية...');

            const requiredFunctions = [
                'viewIncentivesRewards',
                'saveIncentivesRewardsToLocalStorage',
                'loadIncentivesRewardsFromLocalStorage',
                'closeIncentivesRewardsModal',
                'updateIncentivesRewardsList',
                'openAddIncentiveModal',
                'openAddRewardModal',
                'saveIncentiveReward',
                'deleteIncentiveReward',
                'exportIncentivesRewards'
            ];

            let functionsFound = 0;

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult('basicResults', `✓ دالة موجودة: ${funcName}`, 'success');
                    addConsoleLog(`✓ ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult('basicResults', `✗ دالة مفقودة: ${funcName}`, 'error');
                    addConsoleLog(`✗ ${funcName}: مفقودة`, 'error');
                }
            });

            const percentage = (functionsFound / requiredFunctions.length) * 100;
            const resultType = percentage === 100 ? 'success' : percentage >= 80 ? 'warning' : 'error';

            addResult('basicResults', `النتيجة: ${functionsFound}/${requiredFunctions.length} دالة موجودة (${percentage.toFixed(1)}%)`, resultType);
            addConsoleLog(`📊 النتيجة النهائية: ${percentage.toFixed(1)}%`, resultType);
        }

        // إنشاء بيانات تجريبية
        function createTestData() {
            addResult('basicResults', 'إنشاء بيانات تجريبية...', 'info');
            addConsoleLog('🔧 إنشاء بيانات تجريبية...');

            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 1001)) {
                    employees.push({
                        id: 1001,
                        name: 'أحمد محمد علي - اختبار الحوافز',
                        position: 'مطور برمجيات',
                        employeeCode: 'TEST001',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                    addResult('basicResults', '✓ تم إنشاء موظف تجريبي', 'success');
                    addConsoleLog('✓ موظف تجريبي: تم الإنشاء', 'success');
                }

                // إنشاء بيانات حوافز ومكافآت تجريبية
                const testIncentivesData = [
                    {
                        id: Date.now() + 1,
                        employeeId: 1001,
                        type: 'incentive',
                        month: 1,
                        year: 2024,
                        amount: 1000,
                        description: 'حافز الأداء المتميز',
                        date: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 1001,
                        type: 'reward',
                        month: 2,
                        year: 2024,
                        amount: 1500,
                        description: 'مكافأة إنجاز المشروع',
                        date: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 1001,
                        type: 'incentive',
                        month: 3,
                        year: 2024,
                        amount: 800,
                        description: 'حافز الحضور المنتظم',
                        date: new Date().toISOString()
                    }
                ];

                // إضافة البيانات إلى المصفوفة
                if (typeof incentivesRewardsData === 'undefined') {
                    window.incentivesRewardsData = [];
                }

                testIncentivesData.forEach(record => {
                    if (!incentivesRewardsData.find(r => r.id === record.id)) {
                        incentivesRewardsData.push(record);
                    }
                });

                // حفظ البيانات
                if (typeof saveIncentivesRewardsToLocalStorage === 'function') {
                    saveIncentivesRewardsToLocalStorage();
                    addResult('basicResults', '✓ تم حفظ البيانات التجريبية', 'success');
                    addConsoleLog('✓ البيانات التجريبية: تم الحفظ', 'success');
                }

                addResult('basicResults', `✓ تم إنشاء ${testIncentivesData.length} سجل حوافز ومكافآت`, 'success');
                addConsoleLog(`✓ تم إنشاء ${testIncentivesData.length} سجل`, 'success');

            } catch (error) {
                addResult('basicResults', `✗ خطأ في إنشاء البيانات: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ: ${error.message}`, 'error');
            }
        }

        // اختبار واجهة المستخدم
        function testIncentivesUI() {
            addResult('uiResults', 'اختبار واجهة المستخدم...', 'info');
            addConsoleLog('🖥️ اختبار واجهة المستخدم...');

            try {
                // اختبار فتح نافذة الحوافز والمكافآت
                if (typeof viewIncentivesRewards === 'function') {
                    // محاولة فتح النافذة للموظف التجريبي
                    setTimeout(() => {
                        try {
                            viewIncentivesRewards(1001);
                            addResult('uiResults', '✓ تم فتح نافذة الحوافز والمكافآت', 'success');
                            addConsoleLog('✓ نافذة الحوافز: تم الفتح', 'success');

                            // التحقق من وجود النافذة
                            setTimeout(() => {
                                const modal = document.getElementById('incentivesRewardsModal');
                                if (modal) {
                                    addResult('uiResults', '✓ النافذة موجودة في DOM', 'success');
                                    addConsoleLog('✓ DOM: النافذة موجودة', 'success');

                                    // إغلاق النافذة بعد فترة
                                    setTimeout(() => {
                                        if (typeof closeIncentivesRewardsModal === 'function') {
                                            closeIncentivesRewardsModal();
                                            addResult('uiResults', '✓ تم إغلاق النافذة', 'success');
                                            addConsoleLog('✓ النافذة: تم الإغلاق', 'success');
                                        }
                                    }, 2000);
                                } else {
                                    addResult('uiResults', '✗ النافذة غير موجودة في DOM', 'error');
                                    addConsoleLog('✗ DOM: النافذة مفقودة', 'error');
                                }
                            }, 500);

                        } catch (error) {
                            addResult('uiResults', `✗ خطأ في فتح النافذة: ${error.message}`, 'error');
                            addConsoleLog(`✗ خطأ في النافذة: ${error.message}`, 'error');
                        }
                    }, 100);
                } else {
                    addResult('uiResults', '✗ دالة viewIncentivesRewards غير موجودة', 'error');
                    addConsoleLog('✗ دالة viewIncentivesRewards: مفقودة', 'error');
                }

            } catch (error) {
                addResult('uiResults', `✗ خطأ في اختبار الواجهة: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ في الواجهة: ${error.message}`, 'error');
            }
        }

        // اختبار التحقق من صحة النماذج
        function testFormValidation() {
            addResult('uiResults', 'اختبار التحقق من صحة النماذج...', 'info');
            addConsoleLog('✅ اختبار التحقق من النماذج...');

            // هذا الاختبار سيتم تنفيذه عند فتح النافذة فعلياً
            addResult('uiResults', 'ℹ️ يتطلب فتح النافذة لاختبار النماذج', 'warning');
            addConsoleLog('⚠️ اختبار النماذج: يتطلب تفاعل المستخدم', 'warning');
        }

        // اختبار حفظ البيانات
        function testDataPersistence() {
            addResult('dataResults', 'اختبار حفظ البيانات...', 'info');
            addConsoleLog('💾 اختبار حفظ البيانات...');

            try {
                // اختبار localStorage
                const testData = [
                    {
                        id: 999999,
                        employeeId: 1001,
                        type: 'incentive',
                        month: 12,
                        year: 2023,
                        amount: 500,
                        description: 'اختبار حفظ البيانات',
                        date: new Date().toISOString()
                    }
                ];

                // حفظ البيانات
                localStorage.setItem('oscoIncentivesRewards_test', JSON.stringify(testData));
                addResult('dataResults', '✓ تم حفظ البيانات في localStorage', 'success');
                addConsoleLog('✓ localStorage: تم الحفظ', 'success');

                // استرجاع البيانات
                const retrievedData = localStorage.getItem('oscoIncentivesRewards_test');
                if (retrievedData) {
                    const parsedData = JSON.parse(retrievedData);
                    if (parsedData.length === testData.length && parsedData[0].id === testData[0].id) {
                        addResult('dataResults', '✓ تم استرجاع البيانات بنجاح', 'success');
                        addConsoleLog('✓ localStorage: تم الاسترجاع', 'success');
                    } else {
                        addResult('dataResults', '✗ البيانات المسترجعة غير صحيحة', 'error');
                        addConsoleLog('✗ localStorage: بيانات خاطئة', 'error');
                    }
                } else {
                    addResult('dataResults', '✗ فشل في استرجاع البيانات', 'error');
                    addConsoleLog('✗ localStorage: فشل الاسترجاع', 'error');
                }

                // تنظيف البيانات التجريبية
                localStorage.removeItem('oscoIncentivesRewards_test');
                addResult('dataResults', '✓ تم تنظيف البيانات التجريبية', 'success');
                addConsoleLog('✓ تنظيف: تم', 'success');

            } catch (error) {
                addResult('dataResults', `✗ خطأ في اختبار البيانات: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ في البيانات: ${error.message}`, 'error');
            }
        }

        // مسح البيانات التجريبية
        function clearTestData() {
            addResult('dataResults', 'مسح البيانات التجريبية...', 'info');
            addConsoleLog('🗑️ مسح البيانات التجريبية...');

            try {
                // مسح الموظف التجريبي
                const employeeIndex = employees.findIndex(emp => emp.id === 1001);
                if (employeeIndex !== -1) {
                    employees.splice(employeeIndex, 1);
                    addResult('dataResults', '✓ تم مسح الموظف التجريبي', 'success');
                    addConsoleLog('✓ الموظف التجريبي: تم المسح', 'success');
                }

                // مسح بيانات الحوافز والمكافآت التجريبية
                if (typeof incentivesRewardsData !== 'undefined') {
                    const originalLength = incentivesRewardsData.length;
                    incentivesRewardsData = incentivesRewardsData.filter(record => record.employeeId !== 1001);
                    const removedCount = originalLength - incentivesRewardsData.length;

                    if (removedCount > 0) {
                        addResult('dataResults', `✓ تم مسح ${removedCount} سجل حوافز ومكافآت`, 'success');
                        addConsoleLog(`✓ تم مسح ${removedCount} سجل`, 'success');

                        // حفظ التغييرات
                        if (typeof saveIncentivesRewardsToLocalStorage === 'function') {
                            saveIncentivesRewardsToLocalStorage();
                            addResult('dataResults', '✓ تم حفظ التغييرات', 'success');
                            addConsoleLog('✓ التغييرات: تم الحفظ', 'success');
                        }
                    }
                }

                addResult('dataResults', '✓ تم مسح جميع البيانات التجريبية', 'success');
                addConsoleLog('✓ المسح: مكتمل', 'success');

            } catch (error) {
                addResult('dataResults', `✗ خطأ في مسح البيانات: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ في المسح: ${error.message}`, 'error');
            }
        }

        // اختبار العمليات CRUD
        function testCRUDOperations() {
            addResult('operationsResults', 'اختبار عمليات CRUD...', 'info');
            addConsoleLog('🔄 اختبار عمليات CRUD...');

            try {
                // اختبار إنشاء سجل جديد (Create)
                const testRecord = {
                    id: Date.now() + 9999,
                    employeeId: 1001,
                    type: 'incentive',
                    month: 6,
                    year: 2024,
                    amount: 750,
                    description: 'اختبار CRUD - إنشاء',
                    date: new Date().toISOString()
                };

                if (typeof incentivesRewardsData !== 'undefined') {
                    incentivesRewardsData.push(testRecord);
                    addResult('operationsResults', '✓ Create: تم إنشاء سجل جديد', 'success');
                    addConsoleLog('✓ Create: نجح', 'success');

                    // اختبار قراءة السجل (Read)
                    const foundRecord = incentivesRewardsData.find(r => r.id === testRecord.id);
                    if (foundRecord) {
                        addResult('operationsResults', '✓ Read: تم العثور على السجل', 'success');
                        addConsoleLog('✓ Read: نجح', 'success');

                        // اختبار تحديث السجل (Update)
                        foundRecord.amount = 1000;
                        foundRecord.description = 'اختبار CRUD - تحديث';

                        const updatedRecord = incentivesRewardsData.find(r => r.id === testRecord.id);
                        if (updatedRecord && updatedRecord.amount === 1000) {
                            addResult('operationsResults', '✓ Update: تم تحديث السجل', 'success');
                            addConsoleLog('✓ Update: نجح', 'success');
                        } else {
                            addResult('operationsResults', '✗ Update: فشل في التحديث', 'error');
                            addConsoleLog('✗ Update: فشل', 'error');
                        }

                        // اختبار حذف السجل (Delete)
                        const recordIndex = incentivesRewardsData.findIndex(r => r.id === testRecord.id);
                        if (recordIndex !== -1) {
                            incentivesRewardsData.splice(recordIndex, 1);

                            const deletedRecord = incentivesRewardsData.find(r => r.id === testRecord.id);
                            if (!deletedRecord) {
                                addResult('operationsResults', '✓ Delete: تم حذف السجل', 'success');
                                addConsoleLog('✓ Delete: نجح', 'success');
                            } else {
                                addResult('operationsResults', '✗ Delete: فشل في الحذف', 'error');
                                addConsoleLog('✗ Delete: فشل', 'error');
                            }
                        }
                    } else {
                        addResult('operationsResults', '✗ Read: لم يتم العثور على السجل', 'error');
                        addConsoleLog('✗ Read: فشل', 'error');
                    }
                } else {
                    addResult('operationsResults', '✗ متغير incentivesRewardsData غير موجود', 'error');
                    addConsoleLog('✗ متغير البيانات: مفقود', 'error');
                }

                addResult('operationsResults', '✓ اكتمل اختبار عمليات CRUD', 'success');
                addConsoleLog('✓ CRUD: اكتمل', 'success');

            } catch (error) {
                addResult('operationsResults', `✗ خطأ في اختبار CRUD: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ في CRUD: ${error.message}`, 'error');
            }
        }

        // اختبار الحسابات
        function testCalculations() {
            addResult('operationsResults', 'اختبار الحسابات...', 'info');
            addConsoleLog('🧮 اختبار الحسابات...');

            try {
                // إنشاء بيانات تجريبية للحسابات
                const testData = [
                    { type: 'incentive', amount: 1000 },
                    { type: 'incentive', amount: 1500 },
                    { type: 'reward', amount: 2000 },
                    { type: 'reward', amount: 800 },
                    { type: 'incentive', amount: 500 }
                ];

                // حساب إجمالي الحوافز
                const totalIncentives = testData
                    .filter(record => record.type === 'incentive')
                    .reduce((sum, record) => sum + record.amount, 0);

                const expectedIncentives = 1000 + 1500 + 500; // 3000

                if (totalIncentives === expectedIncentives) {
                    addResult('operationsResults', `✓ حساب الحوافز: ${totalIncentives} ج.م`, 'success');
                    addConsoleLog(`✓ حساب الحوافز: ${totalIncentives}`, 'success');
                } else {
                    addResult('operationsResults', `✗ خطأ في حساب الحوافز: ${totalIncentives} بدلاً من ${expectedIncentives}`, 'error');
                    addConsoleLog(`✗ حساب الحوافز: خطأ`, 'error');
                }

                // حساب إجمالي المكافآت
                const totalRewards = testData
                    .filter(record => record.type === 'reward')
                    .reduce((sum, record) => sum + record.amount, 0);

                const expectedRewards = 2000 + 800; // 2800

                if (totalRewards === expectedRewards) {
                    addResult('operationsResults', `✓ حساب المكافآت: ${totalRewards} ج.م`, 'success');
                    addConsoleLog(`✓ حساب المكافآت: ${totalRewards}`, 'success');
                } else {
                    addResult('operationsResults', `✗ خطأ في حساب المكافآت: ${totalRewards} بدلاً من ${expectedRewards}`, 'error');
                    addConsoleLog(`✗ حساب المكافآت: خطأ`, 'error');
                }

                // حساب الإجمالي العام
                const grandTotal = totalIncentives + totalRewards;
                const expectedGrandTotal = expectedIncentives + expectedRewards; // 5800

                if (grandTotal === expectedGrandTotal) {
                    addResult('operationsResults', `✓ الإجمالي العام: ${grandTotal} ج.م`, 'success');
                    addConsoleLog(`✓ الإجمالي العام: ${grandTotal}`, 'success');
                } else {
                    addResult('operationsResults', `✗ خطأ في الإجمالي العام: ${grandTotal} بدلاً من ${expectedGrandTotal}`, 'error');
                    addConsoleLog(`✗ الإجمالي العام: خطأ`, 'error');
                }

                // اختبار دالة getMonthName إذا كانت موجودة
                if (typeof getMonthName === 'function') {
                    const monthTest = getMonthName(1);
                    if (monthTest === 'يناير') {
                        addResult('operationsResults', '✓ دالة getMonthName تعمل بشكل صحيح', 'success');
                        addConsoleLog('✓ getMonthName: تعمل', 'success');
                    } else {
                        addResult('operationsResults', `✗ دالة getMonthName ترجع قيمة خاطئة: ${monthTest}`, 'error');
                        addConsoleLog('✗ getMonthName: خطأ', 'error');
                    }
                } else {
                    addResult('operationsResults', '⚠️ دالة getMonthName غير موجودة', 'warning');
                    addConsoleLog('⚠️ getMonthName: مفقودة', 'warning');
                }

                addResult('operationsResults', '✓ اكتمل اختبار الحسابات', 'success');
                addConsoleLog('✓ الحسابات: اكتملت', 'success');

            } catch (error) {
                addResult('operationsResults', `✗ خطأ في اختبار الحسابات: ${error.message}`, 'error');
                addConsoleLog(`✗ خطأ في الحسابات: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار شامل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addConsoleLog('🚀 بدء الاختبار التلقائي...');

            setTimeout(() => {
                addConsoleLog('--- اختبار تلقائي سريع ---');

                // فحص المتغيرات العامة
                if (typeof employees !== 'undefined') {
                    addConsoleLog('✓ متغير employees: موجود', 'success');
                } else {
                    addConsoleLog('✗ متغير employees: مفقود', 'error');
                }

                if (typeof incentivesRewardsData !== 'undefined') {
                    addConsoleLog('✓ متغير incentivesRewardsData: موجود', 'success');
                } else {
                    addConsoleLog('✗ متغير incentivesRewardsData: مفقود', 'error');
                }

                // فحص الدوال الأساسية
                const criticalFunctions = ['viewIncentivesRewards', 'saveIncentivesRewardsToLocalStorage'];
                criticalFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addConsoleLog(`✓ ${funcName}: متاحة`, 'success');
                    } else {
                        addConsoleLog(`✗ ${funcName}: غير متاحة`, 'error');
                    }
                });

                addConsoleLog('--- انتهى الاختبار التلقائي ---');
            }, 1000);
        });
    </script>
</body>
</html>
