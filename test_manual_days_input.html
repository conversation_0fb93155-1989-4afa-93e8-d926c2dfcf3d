<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإدخال اليدوي للأيام</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .fix-summary {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .fix-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        
        .fix-item:last-child {
            border-bottom: none;
        }
        
        .fix-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            background: #28a745;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 20px 0;
        }
        
        .step {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        
        .step-number {
            background: #ffc107;
            color: #212529;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 8px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> اختبار الإدخال اليدوي للأيام</h1>
            <p>التأكد من أن المستخدم يدخل عدد الأيام المحسوبة بنفسه</p>
        </div>

        <div class="fix-summary">
            <h4><i class="fas fa-wrench"></i> الإصلاحات المطبقة:</h4>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إيقاف الحساب التلقائي في updateMonthInfo</strong>
                    <br><small>لا يتم ملء حقل calculatedDays تلقائياً عند اختيار الشهر والسنة</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إيقاف الملء التلقائي في fillSampleDataEnhanced</strong>
                    <br><small>البيانات التجريبية لا تملأ حقل الأيام المحسوبة</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إيقاف الملء التلقائي في fillTestData</strong>
                    <br><small>دوال الاختبار لا تملأ حقل الأيام المحسوبة</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>الحفاظ على الحساب للمرجع</strong>
                    <br><small>النظام يحسب عدد أيام الشهر للمرجع فقط في console</small>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-list-ol"></i> خطوات الاختبار:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء بيانات تجريبية</strong> - إنشاء موظف للاختبار
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة حساب الأيام</strong> - فتح النافذة للموظف التجريبي
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>اختيار الشهر والسنة</strong> - اختيار شهر وسنة من القوائم المنسدلة
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>فحص حقل الأيام</strong> - التأكد من أن حقل "عدد الأيام المحسوبة" فارغ
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>إدخال يدوي</strong> - إدخال عدد الأيام المطلوب يدوياً
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button success" onclick="runManualInputTest()">
                <i class="fas fa-play"></i> اختبار الإدخال اليدوي
            </button>
            
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button warning" onclick="openDaysCalculator()">
                <i class="fas fa-calculator"></i> فتح نافذة حساب الأيام
            </button>
            
            <button class="test-button danger" onclick="testMonthSelection()">
                <i class="fas fa-calendar"></i> اختبار اختيار الشهر
            </button>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار الإدخال اليدوي للأيام...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [MANUAL_INPUT_TEST] مراقب الإدخال اليدوي جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');
            addLog('🔧 إنشاء بيانات تجريبية...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 888)) {
                    employees.push({
                        id: 888,
                        name: 'خالد محمد - اختبار الإدخال اليدوي',
                        position: 'محاسب',
                        employeeCode: 'MANUAL888',
                        employmentType: 'monthly',
                        basicSalary: 6500
                    });
                    addLog('✓ تم إنشاء موظف تجريبي');
                }
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openDaysCalculator() {
            try {
                if (!employees.find(emp => emp.id === 888)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة حساب الأيام...');
                createEnhancedDaysCalculator(888);
                
                setTimeout(() => {
                    const modal = document.getElementById('enhancedDaysModal');
                    if (modal) {
                        addLog('✓ تم فتح نافذة حساب الأيام بنجاح');
                        updateStatus('تم فتح نافذة حساب الأيام! الآن اختبر اختيار الشهر والسنة', 'success');
                    } else {
                        addLog('✗ فشل في فتح نافذة حساب الأيام');
                        updateStatus('فشل في فتح نافذة حساب الأيام', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح نافذة حساب الأيام: ' + error.message, 'error');
            }
        }

        function testMonthSelection() {
            addLog('🧪 اختبار اختيار الشهر والسنة...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addLog('❌ نافذة حساب الأيام غير مفتوحة');
                updateStatus('يرجى فتح نافذة حساب الأيام أولاً', 'error');
                return;
            }
            
            const monthSelect = modal.querySelector('#monthSelect');
            const yearSelect = modal.querySelector('#yearSelect');
            const calculatedDaysField = modal.querySelector('#calculatedDays');
            
            if (!monthSelect || !yearSelect || !calculatedDaysField) {
                addLog('❌ لم يتم العثور على العناصر المطلوبة');
                updateStatus('لم يتم العثور على العناصر المطلوبة في النافذة', 'error');
                return;
            }
            
            // فحص القيمة الأولية
            const initialValue = calculatedDaysField.value;
            addLog(`📊 القيمة الأولية لحقل الأيام: "${initialValue}"`);
            
            // اختيار شهر وسنة
            addLog('🔄 اختيار شهر يناير 2024...');
            monthSelect.value = '1';
            yearSelect.value = '2024';
            
            // استدعاء دالة تحديث الشهر
            updateMonthInfo();
            
            // فحص القيمة بعد التحديث
            setTimeout(() => {
                const afterValue = calculatedDaysField.value;
                addLog(`📊 القيمة بعد اختيار الشهر: "${afterValue}"`);
                
                if (afterValue === '' || afterValue === initialValue) {
                    addLog('✅ ممتاز! حقل الأيام المحسوبة لم يتم ملؤه تلقائياً');
                    updateStatus('✅ نجح الاختبار! المستخدم يحتاج لإدخال الأيام يدوياً', 'success');
                } else {
                    addLog('❌ مشكلة! حقل الأيام المحسوبة تم ملؤه تلقائياً');
                    updateStatus('❌ فشل الاختبار! الحقل لا يزال يملأ تلقائياً', 'error');
                }
                
                // اختبار إدخال يدوي
                addLog('🔄 اختبار الإدخال اليدوي...');
                calculatedDaysField.value = '25';
                addLog('✓ تم إدخال 25 يوم يدوياً');
                
            }, 500);
        }

        function runManualInputTest() {
            updateStatus('🔍 بدء اختبار الإدخال اليدوي الشامل...', 'warning');
            addLog('=== بدء اختبار الإدخال اليدوي الشامل ===');
            
            // خطوة 1: إنشاء البيانات
            setTimeout(() => {
                createTestData();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openDaysCalculator();
            }, 2000);
            
            // خطوة 3: اختبار اختيار الشهر
            setTimeout(() => {
                testMonthSelection();
            }, 4000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء اختبار الإدخال اليدوي ===');
                addLog('📋 راجع النتائج أعلاه للتأكد من نجاح الاختبار');
            }, 6000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة اختبار الإدخال اليدوي - جاهز للاختبار!', 'info');
            addLog('تم تحميل مختبر الإدخال اليدوي للأيام');
        });
    </script>
</body>
</html>
