<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم البسيط - نافذة المشروعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #f8f9fa;
            color: #495057;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            margin: 0;
            font-size: 1.8em;
        }

        .content {
            padding: 20px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .test-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.success:hover {
            background: #1e7e34;
        }

        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }

        .test-button.warning:hover {
            background: #e0a800;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .test-button.danger:hover {
            background: #c82333;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .status-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .status-label {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }

        .preview-area {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            min-height: 300px;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram"></i> اختبار التصميم البسيط - نافذة المشروعات</h1>
            <p>اختبار النافذة الجديدة البسيطة مثل نافذة أيام الحضور</p>
        </div>

        <div class="content">
            <!-- قسم الاختبارات الأساسية -->
            <div class="test-section">
                <h3><i class="fas fa-play"></i> الاختبارات الأساسية</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="openProjectsModal()">
                        <i class="fas fa-external-link-alt"></i> فتح نافذة المشروعات
                    </button>
                    
                    <button class="test-button" onclick="testSimpleDesign()">
                        <i class="fas fa-palette"></i> اختبار التصميم البسيط
                    </button>
                    
                    <button class="test-button warning" onclick="addTestProjects()">
                        <i class="fas fa-plus"></i> إضافة مشروعات تجريبية
                    </button>
                    
                    <button class="test-button" onclick="previewTable()">
                        <i class="fas fa-table"></i> معاينة الجدول
                    </button>
                    
                    <button class="test-button" onclick="window.open('/', '_blank')">
                        <i class="fas fa-home"></i> التطبيق الأصلي
                    </button>
                    
                    <button class="test-button danger" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> الإحصائيات</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-number" id="projectsCount">0</div>
                        <div class="status-label">عدد المشروعات</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="functionsCount">0</div>
                        <div class="status-label">الدوال المتاحة</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="testsRun">0</div>
                        <div class="status-label">الاختبارات المنجزة</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="successRate">100%</div>
                        <div class="status-label">معدل النجاح</div>
                    </div>
                </div>
            </div>

            <!-- معاينة الجدول -->
            <div class="test-section">
                <h3><i class="fas fa-table"></i> معاينة الجدول البسيط</h3>
                <div class="preview-area" id="previewArea">
                    <p style="text-align: center; color: #6c757d; padding: 50px;">
                        <i class="fas fa-table" style="font-size: 48px; opacity: 0.3; display: block; margin-bottom: 15px;"></i>
                        انقر على "معاينة الجدول" لعرض التصميم الجديد
                    </p>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> سجل الأحداث</h3>
                <div class="log" id="log">
                    <div class="log-entry log-info">[تحميل] جاهز لاختبار التصميم البسيط...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let testsRun = 0;
        let testsSuccess = 0;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('projectsCount').textContent = projects ? projects.length : 0;
            document.getElementById('testsRun').textContent = testsRun;
            
            const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 100;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function testSimpleDesign() {
            log('🎨 اختبار التصميم البسيط...', 'info');
            testsRun++;
            
            try {
                // اختبار وجود الدوال
                const functions = [
                    'createProjectsTable',
                    'createProjectTableRow',
                    'resetProjectForm',
                    'populateProjectsList'
                ];
                
                let functionsFound = 0;
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        log(`✅ دالة ${funcName} موجودة`, 'success');
                        functionsFound++;
                    } else {
                        log(`❌ دالة ${funcName} غير موجودة`, 'error');
                    }
                });
                
                document.getElementById('functionsCount').textContent = functionsFound;
                
                if (functionsFound === functions.length) {
                    log('✅ جميع دوال التصميم البسيط متاحة', 'success');
                    testsSuccess++;
                } else {
                    log('⚠️ بعض الدوال مفقودة', 'warning');
                }
                
            } catch (error) {
                log('❌ خطأ في اختبار التصميم: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function addTestProjects() {
            log('➕ إضافة مشروعات تجريبية...', 'info');
            testsRun++;
            
            try {
                const testProjects = [
                    {
                        id: Date.now() + 1,
                        name: 'مشروع تطوير النظام',
                        description: 'تطوير نظام إدارة الموظفين',
                        po: 'PO-001',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        name: 'مشروع التدريب',
                        description: 'برنامج تدريب الموظفين الجدد',
                        po: 'PO-002',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ];
                
                testProjects.forEach(project => {
                    projects.push(project);
                    log(`✅ تم إضافة: ${project.name}`, 'success');
                });
                
                saveProjectsToLocalStorage();
                testsSuccess++;
                log(`🎉 تم إضافة ${testProjects.length} مشروع تجريبي`, 'success');
                
            } catch (error) {
                log('❌ خطأ في إضافة المشروعات: ' + error.message, 'error');
            }
            
            updateStats();
        }

        function previewTable() {
            log('👁️ معاينة الجدول البسيط...', 'info');
            testsRun++;
            
            const previewArea = document.getElementById('previewArea');
            
            try {
                if (projects.length === 0) {
                    previewArea.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; opacity: 0.3; display: block; margin-bottom: 15px;"></i>
                            <h4>لا توجد مشروعات</h4>
                            <p>أضف مشروعات تجريبية أولاً</p>
                        </div>
                    `;
                    log('⚠️ لا توجد مشروعات لعرضها', 'warning');
                    updateStats();
                    return;
                }
                
                const table = createProjectsTable();
                previewArea.innerHTML = '';
                previewArea.appendChild(table);
                
                log(`✅ تم عرض ${projects.length} مشروع في الجدول البسيط`, 'success');
                testsSuccess++;
                
            } catch (error) {
                log('❌ خطأ في إنشاء الجدول: ' + error.message, 'error');
                previewArea.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #dc3545;">
                        <i class="fas fa-times-circle" style="font-size: 48px; opacity: 0.3; display: block; margin-bottom: 15px;"></i>
                        <h4>خطأ في إنشاء الجدول</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
            
            updateStats();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
            testsRun = 0;
            testsSuccess = 0;
            updateStats();
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة اختبار التصميم البسيط', 'success');
            updateStats();
            
            setTimeout(() => {
                log('🔍 تشغيل اختبار تلقائي...', 'info');
                testSimpleDesign();
            }, 1000);
        });
    </script>
</body>
</html>
