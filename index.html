<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية</title>
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة مكتبة SheetJS لتصدير البيانات إلى إكسل -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
        }

        header {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo {
            margin-left: 20px;
        }

        .main-container {
            width: 100%;
            height: calc(100vh - 70px);
        }

        .content {
            width: 100%;
            padding: 20px;
            overflow-y: auto;
            height: 100%;
        }

        .search-bar {
            display: flex;
            margin-bottom: 20px;
            align-items: center;
        }

        .search-bar input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        button {
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }

        .search-btn {
            background-color: #f0f0f0;
        }

        .add-btn {
            background-color: #4CAF50;
            color: white;
        }

        .import-btn {
            background-color: #2196F3;
            color: white;
        }

        /* تنسيق زر استيراد من إكسل */
        .import-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .import-btn:hover {
            background-color: #45a049;
        }

        .import-btn::before {
            content: "\f1c3";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            margin-left: 5px;
        }

        .categories {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .category {
            background-color: white;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
        }

        .category h3 {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        /* أنماط رأس قائمة الموظفين */
        .employees-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .employees-header h3 {
            margin: 0;
            padding: 0;
            border: none;
        }

        .bulk-activation {
            display: flex !important;
            align-items: center;
            gap: 10px;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* أنماط checkbox تنشيط الكل - تصميم مصغر وبسيط */
        .activate-all-container {
            display: flex !important;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            user-select: none;
            font-size: 13px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.2s ease;
            padding: 6px 10px;
            border-radius: 20px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            visibility: visible !important;
            opacity: 1 !important;
            min-width: fit-content;
        }

        .activate-all-container:hover {
            color: #28a745;
            background: #e8f5e8;
            border-color: #c3e6cb;
        }

        .activate-all-container input[type="checkbox"] {
            width: 14px;
            height: 14px;
            cursor: pointer;
            accent-color: #28a745;
            margin: 0;
        }

        .activate-all-container i {
            font-size: 12px;
            color: inherit;
            transition: color 0.2s ease;
        }

        .activate-all-text {
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
        }

        /* تأثير عند التفعيل */
        .activate-all-container input[type="checkbox"]:checked {
            accent-color: #28a745;
        }

        .activate-all-container input[type="checkbox"]:checked ~ .activate-all-text,
        .activate-all-container input[type="checkbox"]:checked ~ i {
            color: #28a745;
        }

        .activate-all-container input[type="checkbox"]:checked + i + .activate-all-text {
            color: #28a745;
            font-weight: 600;
        }

        /* أنماط زر إدارة التواريخ الدائري */
        .date-management-btn {
            display: flex !important;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            user-select: none;
            color: white !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 0;
            width: 45px;
            height: 45px;
            border-radius: 50% !important;
            border: 2px solid;
            visibility: visible !important;
            opacity: 1 !important;
            margin-right: 15px;
            position: relative;
            overflow: hidden;
        }

        /* حالة التطبيق (أزرق) */
        .date-management-btn.apply-mode,
        .date-management-btn {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            border-color: #0056b3 !important;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .date-management-btn:hover {
            background: linear-gradient(135deg, #0056b3, #004085) !important;
            border-color: #004085 !important;
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        /* حالة الإلغاء (أحمر) */
        .date-management-btn.cancel-mode {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            border-color: #c82333 !important;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .date-management-btn.cancel-mode:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a) !important;
            border-color: #a71e2a !important;
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }

        .date-management-btn i {
            font-size: 18px;
            color: white !important;
            transition: transform 0.3s ease;
        }

        .date-management-btn:hover i {
            transform: rotate(15deg) scale(1.1);
        }

        .date-management-btn:active {
            transform: scale(0.95);
        }

        /* تأثير التحويل */
        .date-management-btn.transitioning {
            transform: scale(0.9);
        }

        .date-management-btn.transitioning i {
            transform: rotate(180deg) scale(0.8);
        }

        /* تأثير النبض للفت الانتباه */
        @keyframes pulse-circle {
            0% {
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.6);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
                transform: scale(1);
            }
        }

        @keyframes pulse-circle-red {
            0% {
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.6);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
                transform: scale(1);
            }
        }

        .date-management-btn.pulse {
            animation: pulse-circle 2s infinite;
        }

        .date-management-btn.cancel-mode.pulse {
            animation: pulse-circle-red 2s infinite;
        }

        /* تحسين تخطيط bulk-activation */
        .bulk-activation {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        /* تجاوب للشاشات الصغيرة */
        @media (max-width: 768px) {
            .date-management-btn {
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }

            .date-management-btn i {
                font-size: 16px;
            }

            .bulk-activation {
                gap: 10px;
            }
        }

        /* تأثير الضوء الداخلي */
        .date-management-btn::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .date-management-btn:hover::before {
            opacity: 0.8;
        }

        /* تجاوب للشاشات الصغيرة */
        @media (max-width: 768px) {
            .employees-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .activate-all-container {
                font-size: 12px;
                padding: 4px 8px;
                gap: 4px;
            }

            .activate-all-container input[type="checkbox"] {
                width: 12px;
                height: 12px;
            }

            .activate-all-container i {
                font-size: 10px;
            }

            .activate-all-text {
                font-size: 12px;
            }
        }

        .employees {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            padding: 10px 0;
        }

        /* تصميم متجاوب للبطاقات */
        @media (max-width: 1600px) {
            .employees {
                grid-template-columns: repeat(5, 1fr);
            }
        }

        @media (max-width: 1300px) {
            .employees {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 1000px) {
            .employees {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 750px) {
            .employees {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }

        @media (max-width: 500px) {
            .employees {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        .employee-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
            min-height: 220px;
        }

        .employee-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #007bff;
            opacity: 0;
            transition: all 0.3s ease;
            border-radius: 12px 12px 0 0;
        }

        .employee-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .employee-card:hover::before {
            opacity: 1;
        }

        .employee-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
            position: relative;
        }

        /* أنماط checkbox في بطاقة الموظف */
        .employee-checkbox {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
        }

        .employee-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: #007bff;
            border-radius: 3px;
        }

        .employee-card.selected {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
        }

        .employee-card.selected::before {
            opacity: 1;
            background: #007bff;
        }

        .employee-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-left: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .employee-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .employee-card:hover .employee-photo {
            border-color: #007bff;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        }

        .employee-basic-info {
            flex: 1;
            min-width: 0;
            position: relative;
        }

        .employee-name {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.3;
            transition: all 0.3s ease;
        }

        .employee-card:hover .employee-name {
            color: #007bff;
        }

        .employee-position {
            margin: 0 0 6px 0;
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .employee-card:hover .employee-position {
            color: #495057;
        }

        .employee-code-line {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 4px;
        }

        .employee-code {
            background: #007bff;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .employee-card:hover .employee-code {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .employment-type {
            background: #28a745;
            color: white;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 9px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .employee-card:hover .employment-type {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .salary-info {
            margin: 8px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            position: relative;
            transition: all 0.3s ease;
        }

        .salary-info::before {
            content: '💰';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 12px;
            opacity: 0.6;
        }

        .employee-card:hover .salary-info {
            background: #e3f2fd;
            border-color: #007bff;
            transform: translateY(-1px);
        }

        .salary-info p {
            margin: 0;
            font-size: 11px;
            color: #495057;
            font-weight: 500;
        }

        .salary-amount {
            font-weight: 700;
            color: #28a745;
            font-size: 12px;
        }

        /* أنماط أزرار بطاقات الموظفين */
        .employee-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            text-decoration: none;
            min-width: 70px;
            justify-content: center;
        }

        .action-btn i {
            font-size: 14px;
        }

        .action-btn span {
            font-size: 11px;
        }

        .edit-btn {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }

        .edit-btn:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
        }

        .salary-btn {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .salary-btn:hover {
            background: linear-gradient(135deg, #1e7e34, #155724);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        .delete-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        .delete-btn:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .employee-actions {
                gap: 6px;
            }

            .action-btn {
                padding: 6px 10px;
                font-size: 11px;
                min-width: 60px;
            }

            .action-btn i {
                font-size: 12px;
            }

            .action-btn span {
                font-size: 10px;
            }
        }

        @media (max-width: 480px) {
            .employee-actions {
                gap: 4px;
            }

            .action-btn {
                padding: 5px 8px;
                font-size: 10px;
                min-width: 50px;
            }

            .action-btn span {
                display: none;
            }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            position: relative;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        /* تحسينات للنوافذ القابلة للسحب */
        .draggable-modal {
            transition: box-shadow 0.3s ease;
        }

        .draggable-modal:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .draggable-header {
            position: relative;
            background-color: #f8f9fa;
            color: #495057;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draggable-header::before {
            content: "⋮⋮";
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: rgba(73,80,87,0.5);
            letter-spacing: 2px;
        }

        .draggable-header:hover::before {
            color: rgba(73,80,87,0.8);
        }

        .draggable-header h2 {
            margin: 0;
            margin-left: 30px;
            font-size: 1.2em;
        }

        .draggable-header .close {
            color: #6c757d;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .draggable-header .close:hover {
            color: #495057;
        }

        /* تخصيص شريط التمرير للنوافذ */
        .modal-body {
            scrollbar-width: thin;
            scrollbar-color: #6c757d #f1f1f1;
        }

        .modal-body::-webkit-scrollbar {
            width: 12px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 6px;
            border: 2px solid #f1f1f1;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }

        .modal-body::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* تحسين المظهر العام للنافذة مع شريط التمرير */
        .draggable-modal {
            border-radius: 8px;
            overflow: hidden;
        }

        .modal-body {
            direction: rtl;
        }

        .modal-body form {
            direction: rtl;
        }

        /* تصميم الجدول المدمج المصغر */
        .days-records-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            background: white;
        }

        .days-records-table th {
            background: #f8f9fa;
            color: #495057;
            padding: 6px 4px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 10px;
        }

        .days-records-table td {
            padding: 5px 4px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-size: 10px;
        }

        .days-records-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .days-records-table tr:hover {
            background: #f1f3f4;
        }

        /* أزرار التعديل والحذف المصغرة */
        .record-action-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            margin: 1px;
            transition: all 0.2s ease;
        }

        .edit-record-btn {
            background: #6c757d;
            color: white;
        }

        .edit-record-btn:hover {
            background: #5a6268;
        }

        .delete-record-btn {
            background: #6c757d;
            color: white;
        }

        .delete-record-btn:hover {
            background: #5a6268;
        }

        .save-record-btn {
            background: #495057;
            color: white;
        }

        .save-record-btn:hover {
            background: #343a40;
        }

        .cancel-record-btn {
            background: #6c757d;
            color: white;
        }

        .cancel-record-btn:hover {
            background: #5a6268;
        }

        /* حقول التعديل المباشر */
        .inline-edit-input {
            width: 100%;
            padding: 2px 4px;
            border: 1px solid #6c757d;
            border-radius: 3px;
            font-size: 10px;
            text-align: center;
        }

        .inline-edit-select {
            width: 100%;
            padding: 2px;
            border: 1px solid #6c757d;
            border-radius: 3px;
            font-size: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
            padding: 5px 8px;
            margin-top: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .delete-btn:hover {
            background-color: #d32f2f;
        }

        /* أنماط أزرار الإجراءات */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            margin-top: 12px;
        }

        .action-btn {
            padding: 8px 10px;
            font-size: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .action-btn i {
            font-size: 10px;
            transition: transform 0.3s ease;
        }

        /* الصف الأول من الأزرار */
        .edit-btn {
            background: #007bff;
            color: white;
        }

        .edit-btn:hover {
            background: #0056b3;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        /* الصف الثاني من الأزرار */
        .salary-btn {
            background: #17a2b8;
            color: white;
        }

        .salary-btn:hover {
            background: #138496;
        }

        /* تم حذف أنماط زر حساب الأيام */

        /* تم إزالة CSS زر السجلات - لم يعد مطلوباً */

        /* الصف الثالث من الأزرار */
        .advances-btn {
            background: #28a745;
            color: white;
        }

        .advances-btn:hover {
            background: #1e7e34;
        }





        /* تخطيط خاص للأزرار الستة */
        .action-buttons.six-buttons {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(3, 1fr);
        }

        .action-buttons.seven-buttons {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(4, 1fr);
            gap: 6px;
        }

        .action-buttons.eight-buttons {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(4, 1fr);
            gap: 6px;
        }

        .action-buttons.nine-buttons {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(5, 1fr);
            gap: 5px;
        }



        /* أنماط حقل الصورة */
        .photo-input-container {
            display: flex;
            margin-bottom: 10px;
        }

        .photo-input-container input {
            flex: 1;
            margin-left: 10px;
        }

        .photo-upload-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
        }

        .photo-upload-btn:hover {
            background-color: #45a049;
        }

        .photo-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin: 10px auto;
            border: 2px solid #ddd;
        }

        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* أنماط زر عرض الجدول */
        .table-view-btn {
            background-color: #673AB7;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
        }

        .table-view-btn:hover {
            background-color: #5E35B1;
        }

        /* أنماط زر المشروعات */
        .projects-btn {
            background-color: #FF9800;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
        }

        .projects-btn:hover {
            background-color: #F57C00;
        }



        /* أنماط زر تقارير الرواتب */
        .salary-reports-btn {
            background-color: #FF5722;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
        }

        .salary-reports-btn:hover {
            background-color: #E64A19;
        }

        /* أنماط نافذة الجدول المنبثقة */
        .table-modal-content {
            width: 90%;
            max-width: 1200px;
            max-height: 85vh;
            overflow: hidden;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }

        .print-btn, .export-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .print-btn:hover, .export-btn:hover {
            background-color: #0b7dda;
        }

        .print-btn i, .export-btn i {
            margin-left: 5px;
        }

        .table-container {
            overflow: auto;
            margin-top: 10px;
            max-height: 70vh;
            width: 100%;
            border: 1px solid #ddd;
        }

        #employeesTable {
            width: 100%;
            border-collapse: collapse;
            direction: rtl;
            font-size: 12px;
        }

        #employeesTable thead {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 10;
        }

        #employeesTable th {
            background-color: #f2f2f2;
            border: 1px solid #ddd;
            padding: 6px;
            text-align: center !important;
            white-space: nowrap;
            font-weight: bold;
            font-size: 12px;
        }

        #employeesTable td {
            border: 1px solid #ddd;
            padding: 5px;
            text-align: center !important;
            white-space: nowrap;
            font-size: 12px;
        }

        #employeesTable tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #employeesTable tr:hover {
            background-color: #f1f1f1;
        }

        /* أنماط زر إزالة المكررين */
        .remove-duplicates-btn {
            background-color: #9C27B0;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 5px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
        }

        .remove-duplicates-btn:hover {
            background-color: #7B1FA2;
        }

        .remove-duplicates-btn i {
            margin-left: 5px;
        }

        /* شريط الترتيب والفلترة */
        .sort-filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .sort-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sort-controls label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .sort-controls select {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background: white;
            font-size: 13px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sort-controls select:hover {
            border-color: #007bff;
        }

        .sort-controls select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .view-controls {
            display: flex;
            gap: 5px;
        }

        .view-btn {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            background: white;
            color: #6c757d;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .view-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .view-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .view-btn i {
            font-size: 12px;
        }

        /* تصميم متجاوب لشريط الترتيب */
        @media (max-width: 768px) {
            .sort-filter-bar {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .sort-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .view-controls {
                justify-content: center;
            }
        }

        /* تنسيق تشيك بوكس داخل البطاقات */
        .employee-checkbox {
            position: absolute;
            top: 12px;
            left: 12px;
            width: 18px;
            height: 18px;
            cursor: pointer;
            z-index: 5;
            accent-color: #007bff;
        }

        /* تنسيق حالة التحديد للبطاقة */
        .employee-card.selected {
            border: 2px solid #007bff;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
            transform: translateY(-3px);
        }

        .employee-card.selected::before {
            opacity: 1;
        }

        /* تحسينات إضافية للبطاقات */
        .employee-card {
            position: relative;
        }

        /* إضافة مؤشر حالة الموظف */
        .employee-status {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .employee-card.inactive .employee-status {
            background: #dc3545;
        }

        /* تحسين أيقونات الراتب */
        .salary-info i {
            color: #28a745;
            margin-left: 6px;
            width: 14px;
            text-align: center;
        }

        /* تأثيرات hover للأزرار */
        .action-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* تحسين النصوص */
        .employee-name {
            text-shadow: none;
        }

        .employee-position {
            font-style: normal;
        }

        /* تصميم متجاوب للبطاقات */
        @media (max-width: 768px) {
            .employees {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .employee-card {
                padding: 12px;
            }

            .employee-header {
                flex-direction: column;
                text-align: center;
                align-items: center;
            }

            .employee-photo {
                margin: 0 0 10px 0;
            }

            .employee-basic-info {
                text-align: center;
            }

            .action-buttons {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .action-btn {
                padding: 10px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .employee-card {
                padding: 10px;
            }

            .employee-photo {
                width: 50px;
                height: 50px;
            }

            .employee-name {
                font-size: 14px;
            }

            .employee-position {
                font-size: 12px;
            }

            .employee-code, .employment-type {
                font-size: 10px;
                padding: 1px 6px;
            }

            .salary-info {
                padding: 6px;
                margin: 6px 0;
            }

            .salary-info p {
                font-size: 11px;
            }
        }

        /* عرض القائمة */
        .employees.list-view {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .employees.list-view .employee-card {
            padding: 12px 16px;
            border-radius: 8px;
        }

        .employees.list-view .employee-header {
            flex-direction: row;
            align-items: center;
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .employees.list-view .employee-photo {
            width: 50px;
            height: 50px;
            margin-left: 12px;
        }

        .employees.list-view .employee-basic-info {
            flex: 1;
        }

        .employees.list-view .employee-name {
            font-size: 15px;
            margin-bottom: 2px;
        }

        .employees.list-view .employee-position {
            font-size: 12px;
            margin-bottom: 4px;
        }

        .employees.list-view .salary-info {
            display: none;
        }

        .employees.list-view .action-buttons {
            display: flex;
            flex-direction: row;
            gap: 4px;
            margin-top: 0;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .employees.list-view .action-btn {
            padding: 4px 8px;
            font-size: 10px;
            min-width: auto;
        }

        .employees.list-view .action-btn span {
            display: none;
        }

        .employees.list-view .action-btn i {
            margin: 0;
        }

        /* تحسينات للأزرار السبعة والثمانية في العرض المتجاوب */
        @media (max-width: 768px) {
            .action-buttons.seven-buttons,
            .action-buttons.eight-buttons {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .action-buttons.seven-buttons {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .action-buttons.seven-buttons .action-btn {
                padding: 8px;
                font-size: 11px;
            }
        }

        /* أنماط شريط التمرير للنوافذ الجديدة */
        .modal-content::-webkit-scrollbar,
        .modal-content div::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .modal-content::-webkit-scrollbar-track,
        .modal-content div::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .modal-content::-webkit-scrollbar-thumb,
        .modal-content div::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .modal-content::-webkit-scrollbar-thumb:hover,
        .modal-content div::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .modal-content::-webkit-scrollbar-corner,
        .modal-content div::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* شريط تمرير مخصص لجدول الموظفين */
        .employee-table-container::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        .employee-table-container::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 5px;
        }

        .employee-table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .employee-table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
        }

        /* أنماط نافذة إدارة الراتب البسيطة */
        .salary-modal-content {
            width: 600px;
            max-width: 95%;
            margin: 3% auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        .salary-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #dee2e6;
            flex-shrink: 0;
        }

        .salary-header .header-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .salary-header .header-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #6c757d, #495057);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .salary-header .header-text h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: -0.5px;
        }

        .salary-header .header-text p {
            margin: 5px 0 0 0;
            font-size: 14px;
            color: #6c757d;
            font-weight: 400;
        }

        .salary-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
        }

        .salary-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .form-section h3 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }

        .form-section h3 i {
            color: #6c757d;
            font-size: 18px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .salary-form .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .salary-form .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .salary-form .form-label i {
            color: #6c757d;
            width: 16px;
            text-align: center;
        }

        .salary-form .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            color: #495057;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .salary-form .form-input:focus {
            outline: none;
            border-color: #adb5bd;
            box-shadow: 0 0 0 3px rgba(173, 181, 189, 0.1);
            transform: translateY(-1px);
        }

        .salary-form .form-input[readonly] {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        .salary-form .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            color: #495057;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .salary-form .form-textarea:focus {
            outline: none;
            border-color: #adb5bd;
            box-shadow: 0 0 0 3px rgba(173, 181, 189, 0.1);
        }

        .salary-form .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 10px;
        }

        .salary-form .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
            min-width: 140px;
            justify-content: center;
        }

        .salary-form .btn-calculate {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        .salary-form .btn-calculate:hover {
            background: linear-gradient(135deg, #138496, #117a8b);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(23, 162, 184, 0.4);
        }

        .salary-form .btn-save {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .salary-form .btn-save:hover {
            background: linear-gradient(135deg, #5a6268, #3d4043);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
        }

        .salary-form .btn-print {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .salary-form .btn-print:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .salary-modal-content {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .salary-header {
                padding: 20px;
            }

            .salary-header .header-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .salary-header .header-text h2 {
                font-size: 18px;
            }

            .salary-body {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .salary-form .form-actions {
                flex-direction: column;
            }

            .salary-form .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .salary-header {
                padding: 15px;
            }

            .salary-header .header-content {
                gap: 10px;
            }

            .salary-header .header-text h2 {
                font-size: 16px;
            }

            .salary-header .header-text p {
                font-size: 12px;
            }

            .salary-body {
                padding: 15px;
            }

            .salary-form .form-input,
            .salary-form .form-textarea {
                padding: 10px 12px;
            }

            .form-section {
                padding: 15px;
            }
        }

        /* تحسين زر الراتب في بطاقات الموظفين */
        .salary-btn {
            background: linear-gradient(135deg, #28a745, #1e7e34) !important;
            border: none !important;
            color: white !important;
            transition: all 0.2s ease !important;
        }

        .salary-btn:hover {
            background: linear-gradient(135deg, #1e7e34, #155724) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
        }

        /* أنماط نافذة حساب الراتب البسيطة جداً */
        .simple-salary-modal {
            width: 400px;
            max-width: 90%;
            margin: 5% auto;
            background: white;
            border: 1px solid #ccc;
            font-family: Arial, sans-serif;
            font-size: 13px;
        }

        .simple-header {
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .simple-header h3 {
            margin: 0;
            font-size: 14px;
            font-weight: normal;
            color: #333;
        }

        .simple-close {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #666;
        }

        .simple-body {
            padding: 15px;
        }

        .simple-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .simple-row label {
            font-size: 12px;
            color: #555;
            min-width: 40px;
        }

        .simple-input {
            padding: 4px 6px;
            border: 1px solid #ccc;
            font-size: 12px;
            width: 80px;
        }

        .simple-info {
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #eee;
            font-size: 11px;
            display: flex;
            justify-content: space-between;
        }

        .simple-table {
            margin: 15px 0;
        }

        .simple-table table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .simple-table th,
        .simple-table td {
            border: 1px solid #ddd;
            padding: 4px 6px;
            text-align: center;
        }

        .simple-table th {
            background: #f5f5f5;
            font-weight: normal;
            font-size: 10px;
        }

        .simple-summary {
            margin: 15px 0;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 3px 0;
            font-size: 11px;
        }

        .summary-item.total {
            font-weight: bold;
            border-top: 1px solid #ddd;
            padding-top: 6px;
            margin-top: 6px;
        }

        .simple-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-top: 15px;
        }

        .simple-buttons button {
            padding: 6px 12px;
            border: 1px solid #ccc;
            background: white;
            font-size: 11px;
            cursor: pointer;
        }

        .simple-buttons button:hover {
            background: #f0f0f0;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 480px) {
            .simple-salary-modal {
                width: 95%;
                margin: 2% auto;
            }

            .simple-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .simple-input {
                width: 100px;
            }

            .simple-info {
                flex-direction: column;
                gap: 5px;
            }

            .simple-buttons {
                flex-direction: column;
            }

            .simple-buttons button {
                width: 100%;
            }
        }

        .calculation-section {
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }

        .employee-info-display {
            background: #fff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .info-value {
            color: #28a745;
            font-weight: 700;
            font-size: 14px;
        }

        .projects-table-container {
            margin: 20px 0;
        }

        .projects-table-container h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .projects-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .projects-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 12px;
            text-align: center;
            border-bottom: 2px solid #dee2e6;
            font-size: 13px;
        }

        .projects-table td {
            padding: 10px 12px;
            text-align: center;
            border-bottom: 1px solid #f1f3f4;
            font-size: 13px;
            color: #495057;
        }

        .projects-table tbody tr:hover {
            background: #f8f9fa;
        }

        .projects-table .project-total {
            font-weight: 700;
            color: #28a745;
        }

        .salary-summary {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.1);
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .summary-row:last-child {
            border-bottom: none;
            padding-top: 15px;
            margin-top: 10px;
            border-top: 2px solid #28a745;
        }

        .summary-label {
            font-weight: 600;
            color: #495057;
            font-size: 15px;
        }

        .summary-value {
            font-weight: 700;
            color: #6c757d;
            font-size: 15px;
        }

        .total-amount {
            color: #28a745 !important;
            font-size: 18px !important;
            background: #e8f5e8;
            padding: 8px 15px;
            border-radius: 6px;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .projects-table {
                font-size: 11px;
            }

            .projects-table th,
            .projects-table td {
                padding: 8px 6px;
            }

            .employee-info-display {
                padding: 12px;
            }

            .salary-summary {
                padding: 15px;
            }

            .summary-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }

        /* أنماط نافذة الموظف الجديد المحسنة */
        .new-employee-modal-content {
            width: 600px;
            max-width: 95%;
            margin: 3% auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .new-employee-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #dee2e6;
            flex-shrink: 0;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #6c757d, #495057);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .header-text h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: -0.5px;
        }

        .header-text p {
            margin: 5px 0 0 0;
            font-size: 14px;
            color: #6c757d;
            font-weight: 400;
        }

        .close-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: #f8f9fa;
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #e9ecef;
            color: #495057;
            transform: scale(1.05);
        }

        .new-employee-body {
            padding: 30px;
            overflow-y: auto;
            flex: 1;
        }

        .employee-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .form-label i {
            color: #6c757d;
            width: 16px;
            text-align: center;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            color: #495057;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #adb5bd;
            box-shadow: 0 0 0 3px rgba(173, 181, 189, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #adb5bd;
            font-style: italic;
        }

        .photo-group {
            grid-column: 1 / -1;
        }

        .photo-upload-container {
            position: relative;
        }

        .photo-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .photo-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9fa;
        }

        .photo-upload-area:hover {
            border-color: #adb5bd;
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .photo-upload-area i {
            font-size: 32px;
            color: #6c757d;
            margin-bottom: 10px;
            display: block;
        }

        .photo-upload-area span {
            color: #495057;
            font-weight: 500;
            font-size: 14px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            margin-top: 10px;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
            min-width: 140px;
            justify-content: center;
        }

        .btn-save {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #5a6268, #3d4043);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
        }

        .btn-records {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .btn-records:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .new-employee-modal-content {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .new-employee-header {
                padding: 20px;
            }

            .header-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .header-text h2 {
                font-size: 18px;
            }

            .new-employee-body {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .new-employee-header {
                padding: 15px;
            }

            .header-content {
                gap: 10px;
            }

            .header-text h2 {
                font-size: 16px;
            }

            .header-text p {
                font-size: 12px;
            }

            .new-employee-body {
                padding: 15px;
            }

            .form-input {
                padding: 10px 12px;
            }

            .photo-upload-area {
                padding: 20px;
            }

            .photo-upload-area i {
                font-size: 24px;
            }
        }







        /* أنماط شريط التمرير للوضع المظلم */
        @media (prefers-color-scheme: dark) {
            .modal-content::-webkit-scrollbar-track,
            .modal-content div::-webkit-scrollbar-track {
                background: #2d3748;
            }

            .modal-content::-webkit-scrollbar-thumb,
            .modal-content div::-webkit-scrollbar-thumb {
                background: #4a5568;
            }

            .modal-content::-webkit-scrollbar-thumb:hover,
            .modal-content div::-webkit-scrollbar-thumb:hover {
                background: #718096;
            }

            .employee-table-container::-webkit-scrollbar-track {
                background: #2d3748;
            }

            .employee-table-container::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, #4299e1, #3182ce);
            }

            .employee-table-container::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, #3182ce, #2c5282);
            }
        }

        /* تحسين الألوان للوضع المظلم */
        @media (prefers-color-scheme: dark) {
            .employee-card {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                border-color: #4a5568;
                color: #e2e8f0;
            }

            .employee-name {
                color: #e2e8f0;
            }

            .employee-position {
                color: #a0aec0;
            }

            .salary-info {
                background: #4a5568;
                border-left-color: #48bb78;
            }


        }

        /* تنسيق رسالة عدم وجود نتائج */
        .no-results {
            text-align: center;
            padding: 30px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin: 20px 0;
        }

        .no-results i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 10px;
            display: block;
        }

        .no-results p {
            font-size: 18px;
            color: #666;
        }

        /* تنسيق عنوان نتائج البحث */
        .search-results-header {
            background-color: #f0f0f0;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .search-results-header p {
            margin: 0;
            font-size: 14px;
            color: #555;
        }

        /* تحسين مظهر حقل البحث */
        .search-bar input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .search-bar input:focus {
            border-color: #4CAF50;
            outline: none;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }

        /* تنسيق زر البحث */
        .search-btn {
            background-color: #f0f0f0;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .search-btn:hover {
            background-color: #e0e0e0;
        }

        /* تعديل حجم النافذة المنبثقة لإضافة وتعديل الموظف */
        #employeeModalContent {
            width: 60%;
            max-width: 600px;
            margin: 5% auto;
            height: auto;
            max-height: 80vh;
            overflow-y: auto;
        }

        /* تعديل حجم النافذة المنبثقة للراتب والبدلات */
        #salaryModalContent {
            width: 70%;
            max-width: 700px;
            margin: 3% auto;
            height: auto;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border-radius: 12px;
        }

        /* تحسين أنماط نافذة الراتب */
        #salaryModal .form-group {
            margin-bottom: 20px;
        }

        #salaryModal .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        #salaryModal .form-group input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        #salaryModal .form-group input[type="number"]:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
            outline: none;
        }

        #salaryModal .form-group small {
            display: block;
            margin-top: 5px;
            font-style: italic;
        }

        /* تحسين أزرار نافذة الراتب */
        #salaryModal .form-actions button {
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: none;
            border-radius: 8px !important;
        }

        #salaryModal .form-actions button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        #salaryModal .form-actions button:active {
            transform: translateY(0);
        }

        /* أنماط نافذة المشروعات */
        #projectsModalContent {
            width: 80%;
            max-width: 900px;
            margin: 2% auto;
            height: auto;
            max-height: 90vh;
            overflow-y: auto;
        }

        .projects-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .add-project-section, .projects-list-section {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .add-project-section h3, .projects-list-section h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #FF9800;
            padding-bottom: 5px;
        }

        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: inherit;
        }

        .projects-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .projects-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .project-card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .project-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .project-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .status-planning { background-color: #FFC107; }
        .status-in-progress { background-color: #2196F3; }
        .status-completed { background-color: #4CAF50; }
        .status-on-hold { background-color: #FF5722; }

        .project-details {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .project-dates {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 12px;
            color: #888;
        }

        .project-budget {
            font-weight: bold;
            color: #FF9800;
            margin-top: 5px;
        }

        .project-actions {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .project-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .edit-project-btn {
            background-color: #2196F3;
            color: white;
        }

        .delete-project-btn {
            background-color: #f44336;
            color: white;
        }

        /* تنسيق responsive للمشروعات */
        @media (max-width: 768px) {
            .projects-container {
                grid-template-columns: 1fr;
            }

            #projectsModalContent {
                width: 95%;
                margin: 1% auto;
            }
        }

        /* أنماط زر التقارير */
        .reports-btn {
            background-color: #17a2b8;
            color: white;
        }

        .reports-btn:hover {
            background-color: #138496;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">
            <h2 style="color: #FF7F00;">Osco Engineering Solutions</h2>
            <p style="margin: 0; font-size: 14px; color: #000;">اســـــكو للحلول الهندسيه</p>
        </div>

        <!-- زر إعدادات البرنامج -->
        <div style="margin-right: auto;">
            <button id="settingsBtn" onclick="openSettingsModal()" style="
                background: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 6px;
                transition: all 0.2s ease;
            " onmouseover="this.style.background='#e9ecef'"
               onmouseout="this.style.background='#f8f9fa'">
                <i class="fas fa-cog"></i>
                <span>إعدادات البرنامج</span>
            </button>
        </div>
    </header>

    <div class="main-container">
        <!-- Main content area -->
        <div class="content">
            <div class="search-bar">
                <input type="text" id="searchInput" placeholder="بحث...">
                <button class="search-btn" id="searchBtn">🔍</button>
                <button class="add-btn" id="newEmployeeBtn" onclick="openNewEmployeeModal()">موظف جديد</button>
                <button class="table-view-btn" id="tableViewBtn"><i class="fas fa-table"></i> عرض جدول</button>
                <button class="projects-btn" id="projectsBtn"><i class="fas fa-project-diagram"></i> المشروعات</button>
                <button class="salary-reports-btn" id="salaryReportsBtn"><i class="fas fa-file-invoice-dollar"></i> تقارير الرواتب</button>
                <button class="reports-btn" id="reportsBtn" onclick="openReportsWindow()"><i class="fas fa-chart-bar"></i> التقارير</button>
                <button class="import-btn" id="importExcelBtn">استيراد من إكسل</button>
                <button class="remove-duplicates-btn" id="removeDuplicatesBtn"><i class="fas fa-clone"></i> إزالة المكررين</button>
                <button class="fix-allowances-btn" id="fixAllowancesBtn" onclick="fixDailyAllowances()" title="إصلاح البدلات اليومية لجميع الموظفين"><i class="fas fa-tools"></i> إصلاح البدلات</button>
            </div>

            <!-- شريط الترتيب والفلترة -->
            <div class="sort-filter-bar">
                <div class="sort-controls">
                    <label for="sortBy">ترتيب حسب:</label>
                    <select id="sortBy" onchange="applySorting()">
                        <option value="name">الاسم</option>
                        <option value="position">الوظيفة</option>
                        <option value="employeeCode">الكود الوظيفي</option>
                        <option value="employmentType">نوع التوظيف</option>
                        <option value="salary">الراتب</option>
                    </select>
                    <select id="sortOrder" onchange="applySorting()">
                        <option value="asc">تصاعدي</option>
                        <option value="desc">تنازلي</option>
                    </select>
                </div>
                <div class="view-controls">
                    <button class="view-btn active" id="cardViewBtn" onclick="switchToCardView()">
                        <i class="fas fa-th-large"></i> بطاقات
                    </button>
                    <button class="view-btn" id="listViewBtn" onclick="switchToListView()">
                        <i class="fas fa-list"></i> قائمة
                    </button>
                </div>
            </div>

            <!-- قائمة الموظفين -->
            <div class="category">
                <div class="employees-header">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <h3>قائمة الموظفين</h3>
                        <span id="employeeCount" style="
                            background: #007bff;
                            color: white;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 12px;
                            font-weight: 600;
                            min-width: 60px;
                            text-align: center;
                        ">0 موظف</span>
                    </div>
                    <div class="bulk-activation">
                        <label class="activate-all-container">
                            <input type="checkbox" id="activateAllEmployees" onchange="toggleAllEmployeesActivation()">
                            <i class="fas fa-check-circle"></i>
                            <span class="activate-all-text">تنشيط الكل</span>
                        </label>

                        <!-- زر إدارة التواريخ دائري بجانب تنشيط الكل -->
                        <button class="date-management-btn" id="dateManagementBtn" onclick="toggleDateApplication()" title="تطبيق التاريخ على الكل">
                            <i class="fas fa-calendar-check" id="dateIcon"></i>
                        </button>

                    </div>
                </div>
                <div class="employees" id="employeesContainer"></div>
            </div>
        </div>
    </div>

    <!-- نافذة موظف جديد محسنة -->
    <div id="newEmployeeModal" class="modal" style="display: none; background: rgba(0,0,0,0.5); backdrop-filter: blur(5px);">
        <div class="new-employee-modal-content">
            <div class="new-employee-header">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="header-text">
                        <h2>موظف جديد</h2>
                        <p>إضافة بيانات موظف جديد إلى النظام</p>
                    </div>
                </div>
                <button class="close-btn" onclick="closeNewEmployeeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="new-employee-body">
                <form id="newEmployeeForm" class="employee-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-hashtag"></i>
                                الكود
                            </label>
                            <input type="text" id="newEmployeeCode" class="form-input" placeholder="أدخل كود الموظف" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-user"></i>
                                الاسم الكامل
                            </label>
                            <input type="text" id="newEmployeeName" class="form-input" placeholder="أدخل اسم الموظف" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-briefcase"></i>
                                الوظيفة
                            </label>
                            <input type="text" id="newEmployeePosition" class="form-input" placeholder="أدخل الوظيفة" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-id-card"></i>
                                الرقم القومي
                            </label>
                            <input type="text" id="newEmployeeNationalId" class="form-input" placeholder="أدخل الرقم القومي" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-phone"></i>
                                رقم الموبايل
                            </label>
                            <input type="text" id="newEmployeePhone" class="form-input" placeholder="أدخل رقم الهاتف" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-map-marker-alt"></i>
                                العنوان
                            </label>
                            <input type="text" id="newEmployeeAddress" class="form-input" placeholder="أدخل العنوان">
                        </div>

                        <div class="form-group photo-group">
                            <label class="form-label">
                                <i class="fas fa-camera"></i>
                                صورة الموظف
                            </label>
                            <div class="photo-upload-container">
                                <input type="file" id="newEmployeePhoto" accept="image/*" class="photo-input">
                                <div class="photo-upload-area" onclick="selectPhoto()">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>اضغط لاختيار صورة</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar-alt"></i>
                                تاريخ الالتحاق
                            </label>
                            <input type="date" id="newEmployeeJoinDate" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="saveNewEmployee()" class="btn btn-save">
                            <i class="fas fa-save"></i>
                            حفظ البيانات
                        </button>
                        <button type="button" onclick="openEmployeeRecordsModal()" class="btn btn-records">
                            <i class="fas fa-list"></i>
                            عرض السجل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة عرض سجل الموظفين -->
    <div id="employeeRecordsModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 90%; max-width: 1200px; margin: 2% auto; background: white; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); max-height: 90vh; display: flex; flex-direction: column;">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
                <h2 style="margin: 0; color: #333; font-size: 18px;">سجل الموظفين</h2>
                <span class="close" onclick="closeEmployeeRecordsModal()" style="font-size: 24px; cursor: pointer; color: #999;">&times;</span>
            </div>
            <div style="padding: 20px; overflow-y: auto; flex: 1;">
                <!-- شيك بوكس لاختيار الأعمدة -->
                <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <label style="font-weight: bold; margin-bottom: 10px; display: block;">اختيار الأعمدة:</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showSerial" checked onchange="toggleColumn('serial')"> م</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showCode" checked onchange="toggleColumn('code')"> الكود</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showName" checked onchange="toggleColumn('name')"> الاسم</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showPosition" checked onchange="toggleColumn('position')"> الوظيفة</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showNationalId" checked onchange="toggleColumn('nationalId')"> الرقم القومي</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showPhone" checked onchange="toggleColumn('phone')"> الهاتف</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showAddress" checked onchange="toggleColumn('address')"> العنوان</label>
                        <label style="display: flex; align-items: center; gap: 5px;"><input type="checkbox" id="showJoinDate" checked onchange="toggleColumn('joinDate')"> تاريخ الالتحاق</label>
                    </div>
                </div>

                <!-- جدول الموظفين -->
                <div class="employee-table-container" style="overflow: auto; max-height: 400px; border: 1px solid #ddd; border-radius: 4px; scrollbar-width: thin; scrollbar-color: #888 #f1f1f1;">
                    <table id="employeeRecordsTable" style="width: 100%; border-collapse: collapse; background: white;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th class="col-serial" style="padding: 10px; border: 1px solid #ddd; text-align: center; width: 50px;">م</th>
                                <th class="col-code" style="padding: 10px; border: 1px solid #ddd; text-align: center;">الكود</th>
                                <th class="col-name" style="padding: 10px; border: 1px solid #ddd; text-align: center;">الاسم</th>
                                <th class="col-position" style="padding: 10px; border: 1px solid #ddd; text-align: center;">الوظيفة</th>
                                <th class="col-nationalId" style="padding: 10px; border: 1px solid #ddd; text-align: center;">الرقم القومي</th>
                                <th class="col-phone" style="padding: 10px; border: 1px solid #ddd; text-align: center;">الهاتف</th>
                                <th class="col-address" style="padding: 10px; border: 1px solid #ddd; text-align: center;">العنوان</th>
                                <th class="col-joinDate" style="padding: 10px; border: 1px solid #ddd; text-align: center;">تاريخ الالتحاق</th>
                                <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeeRecordsTableBody">
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 15px; text-align: center;">
                    <button onclick="saveAllEmployeeChanges()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">حفظ جميع التغييرات</button>
                    <button onclick="exportEmployeesToExcel()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">تصدير إلى إكسل</button>
                    <button onclick="exportEmployeesToPDF()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حساب الراتب البسيطة جداً -->
    <div id="salaryModal" class="modal" style="display: none;">
        <div class="simple-salary-modal">
            <div class="simple-header">
                <h3>حساب الراتب</h3>
                <span id="salaryEmployeeName">الموظف</span>
                <button class="simple-close" onclick="closeSalaryModal()">×</button>
            </div>
            <div class="simple-body">
                <input type="hidden" id="salaryEmployeeId">

                <!-- حقول الشهر والسنة -->
                <div class="simple-row">
                    <label>الشهر:</label>
                    <select id="salaryMonth" class="simple-input" onchange="calculateProjectSalary()">
                        <option value="">اختر</option>
                        <option value="1">يناير</option>
                        <option value="2">فبراير</option>
                        <option value="3">مارس</option>
                        <option value="4">أبريل</option>
                        <option value="5">مايو</option>
                        <option value="6">يونيو</option>
                        <option value="7">يوليو</option>
                        <option value="8">أغسطس</option>
                        <option value="9">سبتمبر</option>
                        <option value="10">أكتوبر</option>
                        <option value="11">نوفمبر</option>
                        <option value="12">ديسمبر</option>
                    </select>

                    <label>السنة:</label>
                    <select id="salaryYear" class="simple-input" onchange="calculateProjectSalary()">
                        <option value="">اختر</option>
                    </select>
                </div>

                <!-- معلومات الموظف -->
                <div class="simple-info">
                    <span>نوع الموظف: <strong id="employeeType">يومي</strong></span>
                    <span>أجر اليوم: <strong id="dailyWage">100 جنيه</strong></span>
                </div>

                <!-- جدول المشاريع -->
                <div class="simple-table">
                    <table>
                        <thead>
                            <tr>
                                <th>المشروع</th>
                                <th>الأيام</th>
                                <th>الأجر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody id="projectsTableBody">
                            <!-- بيانات تلقائية -->
                        </tbody>
                    </table>
                </div>

                <!-- ملخص الراتب -->
                <div class="simple-summary">
                    <div class="summary-item">
                        <span>إجمالي الأيام:</span>
                        <span id="totalDays">0 يوم</span>
                    </div>
                    <div class="summary-item total">
                        <span>إجمالي الراتب:</span>
                        <span id="totalBasicSalary">0.00 جنيه</span>
                    </div>
                </div>

                <!-- أزرار بسيطة -->
                <div class="simple-buttons">
                    <button onclick="refreshCalculations()">إعادة حساب</button>
                    <button onclick="saveSalaryCalculation()">حفظ</button>
                    <button onclick="printSalaryReport()">طباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- إضافة نافذة منبثقة لعرض الجدول -->
    <div id="tableModal" class="modal">
        <div class="modal-content table-modal-content">
            <div class="modal-header">
                <h2>قائمة الموظفين</h2>
                <div class="table-actions">
                    <button class="print-btn" onclick="printTable()"><i class="fas fa-print"></i> طباعة</button>
                    <button class="export-btn" onclick="exportToExcel()"><i class="fas fa-file-excel"></i> تصدير إلى إكسل</button>
                    <span class="close" onclick="closeTableModal()">&times;</span>
                </div>
            </div>
            <div class="table-container">
                <table id="employeesTable">
                    <thead>
                        <tr>
                            <th class="text-center">الكود</th>
                            <th class="text-center">الاسم</th>
                            <th class="text-center">الوظيفة</th>
                            <th class="text-center">نوع التوظيف</th>
                            <th class="text-center">الرقم القومي</th>
                            <th class="text-center">رقم الهاتف</th>
                            <th class="text-center">الراتب الأساسي</th>
                            <th class="text-center">إجمالي الراتب</th>
                        </tr>
                    </thead>
                    <tbody id="employeesTableBody">
                        <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- إضافة نافذة منبثقة للمشروعات -->
    <div id="projectsModal" class="modal">
        <div class="modal-content" id="projectsModalContent" style="width: 80%; max-width: 1000px; margin: 3% auto;">
            <div class="modal-header">
                <h2 id="projectsModalTitle">إدارة المشروعات</h2>
                <span class="close" onclick="closeProjectsModal()">&times;</span>
            </div>

            <!-- قسم إضافة/تعديل مشروع -->
            <form id="projectForm" style="margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
                <h3 style="margin-top: 0; color: #495057; border-bottom: 1px solid #dee2e6; padding-bottom: 10px;">إضافة مشروع جديد</h3>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div class="form-group">
                        <label for="projectName">اسم المشروع</label>
                        <input type="text" id="projectName" required placeholder="أدخل اسم المشروع">
                    </div>
                    <div class="form-group">
                        <label for="projectPO">PO</label>
                        <input type="text" id="projectPO" placeholder="أدخل رقم أمر الشراء (PO)">
                    </div>
                </div>

                <div class="form-group">
                    <label for="projectDescription">وصف المشروع</label>
                    <textarea id="projectDescription" rows="2" placeholder="أدخل وصف المشروع"></textarea>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 15px;">
                    <button type="button" onclick="saveProject()" style="
                        background: #28a745;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">
                        <i class="fas fa-save"></i> حفظ المشروع
                    </button>
                    <button type="button" onclick="resetProjectForm()" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>

            <!-- قسم عرض المشروعات -->
            <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #495057;">قائمة المشروعات</h3>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="exportProjectsToExcel()" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                        <button onclick="printProjects()" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>
                <div class="projects-list" id="projectsList">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- تم حذف نافذة حساب أيام العمل -->

    <!-- تم حذف جدول السجلات المدمج -->

    <!-- تضمين ملف JavaScript في نهاية الصفحة -->
    <script src="app.js"></script>
</body>
</html>
