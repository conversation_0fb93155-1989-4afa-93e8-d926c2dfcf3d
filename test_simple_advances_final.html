<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ نافذة السلف البسيطة النهائية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .success-banner {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }
        .feature-item {
            background: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 2em;
            color: #28a745;
            margin-bottom: 10px;
        }
        .feature-title {
            color: #495057;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .feature-desc {
            color: #6c757d;
            font-size: 0.9em;
        }
        .big-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        .big-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .big-button.primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
        }
        .specs-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .specs-table th,
        .specs-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .specs-table th {
            background: #28a745;
            color: white;
            font-weight: bold;
        }
        .specs-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .result {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .highlight-box {
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .highlight-box h3 {
            color: #1976d2;
            margin: 0 0 10px 0;
        }
        .highlight-box p {
            color: #424242;
            margin: 0;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> نافذة السلف البسيطة</h1>
            <p style="font-size: 1.2em; color: #666;">تم إنشاؤها بنجاح - بسيطة وسهلة مثل نافذة أيام الحضور</p>
        </div>

        <div class="success-banner">
            <i class="fas fa-trophy"></i>
            تم إنشاء نافذة السلف البسيطة بنجاح! حجم صغير 450px وتصميم مدمج
        </div>

        <div class="highlight-box">
            <h3><i class="fas fa-star"></i> المواصفات النهائية</h3>
            <p>نافذة بسيطة وصغيرة بعرض 450px مع تصميم مدمج وسهل الاستخدام</p>
        </div>

        <table class="specs-table">
            <thead>
                <tr>
                    <th>الخاصية</th>
                    <th>القيمة</th>
                    <th>الوصف</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>العرض</strong></td>
                    <td>450px</td>
                    <td>حجم صغير ومناسب</td>
                </tr>
                <tr>
                    <td><strong>الارتفاع</strong></td>
                    <td>60vh</td>
                    <td>ارتفاع متناسب مع الشاشة</td>
                </tr>
                <tr>
                    <td><strong>التصميم</strong></td>
                    <td>بسيط ومدمج</td>
                    <td>مثل نافذة أيام الحضور</td>
                </tr>
                <tr>
                    <td><strong>الحقول</strong></td>
                    <td>صفين متجاورين</td>
                    <td>توفير المساحة</td>
                </tr>
                <tr>
                    <td><strong>الجدول</strong></td>
                    <td>خط صغير 11px</td>
                    <td>عرض أكثر للبيانات</td>
                </tr>
                <tr>
                    <td><strong>الأزرار</strong></td>
                    <td>أيقونات صغيرة</td>
                    <td>توفير المساحة</td>
                </tr>
            </tbody>
        </table>

        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">📏</div>
                <div class="feature-title">حجم صغير</div>
                <div class="feature-desc">450px عرض بدلاً من 800px</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">تصميم بسيط</div>
                <div class="feature-desc">مثل نافذة أيام الحضور تماماً</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📋</div>
                <div class="feature-title">حقول مدمجة</div>
                <div class="feature-desc">صفين متجاورين لتوفير المساحة</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">سهولة الاستخدام</div>
                <div class="feature-desc">واجهة بسيطة وسريعة</div>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="big-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
            </button>
            
            <button class="big-button primary" onclick="openSimpleAdvancesWindow()">
                <i class="fas fa-window-restore"></i> فتح النافذة البسيطة
            </button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'exclamation-triangle'}"></i> ${message}`;
        }

        function createTestEmployee() {
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 99999,
                    name: 'محمد أحمد - النافذة البسيطة',
                    position: 'محاسب',
                    employeeCode: 'SIMPLE99999',
                    employmentType: 'monthly',
                    basicSalary: 4500
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 99999);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء بعض سجلات السلف التجريبية
                const testAdvances = [
                    {
                        id: Date.now() + 1,
                        employeeId: 99999,
                        type: 'advance',
                        month: 12,
                        year: 2024,
                        amount: 800,
                        description: 'سلفة ديسمبر',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 99999,
                        type: 'deduction',
                        month: 11,
                        year: 2024,
                        amount: 150,
                        description: 'خصم تأخير',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                if (typeof advancesDeductionsData !== 'undefined') {
                    advancesDeductionsData = advancesDeductionsData.filter(record => record.employeeId !== 99999);
                    testAdvances.forEach(record => {
                        advancesDeductionsData.push(record);
                    });
                } else {
                    window.advancesDeductionsData = testAdvances;
                }
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                    saveAdvancesDeductionsToLocalStorage();
                }
                
                showResult(`✅ تم إنشاء الموظف "${testEmployee.name}" مع ${testAdvances.length} سجل سلف/خصم`, 'success');
                
            } catch (error) {
                showResult('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openSimpleAdvancesWindow() {
            try {
                if (!employees.find(emp => emp.id === 99999)) {
                    showResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // فتح النافذة البسيطة الجديدة
                viewAdvancesDeductions(99999);
                
                showResult('🎉 تم فتح نافذة السلف البسيطة! لاحظ الحجم الصغير 450px والتصميم المدمج', 'success');
                
                // فحص النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        const modalContent = modal.querySelector('.modal-content');
                        if (modalContent) {
                            const width = modalContent.style.width;
                            showResult(`✅ النافذة مفتوحة بعرض ${width} - تصميم بسيط ومدمج!`, 'success');
                        }
                    }
                }, 1000);
                
            } catch (error) {
                showResult('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewAdvancesDeductions === 'function') {
                    showResult('✅ النظام جاهز! النافذة البسيطة جاهزة للاستخدام', 'success');
                } else {
                    showResult('❌ خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
