<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة زر الحفظ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .diagnostic-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .diagnostic-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .diagnostic-item:last-child {
            border-bottom: none;
        }
        
        .diagnostic-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .diagnostic-icon.pass {
            background: #28a745;
        }
        
        .diagnostic-icon.fail {
            background: #dc3545;
        }
        
        .diagnostic-icon.pending {
            background: #6c757d;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e3f2fd;
        }
        
        .step-number {
            background: #1976d2;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص مشكلة زر الحفظ</h1>
            <p>فحص شامل لمشكلة عدم عمل زر "حفظ جميع التغييرات"</p>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-exclamation-triangle"></i> المشكلة المبلغ عنها:</h4>
            <p><strong>زر "حفظ جميع التغييرات" لا يقوم بحفظ التغييرات بعد التعديل</strong></p>
            
            <h4><i class="fas fa-list-ol"></i> خطوات التشخيص:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء بيانات تجريبية</strong> - إنشاء موظف وسجلات للاختبار
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة السجلات</strong> - فتح نافذة سجلات الأيام
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>تعديل البيانات</strong> - تعديل حقل في الجدول
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>اختبار زر الحفظ</strong> - الضغط على زر "حفظ جميع التغييرات"
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>التحقق من النتيجة</strong> - فحص حفظ البيانات في localStorage
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button danger" onclick="runSaveButtonDiagnostic()">
                <i class="fas fa-play"></i> تشغيل التشخيص الشامل
            </button>
            
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button warning" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>
            
            <button class="test-button success" onclick="testSaveButtonDirectly()">
                <i class="fas fa-save"></i> اختبار زر الحفظ مباشرة
            </button>
        </div>

        <div class="diagnostic-list">
            <h4><i class="fas fa-clipboard-check"></i> قائمة التشخيص:</h4>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check1">1</div>
                <div>
                    <strong>دالة saveAllRecordsChanges موجودة</strong>
                    <br><small>الدالة المسؤولة عن حفظ التغييرات</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check2">2</div>
                <div>
                    <strong>زر الحفظ موجود في النافذة</strong>
                    <br><small>العنصر saveChangesBtn في DOM</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check3">3</div>
                <div>
                    <strong>زر الحفظ قابل للنقر</strong>
                    <br><small>ليس معطلاً أو مخفياً</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check4">4</div>
                <div>
                    <strong>يوجد تغييرات للحفظ</strong>
                    <br><small>متغير recordsChanges يحتوي على تغييرات</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check5">5</div>
                <div>
                    <strong>دالة الحفظ تعمل بشكل صحيح</strong>
                    <br><small>saveDaysDataToLocalStorage تحفظ البيانات</small>
                </div>
            </div>
            
            <div class="diagnostic-item">
                <div class="diagnostic-icon pending" id="check6">6</div>
                <div>
                    <strong>البيانات محفوظة فعلياً</strong>
                    <br><small>التغييرات تظهر في localStorage</small>
                </div>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء تشخيص مشكلة زر الحفظ...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [SAVE_BUTTON_DEBUG] مراقب زر الحفظ جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function updateDiagnosticItem(itemNumber, status) {
            const icon = document.getElementById(`check${itemNumber}`);
            if (icon) {
                if (status === 'pass') {
                    icon.className = 'diagnostic-icon pass';
                    icon.innerHTML = '✓';
                } else if (status === 'fail') {
                    icon.className = 'diagnostic-icon fail';
                    icon.innerHTML = '✗';
                } else {
                    icon.className = 'diagnostic-icon pending';
                    icon.innerHTML = itemNumber;
                }
            }
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');
            addLog('🔧 إنشاء بيانات تجريبية...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 666)) {
                    employees.push({
                        id: 666,
                        name: 'فاطمة أحمد - اختبار زر الحفظ',
                        position: 'محاسبة',
                        employeeCode: 'SAVE666',
                        employmentType: 'monthly',
                        basicSalary: 5500
                    });
                    addLog('✓ تم إنشاء موظف تجريبي');
                }
                
                // إنشاء سجلات أيام تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1000,
                        employeeId: 666,
                        projectName: 'مشروع اختبار زر الحفظ',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 1,
                        overtimeHours: 5,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2000,
                        employeeId: 666,
                        projectName: 'مشروع التطوير الثاني',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 0,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });
                
                // حفظ البيانات
                saveDaysDataToLocalStorage();
                addLog('✓ تم حفظ البيانات التجريبية');
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح! (موظف واحد + سجلين)', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 666)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة السجلات...');
                viewDaysRecords(666);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        addLog('✓ تم فتح نافذة السجلات بنجاح');
                        updateStatus('تم فتح نافذة السجلات! الآن يمكنك اختبار التعديل والحفظ', 'success');
                        
                        // فحص وجود زر الحفظ
                        const saveBtn = modal.querySelector('#saveChangesBtn');
                        if (saveBtn) {
                            addLog('✓ زر الحفظ موجود في النافذة');
                        } else {
                            addLog('✗ زر الحفظ غير موجود في النافذة');
                        }
                    } else {
                        addLog('✗ فشل في فتح نافذة السجلات');
                        updateStatus('فشل في فتح نافذة السجلات', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
        }

        function testSaveButtonDirectly() {
            addLog('🔧 اختبار زر الحفظ مباشرة...');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                addLog('✗ نافذة السجلات غير مفتوحة');
                updateStatus('يرجى فتح نافذة السجلات أولاً', 'error');
                return;
            }
            
            // محاكاة تعديل
            addLog('🔄 محاكاة تعديل في البيانات...');
            if (employeeDaysData.length > 0) {
                const testRecord = employeeDaysData.find(r => r.employeeId === 666);
                if (testRecord) {
                    // إضافة تغيير وهمي
                    if (!recordsChanges[testRecord.id]) {
                        recordsChanges[testRecord.id] = {};
                    }
                    recordsChanges[testRecord.id]['projectName'] = {
                        oldValue: testRecord.projectName,
                        newValue: testRecord.projectName + ' - معدل'
                    };
                    
                    addLog('✓ تم إضافة تغيير وهمي');
                    addLog(`عدد التغييرات: ${Object.keys(recordsChanges).length}`);
                    
                    // اختبار زر الحفظ
                    try {
                        addLog('🔄 اختبار استدعاء دالة الحفظ...');
                        
                        // تجاوز تأكيد الحفظ
                        const originalConfirm = window.confirm;
                        window.confirm = () => {
                            addLog('✓ تم تأكيد الحفظ');
                            return true;
                        };
                        
                        saveAllRecordsChanges(666);
                        addLog('✓ تم استدعاء دالة الحفظ بنجاح');
                        
                        // استعادة confirm الأصلي
                        window.confirm = originalConfirm;
                        
                        updateStatus('تم اختبار زر الحفظ بنجاح!', 'success');
                        
                    } catch (error) {
                        addLog('✗ خطأ في استدعاء دالة الحفظ: ' + error.message);
                        updateStatus('خطأ في اختبار زر الحفظ: ' + error.message, 'error');
                    }
                } else {
                    addLog('✗ لم يتم العثور على سجل للموظف التجريبي');
                }
            } else {
                addLog('✗ لا توجد بيانات أيام للاختبار');
            }
        }

        function runSaveButtonDiagnostic() {
            updateStatus('🔍 بدء التشخيص الشامل لزر الحفظ...', 'warning');
            addLog('=== بدء التشخيص الشامل لزر الحفظ ===');
            
            // إعادة تعيين جميع الفحوصات
            for (let i = 1; i <= 6; i++) {
                updateDiagnosticItem(i, 'pending');
            }
            
            // فحص 1: وجود دالة saveAllRecordsChanges
            setTimeout(() => {
                if (typeof saveAllRecordsChanges === 'function') {
                    updateDiagnosticItem(1, 'pass');
                    addLog('✓ دالة saveAllRecordsChanges موجودة');
                } else {
                    updateDiagnosticItem(1, 'fail');
                    addLog('✗ دالة saveAllRecordsChanges مفقودة');
                }
            }, 500);
            
            // فحص 2: إنشاء البيانات وفتح النافذة
            setTimeout(() => {
                createTestData();
                setTimeout(() => {
                    openRecordsWindow();
                    
                    setTimeout(() => {
                        const modal = document.getElementById('daysRecordsModal');
                        const saveBtn = modal ? modal.querySelector('#saveChangesBtn') : null;
                        
                        if (saveBtn) {
                            updateDiagnosticItem(2, 'pass');
                            addLog('✓ زر الحفظ موجود في النافذة');
                        } else {
                            updateDiagnosticItem(2, 'fail');
                            addLog('✗ زر الحفظ غير موجود في النافذة');
                        }
                    }, 1000);
                }, 1000);
            }, 1000);
            
            // فحص 3: حالة زر الحفظ
            setTimeout(() => {
                const modal = document.getElementById('daysRecordsModal');
                const saveBtn = modal ? modal.querySelector('#saveChangesBtn') : null;
                
                if (saveBtn) {
                    const isDisabled = saveBtn.style.pointerEvents === 'none' || saveBtn.disabled;
                    if (!isDisabled) {
                        updateDiagnosticItem(3, 'pass');
                        addLog('✓ زر الحفظ قابل للنقر');
                    } else {
                        updateDiagnosticItem(3, 'fail');
                        addLog('✗ زر الحفظ معطل أو غير قابل للنقر');
                        addLog(`حالة الزر: pointerEvents=${saveBtn.style.pointerEvents}, disabled=${saveBtn.disabled}`);
                    }
                } else {
                    updateDiagnosticItem(3, 'fail');
                    addLog('✗ لا يمكن فحص حالة الزر - الزر غير موجود');
                }
            }, 4000);
            
            // فحص 4: إضافة تغييرات واختبار الحفظ
            setTimeout(() => {
                testSaveButtonDirectly();
                
                setTimeout(() => {
                    const changesCount = Object.keys(recordsChanges).length;
                    if (changesCount > 0) {
                        updateDiagnosticItem(4, 'pass');
                        addLog(`✓ يوجد ${changesCount} تغيير للحفظ`);
                    } else {
                        updateDiagnosticItem(4, 'fail');
                        addLog('✗ لا توجد تغييرات للحفظ');
                    }
                }, 500);
            }, 5000);
            
            // فحص 5: اختبار دالة الحفظ
            setTimeout(() => {
                try {
                    const beforeSave = JSON.stringify(employeeDaysData);
                    saveDaysDataToLocalStorage();
                    const afterSave = localStorage.getItem('oscoEmployeeDaysData');
                    
                    if (afterSave && JSON.parse(afterSave).length > 0) {
                        updateDiagnosticItem(5, 'pass');
                        addLog('✓ دالة الحفظ تعمل بشكل صحيح');
                    } else {
                        updateDiagnosticItem(5, 'fail');
                        addLog('✗ دالة الحفظ لا تعمل بشكل صحيح');
                    }
                } catch (error) {
                    updateDiagnosticItem(5, 'fail');
                    addLog('✗ خطأ في اختبار دالة الحفظ: ' + error.message);
                }
            }, 6000);
            
            // فحص 6: التحقق النهائي من البيانات
            setTimeout(() => {
                try {
                    const savedData = localStorage.getItem('oscoEmployeeDaysData');
                    if (savedData) {
                        const parsedData = JSON.parse(savedData);
                        const testEmployee = parsedData.filter(r => r.employeeId === 666);
                        
                        if (testEmployee.length > 0) {
                            updateDiagnosticItem(6, 'pass');
                            addLog(`✓ البيانات محفوظة بنجاح (${testEmployee.length} سجل للموظف التجريبي)`);
                        } else {
                            updateDiagnosticItem(6, 'fail');
                            addLog('✗ البيانات غير محفوظة بشكل صحيح');
                        }
                    } else {
                        updateDiagnosticItem(6, 'fail');
                        addLog('✗ لا توجد بيانات محفوظة في localStorage');
                    }
                } catch (error) {
                    updateDiagnosticItem(6, 'fail');
                    addLog('✗ خطأ في فحص البيانات المحفوظة: ' + error.message);
                }
            }, 7000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء التشخيص الشامل ===');
                
                let passedTests = 0;
                for (let i = 1; i <= 6; i++) {
                    const icon = document.getElementById(`check${i}`);
                    if (icon && icon.classList.contains('pass')) {
                        passedTests++;
                    }
                }
                
                const percentage = (passedTests / 6) * 100;
                addLog(`📊 النتيجة النهائية: ${passedTests}/6 اختبار نجح (${percentage.toFixed(0)}%)`);
                
                if (percentage >= 100) {
                    updateStatus('🎉 جميع الاختبارات نجحت! زر الحفظ يعمل بشكل مثالي', 'success');
                } else if (percentage >= 80) {
                    updateStatus(`⚠️ معظم الاختبارات نجحت (${percentage.toFixed(0)}%) - قد تكون هناك مشكلة بسيطة`, 'warning');
                } else {
                    updateStatus(`❌ فشل في ${6 - passedTests} اختبار - هناك مشكلة في زر الحفظ`, 'error');
                }
            }, 8000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة تشخيص زر الحفظ - جاهز للبدء!', 'info');
            addLog('تم تحميل مشخص زر الحفظ');
        });
    </script>
</body>
</html>
