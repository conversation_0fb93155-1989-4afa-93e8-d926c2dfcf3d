<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الموظف الجديد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .test-button {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .result-item.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .result-item.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .result-item.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-user-plus"></i> اختبار نظام الموظف الجديد</h1>
            <p>اختبار شامل للنظام الجديد البسيط لإدارة الموظفين</p>
        </div>

        <div class="content">
            <div class="test-grid">
                <div class="test-card">
                    <h3><i class="fas fa-cogs"></i> اختبارات النظام الأساسي</h3>
                    <button class="test-button" onclick="testSystemLoad()">
                        <i class="fas fa-power-off"></i> اختبار تحميل النظام
                    </button>
                    <button class="test-button" onclick="testFunctions()">
                        <i class="fas fa-code"></i> اختبار الدوال
                    </button>
                    <button class="test-button" onclick="testModalElements()">
                        <i class="fas fa-window-maximize"></i> اختبار عناصر النوافذ
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-user-plus"></i> اختبارات إضافة الموظف</h3>
                    <button class="test-button" onclick="testOpenNewEmployeeModal()">
                        <i class="fas fa-external-link-alt"></i> فتح نافذة موظف جديد
                    </button>
                    <button class="test-button" onclick="testCreateEmployee()">
                        <i class="fas fa-user-check"></i> إنشاء موظف تجريبي
                    </button>
                    <button class="test-button" onclick="testValidation()">
                        <i class="fas fa-shield-alt"></i> اختبار التحقق من البيانات
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-table"></i> اختبارات سجل الموظفين</h3>
                    <button class="test-button" onclick="testOpenRecordsModal()">
                        <i class="fas fa-list"></i> فتح نافذة السجل
                    </button>
                    <button class="test-button" onclick="testTableFunctions()">
                        <i class="fas fa-edit"></i> اختبار التعديل المباشر
                    </button>
                    <button class="test-button" onclick="testColumnToggle()">
                        <i class="fas fa-columns"></i> اختبار إخفاء الأعمدة
                    </button>
                    <button class="test-button" onclick="testScrollbars()">
                        <i class="fas fa-arrows-alt-v"></i> اختبار شريط التمرير
                    </button>
                    <button class="test-button" onclick="testDesignElements()">
                        <i class="fas fa-palette"></i> اختبار التصميم
                    </button>
                    <button class="test-button" onclick="testEditButton()">
                        <i class="fas fa-edit"></i> اختبار زر التعديل
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-tools"></i> اختبارات متقدمة</h3>
                    <button class="test-button" onclick="testExportFunction()">
                        <i class="fas fa-file-excel"></i> اختبار التصدير
                    </button>
                    <button class="test-button" onclick="testDeleteFunction()">
                        <i class="fas fa-trash"></i> اختبار الحذف
                    </button>
                    <button class="test-button" onclick="runFullTest()">
                        <i class="fas fa-play"></i> اختبار شامل
                    </button>
                </div>
            </div>

            <div class="test-grid">
                <div class="test-card">
                    <h3><i class="fas fa-broom"></i> أدوات التنظيف</h3>
                    <button class="test-button warning" onclick="clearResults()">
                        <i class="fas fa-eraser"></i> مسح النتائج
                    </button>
                    <button class="test-button danger" onclick="clearTestData()">
                        <i class="fas fa-trash-alt"></i> مسح البيانات التجريبية
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-external-link-alt"></i> روابط سريعة</h3>
                    <button class="test-button success" onclick="openMainApp()">
                        <i class="fas fa-home"></i> فتح التطبيق الرئيسي
                    </button>
                    <button class="test-button info" onclick="showSystemInfo()">
                        <i class="fas fa-info-circle"></i> معلومات النظام
                    </button>
                </div>
            </div>

            <div class="results" id="results">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="resultsList">
                    <p>جاهز لبدء الاختبارات...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            testResults.push({ message, type, timestamp });

            const resultsList = document.getElementById('resultsList');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `
                <span class="status-indicator status-${type}"></span>
                <strong>[${timestamp}]</strong> ${message}
            `;

            resultsList.appendChild(resultItem);
            resultsList.scrollTop = resultsList.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            document.getElementById('resultsList').innerHTML = '<p>تم مسح النتائج...</p>';
            addResult('تم مسح جميع النتائج', 'warning');
        }

        function testSystemLoad() {
            addResult('🔍 بدء اختبار تحميل النظام...', 'info');

            // اختبار المتغيرات العامة
            if (typeof employees !== 'undefined') {
                addResult('✅ متغير employees محمل بنجاح', 'success');
                addResult(`📊 عدد الموظفين الحالي: ${employees.length}`, 'info');
            } else {
                addResult('❌ متغير employees غير محمل', 'error');
            }

            // اختبار مكتبة XLSX
            if (typeof XLSX !== 'undefined') {
                addResult('✅ مكتبة XLSX محملة بنجاح', 'success');
            } else {
                addResult('❌ مكتبة XLSX غير محملة', 'error');
            }

            addResult('✅ اكتمل اختبار تحميل النظام', 'success');
        }

        function testFunctions() {
            addResult('🔍 بدء اختبار الدوال...', 'info');

            const requiredFunctions = [
                'openNewEmployeeModal',
                'closeNewEmployeeModal',
                'saveNewEmployee',
                'openEmployeeRecordsModal',
                'closeEmployeeRecordsModal',
                'loadEmployeeRecordsTable',
                'updateEmployeeField',
                'deleteEmployee',
                'saveAllEmployeeChanges',
                'toggleColumn',
                'exportEmployeesToExcel'
            ];

            let functionsFound = 0;

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            const percentage = Math.round((functionsFound / requiredFunctions.length) * 100);
            addResult(`📊 نسبة الدوال المتاحة: ${percentage}%`, percentage >= 80 ? 'success' : 'warning');
        }

        function testModalElements() {
            addResult('🔍 بدء اختبار عناصر النوافذ...', 'info');

            // اختبار نافذة الموظف الجديد
            const newEmployeeModal = document.getElementById('newEmployeeModal');
            if (newEmployeeModal) {
                addResult('✅ نافذة الموظف الجديد موجودة', 'success');

                // اختبار شريط التمرير
                const modalContent = newEmployeeModal.querySelector('.modal-content');
                if (modalContent) {
                    const hasScrollbar = modalContent.style.maxHeight && modalContent.style.overflowY;
                    if (hasScrollbar || modalContent.style.overflow) {
                        addResult('✅ شريط التمرير مفعل في نافذة الموظف', 'success');
                    } else {
                        addResult('⚠️ شريط التمرير غير مفعل في نافذة الموظف', 'warning');
                    }
                }

                // اختبار الحقول
                const fields = [
                    'newEmployeeCode',
                    'newEmployeeName',
                    'newEmployeePosition',
                    'newEmployeeNationalId',
                    'newEmployeePhone',
                    'newEmployeeAddress',
                    'newEmployeePhoto',
                    'newEmployeeJoinDate'
                ];

                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        addResult(`✅ حقل ${fieldId}: موجود`, 'success');
                    } else {
                        addResult(`❌ حقل ${fieldId}: مفقود`, 'error');
                    }
                });
            } else {
                addResult('❌ نافذة الموظف الجديد غير موجودة', 'error');
            }

            // اختبار نافذة السجل
            const recordsModal = document.getElementById('employeeRecordsModal');
            if (recordsModal) {
                addResult('✅ نافذة سجل الموظفين موجودة', 'success');

                // اختبار شريط التمرير في الجدول
                const tableContainer = recordsModal.querySelector('.employee-table-container');
                if (tableContainer) {
                    addResult('✅ حاوية الجدول مع شريط التمرير موجودة', 'success');
                } else {
                    addResult('⚠️ حاوية الجدول بدون شريط تمرير', 'warning');
                }
            } else {
                addResult('❌ نافذة سجل الموظفين غير موجودة', 'error');
            }
        }

        function testOpenNewEmployeeModal() {
            addResult('🔍 اختبار فتح نافذة موظف جديد...', 'info');

            try {
                if (typeof openNewEmployeeModal === 'function') {
                    openNewEmployeeModal();

                    setTimeout(() => {
                        const modal = document.getElementById('newEmployeeModal');
                        if (modal && modal.style.display === 'block') {
                            addResult('✅ تم فتح نافذة الموظف الجديد بنجاح', 'success');

                            // إغلاق النافذة
                            closeNewEmployeeModal();
                            addResult('✅ تم إغلاق النافذة بنجاح', 'success');
                        } else {
                            addResult('❌ فشل في فتح النافذة', 'error');
                        }
                    }, 500);
                } else {
                    addResult('❌ دالة فتح النافذة غير موجودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        function testCreateEmployee() {
            addResult('🔍 اختبار إنشاء موظف تجريبي...', 'info');

            try {
                const testEmployee = {
                    id: Date.now(),
                    employeeCode: 'TEST001',
                    name: 'موظف تجريبي للاختبار',
                    position: 'مختبر النظام',
                    nationalId: '12345678901234',
                    phone: '01000000000',
                    address: 'عنوان تجريبي',
                    joinDate: new Date().toISOString().split('T')[0],
                    photo: 'https://via.placeholder.com/150/007bff/ffffff?text=TEST',
                    employmentType: 'monthly',
                    basicSalary: 0,
                    totalSalary: 0,
                    createdAt: new Date().toISOString()
                };

                // التحقق من عدم وجود موظف بنفس الكود
                const existingEmployee = employees.find(emp => emp.employeeCode === 'TEST001');
                if (existingEmployee) {
                    addResult('⚠️ موظف تجريبي موجود بالفعل', 'warning');
                    return;
                }

                employees.push(testEmployee);

                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }

                if (typeof populateEmployees === 'function') {
                    populateEmployees();
                }

                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                addResult(`📝 الكود: ${testEmployee.employeeCode}`, 'info');
                addResult(`👤 الاسم: ${testEmployee.name}`, 'info');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
            }
        }

        function testValidation() {
            addResult('🔍 اختبار التحقق من البيانات...', 'info');

            // محاولة إضافة موظف بكود مكرر
            const duplicateCodeTest = employees.find(emp => emp.employeeCode === 'TEST001');
            if (duplicateCodeTest) {
                addResult('✅ النظام يمنع تكرار الأكواد بشكل صحيح', 'success');
            } else {
                addResult('⚠️ لا يوجد موظف تجريبي لاختبار تكرار الكود', 'warning');
            }

            addResult('✅ اكتمل اختبار التحقق من البيانات', 'success');
        }

        function testOpenRecordsModal() {
            addResult('🔍 اختبار فتح نافذة السجل...', 'info');

            try {
                if (typeof openEmployeeRecordsModal === 'function') {
                    openEmployeeRecordsModal();

                    setTimeout(() => {
                        const modal = document.getElementById('employeeRecordsModal');
                        if (modal && modal.style.display === 'block') {
                            addResult('✅ تم فتح نافذة السجل بنجاح', 'success');

                            // التحقق من تحميل الجدول
                            const tableBody = document.getElementById('employeeRecordsTableBody');
                            if (tableBody && tableBody.children.length > 0) {
                                addResult(`✅ تم تحميل ${tableBody.children.length} صف في الجدول`, 'success');
                            } else {
                                addResult('⚠️ الجدول فارغ أو لم يتم تحميله', 'warning');
                            }

                            // إغلاق النافذة
                            closeEmployeeRecordsModal();
                            addResult('✅ تم إغلاق نافذة السجل بنجاح', 'success');
                        } else {
                            addResult('❌ فشل في فتح نافذة السجل', 'error');
                        }
                    }, 500);
                } else {
                    addResult('❌ دالة فتح نافذة السجل غير موجودة', 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة السجل: ${error.message}`, 'error');
            }
        }

        function testTableFunctions() {
            addResult('🔍 اختبار وظائف الجدول...', 'info');

            if (typeof updateEmployeeField === 'function') {
                addResult('✅ دالة تحديث الحقول متاحة', 'success');
            } else {
                addResult('❌ دالة تحديث الحقول غير متاحة', 'error');
            }

            if (typeof loadEmployeeRecordsTable === 'function') {
                addResult('✅ دالة تحميل الجدول متاحة', 'success');
            } else {
                addResult('❌ دالة تحميل الجدول غير متاحة', 'error');
            }
        }

        function testColumnToggle() {
            addResult('🔍 اختبار إخفاء/إظهار الأعمدة...', 'info');

            if (typeof toggleColumn === 'function') {
                addResult('✅ دالة تبديل الأعمدة متاحة', 'success');

                // اختبار تبديل عمود الاسم
                try {
                    toggleColumn('name');
                    addResult('✅ تم اختبار تبديل عمود الاسم', 'success');
                } catch (error) {
                    addResult(`❌ خطأ في تبديل العمود: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ دالة تبديل الأعمدة غير متاحة', 'error');
            }
        }

        function testScrollbars() {
            addResult('🔍 اختبار شريط التمرير...', 'info');

            // اختبار نافذة الموظف الجديد
            const newEmployeeModal = document.getElementById('newEmployeeModal');
            if (newEmployeeModal) {
                const modalContent = newEmployeeModal.querySelector('.modal-content');
                if (modalContent) {
                    const styles = window.getComputedStyle(modalContent);
                    const hasMaxHeight = modalContent.style.maxHeight || styles.maxHeight !== 'none';
                    const hasOverflow = modalContent.style.overflow === 'auto' || modalContent.style.overflowY === 'auto';

                    if (hasMaxHeight) {
                        addResult('✅ نافذة الموظف: ارتفاع محدود معين', 'success');
                    } else {
                        addResult('⚠️ نافذة الموظف: لا يوجد ارتفاع محدود', 'warning');
                    }

                    // فحص العنصر القابل للتمرير
                    const scrollableDiv = modalContent.querySelector('div[style*="overflow-y: auto"]');
                    if (scrollableDiv) {
                        addResult('✅ عنصر قابل للتمرير موجود في نافذة الموظف', 'success');
                    } else {
                        addResult('⚠️ لا يوجد عنصر قابل للتمرير في نافذة الموظف', 'warning');
                    }
                }
            }

            // اختبار نافذة سجل الموظفين
            const recordsModal = document.getElementById('employeeRecordsModal');
            if (recordsModal) {
                const tableContainer = recordsModal.querySelector('.employee-table-container');
                if (tableContainer) {
                    const styles = window.getComputedStyle(tableContainer);
                    const hasOverflow = tableContainer.style.overflow === 'auto' || styles.overflow === 'auto';
                    const hasMaxHeight = tableContainer.style.maxHeight || styles.maxHeight !== 'none';

                    if (hasOverflow && hasMaxHeight) {
                        addResult('✅ جدول الموظفين: شريط تمرير مفعل بشكل صحيح', 'success');
                    } else {
                        addResult('⚠️ جدول الموظفين: شريط التمرير غير مفعل بشكل صحيح', 'warning');
                    }

                    addResult(`📊 خصائص الجدول: overflow=${styles.overflow}, maxHeight=${styles.maxHeight}`, 'info');
                } else {
                    addResult('❌ حاوية الجدول غير موجودة', 'error');
                }
            }

            // فحص أنماط CSS لشريط التمرير
            const styleSheets = document.styleSheets;
            let scrollbarStylesFound = false;

            try {
                for (let i = 0; i < styleSheets.length; i++) {
                    const rules = styleSheets[i].cssRules || styleSheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        if (rules[j].selectorText && rules[j].selectorText.includes('::-webkit-scrollbar')) {
                            scrollbarStylesFound = true;
                            break;
                        }
                    }
                    if (scrollbarStylesFound) break;
                }

                if (scrollbarStylesFound) {
                    addResult('✅ أنماط CSS لشريط التمرير موجودة', 'success');
                } else {
                    addResult('⚠️ أنماط CSS لشريط التمرير غير موجودة', 'warning');
                }
            } catch (error) {
                addResult('⚠️ لا يمكن فحص أنماط CSS (قيود أمنية)', 'warning');
            }

            addResult('✅ اكتمل اختبار شريط التمرير', 'success');
        }

        function testDesignElements() {
            addResult('🎨 اختبار عناصر التصميم المحسنة...', 'info');

            // اختبار نافذة الموظف الجديد
            const newEmployeeModal = document.getElementById('newEmployeeModal');
            if (newEmployeeModal) {
                // فحص الفئة الجديدة
                const modalContent = newEmployeeModal.querySelector('.new-employee-modal-content');
                if (modalContent) {
                    addResult('✅ فئة النافذة المحسنة موجودة', 'success');
                } else {
                    addResult('❌ فئة النافذة المحسنة غير موجودة', 'error');
                }

                // فحص رأس النافذة
                const header = newEmployeeModal.querySelector('.new-employee-header');
                if (header) {
                    addResult('✅ رأس النافذة المحسن موجود', 'success');

                    // فحص الأيقونة
                    const headerIcon = header.querySelector('.header-icon');
                    if (headerIcon) {
                        addResult('✅ أيقونة الرأس موجودة', 'success');
                    } else {
                        addResult('⚠️ أيقونة الرأس مفقودة', 'warning');
                    }

                    // فحص زر الإغلاق
                    const closeBtn = header.querySelector('.close-btn');
                    if (closeBtn) {
                        addResult('✅ زر الإغلاق المحسن موجود', 'success');
                    } else {
                        addResult('⚠️ زر الإغلاق المحسن مفقود', 'warning');
                    }
                } else {
                    addResult('❌ رأس النافذة المحسن غير موجود', 'error');
                }

                // فحص جسم النافذة
                const body = newEmployeeModal.querySelector('.new-employee-body');
                if (body) {
                    addResult('✅ جسم النافذة المحسن موجود', 'success');

                    // فحص شبكة النموذج
                    const formGrid = body.querySelector('.form-grid');
                    if (formGrid) {
                        addResult('✅ شبكة النموذج موجودة', 'success');
                    } else {
                        addResult('⚠️ شبكة النموذج مفقودة', 'warning');
                    }

                    // فحص مجموعات النموذج
                    const formGroups = body.querySelectorAll('.form-group');
                    if (formGroups.length > 0) {
                        addResult(`✅ تم العثور على ${formGroups.length} مجموعة نموذج`, 'success');
                    } else {
                        addResult('❌ لم يتم العثور على مجموعات النموذج', 'error');
                    }

                    // فحص التسميات المحسنة
                    const formInputs = body.querySelectorAll('.form-input');
                    if (formInputs.length > 0) {
                        addResult(`✅ تم العثور على ${formInputs.length} حقل إدخال محسن`, 'success');
                    } else {
                        addResult('❌ لم يتم العثور على حقول الإدخال المحسنة', 'error');
                    }

                    // فحص منطقة رفع الصور
                    const photoUploadArea = body.querySelector('.photo-upload-area');
                    if (photoUploadArea) {
                        addResult('✅ منطقة رفع الصور المحسنة موجودة', 'success');
                    } else {
                        addResult('⚠️ منطقة رفع الصور المحسنة مفقودة', 'warning');
                    }

                    // فحص أزرار العمليات
                    const formActions = body.querySelector('.form-actions');
                    if (formActions) {
                        addResult('✅ منطقة أزرار العمليات موجودة', 'success');

                        const buttons = formActions.querySelectorAll('.btn');
                        if (buttons.length > 0) {
                            addResult(`✅ تم العثور على ${buttons.length} زر محسن`, 'success');
                        } else {
                            addResult('⚠️ لم يتم العثور على أزرار محسنة', 'warning');
                        }
                    } else {
                        addResult('⚠️ منطقة أزرار العمليات مفقودة', 'warning');
                    }
                } else {
                    addResult('❌ جسم النافذة المحسن غير موجود', 'error');
                }
            } else {
                addResult('❌ نافذة الموظف الجديد غير موجودة', 'error');
            }

            // فحص أنماط CSS المحسنة
            const styleSheets = document.styleSheets;
            let designStylesFound = false;

            try {
                for (let i = 0; i < styleSheets.length; i++) {
                    const rules = styleSheets[i].cssRules || styleSheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        if (rules[j].selectorText && (
                            rules[j].selectorText.includes('.new-employee-modal-content') ||
                            rules[j].selectorText.includes('.form-input') ||
                            rules[j].selectorText.includes('.btn-save')
                        )) {
                            designStylesFound = true;
                            break;
                        }
                    }
                    if (designStylesFound) break;
                }

                if (designStylesFound) {
                    addResult('✅ أنماط CSS المحسنة موجودة', 'success');
                } else {
                    addResult('⚠️ أنماط CSS المحسنة غير موجودة', 'warning');
                }
            } catch (error) {
                addResult('⚠️ لا يمكن فحص أنماط CSS (قيود أمنية)', 'warning');
            }

            addResult('✅ اكتمل اختبار عناصر التصميم', 'success');
        }

        function testEditButton() {
            addResult('🔍 اختبار زر التعديل...', 'info');

            // اختبار وجود دالة تعديل الموظف
            if (typeof editEmployee === 'function') {
                addResult('✅ دالة editEmployee متاحة', 'success');
            } else {
                addResult('❌ دالة editEmployee غير متاحة', 'error');
            }

            // اختبار دوال التعديل المساعدة
            const editFunctions = [
                'fillEmployeeForm',
                'updateEmployee',
                'resetEmployeeFormToAddMode'
            ];

            editFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            // اختبار وجود موظفين للتعديل
            if (typeof employees !== 'undefined' && employees.length > 0) {
                addResult(`✅ يوجد ${employees.length} موظف متاح للتعديل`, 'success');

                // اختبار تعديل أول موظف
                try {
                    const firstEmployee = employees[0];
                    if (firstEmployee && firstEmployee.id) {
                        addResult(`✅ سيتم اختبار تعديل الموظف: ${firstEmployee.name}`, 'info');

                        // محاولة فتح نافذة التعديل
                        if (typeof editEmployee === 'function') {
                            setTimeout(() => {
                                try {
                                    editEmployee(firstEmployee.id);
                                    addResult('✅ تم فتح نافذة التعديل بنجاح', 'success');

                                    // إغلاق النافذة بعد ثانيتين
                                    setTimeout(() => {
                                        if (typeof closeNewEmployeeModal === 'function') {
                                            closeNewEmployeeModal();
                                            addResult('✅ تم إغلاق نافذة التعديل', 'success');
                                        }
                                    }, 2000);
                                } catch (error) {
                                    addResult(`❌ خطأ في فتح نافذة التعديل: ${error.message}`, 'error');
                                }
                            }, 500);
                        }
                    } else {
                        addResult('❌ بيانات الموظف غير صحيحة', 'error');
                    }
                } catch (error) {
                    addResult(`❌ خطأ في اختبار التعديل: ${error.message}`, 'error');
                }
            } else {
                addResult('⚠️ لا يوجد موظفين لاختبار التعديل', 'warning');
                addResult('معلومة: قم بإضافة موظف تجريبي أولاً', 'info');
            }

            addResult('✅ اكتمل اختبار زر التعديل', 'success');
        }

            // اختبار دوال التعديل المساعدة
            const editFunctions = [
                'fillEmployeeForm',
                'updateEmployee',
                'resetEmployeeFormToAddMode'
            ];

            editFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            // اختبار وجود موظفين للتعديل
            if (typeof employees !== 'undefined' && employees.length > 0) {
                addResult(`✅ يوجد ${employees.length} موظف متاح للتعديل`, 'success');

                // اختبار تعديل أول موظف
                try {
                    const firstEmployee = employees[0];
                    if (firstEmployee && firstEmployee.id) {
                        addResult(`✅ سيتم اختبار تعديل الموظف: ${firstEmployee.name}`, 'info');

                        // محاولة فتح نافذة التعديل
                        if (typeof editEmployee === 'function') {
                            setTimeout(() => {
                                try {
                                    editEmployee(firstEmployee.id);
                                    addResult('✅ تم فتح نافذة التعديل بنجاح', 'success');

                                    // إغلاق النافذة بعد ثانيتين
                                    setTimeout(() => {
                                        if (typeof closeNewEmployeeModal === 'function') {
                                            closeNewEmployeeModal();
                                            addResult('✅ تم إغلاق نافذة التعديل', 'success');
                                        }
                                    }, 2000);
                                } catch (error) {
                                    addResult(`❌ خطأ في فتح نافذة التعديل: ${error.message}`, 'error');
                                }
                            }, 500);
                        }
                    } else {
                        addResult('❌ بيانات الموظف غير صحيحة', 'error');
                    }
                } catch (error) {
                    addResult(`❌ خطأ في اختبار التعديل: ${error.message}`, 'error');
                }
            } else {
                addResult('⚠️ لا يوجد موظفين لاختبار التعديل', 'warning');
                addResult('معلومة: قم بإضافة موظف تجريبي أولاً', 'info');
            }

            addResult('✅ اكتمل اختبار زر التعديل', 'success');
        }

        function testExportFunction() {
            addResult('🔍 اختبار دالة التصدير...', 'info');

            if (typeof exportEmployeesToExcel === 'function') {
                addResult('✅ دالة التصدير متاحة', 'success');

                if (typeof XLSX !== 'undefined') {
                    addResult('✅ مكتبة XLSX متاحة للتصدير', 'success');
                } else {
                    addResult('❌ مكتبة XLSX غير متاحة', 'error');
                }
            } else {
                addResult('❌ دالة التصدير غير متاحة', 'error');
            }
        }

        function testDeleteFunction() {
            addResult('🔍 اختبار دالة الحذف...', 'info');

            if (typeof deleteEmployee === 'function') {
                addResult('✅ دالة الحذف متاحة', 'success');
            } else {
                addResult('❌ دالة الحذف غير متاحة', 'error');
            }
        }

        function runFullTest() {
            addResult('🚀 بدء الاختبار الشامل...', 'info');

            clearResults();

            setTimeout(() => testSystemLoad(), 500);
            setTimeout(() => testFunctions(), 1000);
            setTimeout(() => testModalElements(), 1500);
            setTimeout(() => testOpenNewEmployeeModal(), 2000);
            setTimeout(() => testCreateEmployee(), 3000);
            setTimeout(() => testValidation(), 3500);
            setTimeout(() => testOpenRecordsModal(), 4000);
            setTimeout(() => testTableFunctions(), 4500);
            setTimeout(() => testColumnToggle(), 5000);
            setTimeout(() => testScrollbars(), 5500);
            setTimeout(() => testDesignElements(), 6000);
            setTimeout(() => testEditButton(), 6500);
            setTimeout(() => testExportFunction(), 7000);
            setTimeout(() => testDeleteFunction(), 7500);

            setTimeout(() => {
                addResult('🎉 اكتمل الاختبار الشامل!', 'success');

                const successCount = testResults.filter(r => r.type === 'success').length;
                const errorCount = testResults.filter(r => r.type === 'error').length;
                const warningCount = testResults.filter(r => r.type === 'warning').length;

                addResult(`📊 ملخص النتائج:`, 'info');
                addResult(`✅ نجح: ${successCount}`, 'success');
                addResult(`❌ فشل: ${errorCount}`, errorCount > 0 ? 'error' : 'info');
                addResult(`⚠️ تحذيرات: ${warningCount}`, warningCount > 0 ? 'warning' : 'info');

                const successRate = Math.round((successCount / (successCount + errorCount + warningCount)) * 100);
                addResult(`🎯 معدل النجاح: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
            }, 8000);
        }

        function clearTestData() {
            addResult('🗑️ مسح البيانات التجريبية...', 'warning');

            // إزالة الموظفين التجريبيين
            const initialCount = employees.length;
            employees = employees.filter(emp => !emp.employeeCode.startsWith('TEST'));
            const removedCount = initialCount - employees.length;

            if (removedCount > 0) {
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }

                if (typeof populateEmployees === 'function') {
                    populateEmployees();
                }

                addResult(`✅ تم حذف ${removedCount} موظف تجريبي`, 'success');
            } else {
                addResult('ℹ️ لا توجد بيانات تجريبية للحذف', 'info');
            }
        }

        function openMainApp() {
            addResult('🔗 فتح التطبيق الرئيسي...', 'info');
            window.open('index.html', '_blank');
        }

        function showSystemInfo() {
            addResult('ℹ️ معلومات النظام:', 'info');
            addResult(`📅 التاريخ: ${new Date().toLocaleDateString('ar-EG')}`, 'info');
            addResult(`⏰ الوقت: ${new Date().toLocaleTimeString('ar-EG')}`, 'info');
            addResult(`👥 عدد الموظفين: ${employees ? employees.length : 0}`, 'info');
            addResult(`🌐 المتصفح: ${navigator.userAgent.split(' ')[0]}`, 'info');
        }

        // تحميل تلقائي عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 تم تحميل صفحة اختبار النظام الجديد', 'success');
            addResult('🚀 جاهز لبدء الاختبارات...', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                if (typeof openNewEmployeeModal === 'function') {
                    addResult('✅ النظام الجديد محمل ومتاح', 'success');
                } else {
                    addResult('❌ النظام الجديد غير محمل', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
