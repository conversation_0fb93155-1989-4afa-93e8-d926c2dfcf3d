<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحل النهائي - السلف والخصومات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .solution-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #28a745;
        }

        .solution-card h3 {
            color: #28a745;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .solution-card p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .test-button.primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .test-button.primary:hover {
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .test-button.warning:hover {
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.danger:hover {
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .results {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature-list {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .feature-list h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .feature-list ul {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            color: #155724;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-trophy"></i> الحل النهائي للسلف والخصومات</h1>
            <p>حلول متعددة ومضمونة لمشكلة عدم الحفظ</p>
        </div>

        <div class="status" id="status">
            جاهز لاختبار الحلول النهائية...
        </div>

        <div class="content">
            <div class="solution-card">
                <h3>🎯 الحلول المتاحة</h3>
                <div class="feature-list">
                    <h4>الحلول المطورة:</h4>
                    <ul>
                        <li>حفظ مضمون مع نافذة محسنة</li>
                        <li>حفظ سريع بدون نافذة (سلف)</li>
                        <li>حفظ سريع بدون نافذة (خصم)</li>
                        <li>حفظ مع طلب البيانات من المستخدم</li>
                        <li>تأكيد فوري للحفظ</li>
                    </ul>
                </div>
            </div>

            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> 1. إنشاء موظف تجريبي
            </button>

            <div class="grid">
                <button class="test-button primary" onclick="testQuickAdvance()">
                    <i class="fas fa-bolt"></i> سلفة سريعة
                </button>
                
                <button class="test-button warning" onclick="testQuickDeduction()">
                    <i class="fas fa-minus-circle"></i> خصم سريع
                </button>
            </div>

            <button class="test-button" onclick="testAdvancesWindow()">
                <i class="fas fa-window-maximize"></i> 2. اختبار النافذة المحسنة
            </button>

            <button class="test-button" onclick="testGuaranteedSave()">
                <i class="fas fa-shield-alt"></i> 3. اختبار الحفظ المضمون
            </button>

            <button class="test-button danger" onclick="checkAllData()">
                <i class="fas fa-database"></i> 4. فحص جميع البيانات
            </button>

            <div style="margin: 20px 0; padding: 15px; background: #d1ecf1; border-radius: 8px; border: 1px solid #bee5eb;">
                <h4 style="color: #0c5460; margin-bottom: 10px;">💡 كيفية الاستخدام:</h4>
                <p style="color: #0c5460; margin: 0; line-height: 1.5;">
                    • <strong>السلفة السريعة:</strong> إضافة سلفة مباشرة بدون نافذة<br>
                    • <strong>الخصم السريع:</strong> إضافة خصم مباشرة بدون نافذة<br>
                    • <strong>النافذة المحسنة:</strong> استخدم زر "💾 حفظ مضمون"<br>
                    • <strong>الحفظ المضمون:</strong> يطلب البيانات إذا لم تكن متاحة
                </p>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>

    <script>
        function log(message, color = '#ecf0f1') {
            const results = document.getElementById('results');
            const time = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="color: ${color}; margin-bottom: 3px;">[${time}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            log('👤 إنشاء موظف تجريبي للحل النهائي', '#3498db');
            
            try {
                const testEmployee = {
                    id: 44444,
                    name: "موظف الحل النهائي",
                    position: "مختبر الحلول المتقدمة",
                    employeeCode: "ULTIMATE",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/2c3e50/ffffff?text=FINAL",
                    employmentType: "monthly",
                    salary: { basic: 6000, total: 6000 },
                    dateAdded: new Date().toISOString()
                };
                
                // إزالة الموظف إذا كان موجود
                const existingIndex = employees.findIndex(emp => emp.id === 44444);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    log('🗑️ تم إزالة الموظف الموجود', '#f39c12');
                }
                
                employees.push(testEmployee);
                
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                
                log('✅ تم إنشاء الموظف التجريبي بنجاح', '#27ae60');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
                
                // التأكد من إنشاء مصفوفة السلف
                if (!window.advancesDeductionsData) {
                    window.advancesDeductionsData = [];
                    log('📋 تم إنشاء مصفوفة السلف والخصومات', '#3498db');
                }
                
            } catch (error) {
                log(`❌ خطأ في إنشاء الموظف: ${error.message}`, '#e74c3c');
                updateStatus('❌ فشل في إنشاء الموظف', 'error');
            }
        }

        // اختبار السلفة السريعة
        function testQuickAdvance() {
            updateStatus('جاري اختبار السلفة السريعة...', 'warning');
            log('⚡ اختبار السلفة السريعة', '#3498db');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 44444);
                if (!employee) {
                    log('❌ يرجى إنشاء الموظف التجريبي أولاً', '#e74c3c');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // اختبار الدالة السريعة
                if (typeof quickAddAdvance === 'function') {
                    log('🔄 استدعاء دالة السلفة السريعة...', '#3498db');
                    const success = quickAddAdvance(44444);
                    
                    if (success) {
                        log('✅ تم اختبار السلفة السريعة بنجاح!', '#27ae60');
                        updateStatus('✅ السلفة السريعة تعمل!', 'success');
                    } else {
                        log('❌ فشل اختبار السلفة السريعة', '#e74c3c');
                        updateStatus('❌ فشل السلفة السريعة', 'error');
                    }
                } else {
                    log('❌ دالة السلفة السريعة غير موجودة', '#e74c3c');
                    updateStatus('❌ الدالة السريعة مفقودة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار السلفة السريعة: ${error.message}`, '#e74c3c');
                updateStatus('❌ خطأ في السلفة السريعة', 'error');
            }
        }

        // اختبار الخصم السريع
        function testQuickDeduction() {
            updateStatus('جاري اختبار الخصم السريع...', 'warning');
            log('⚡ اختبار الخصم السريع', '#3498db');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 44444);
                if (!employee) {
                    log('❌ يرجى إنشاء الموظف التجريبي أولاً', '#e74c3c');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // اختبار الدالة السريعة
                if (typeof quickAddDeduction === 'function') {
                    log('🔄 استدعاء دالة الخصم السريع...', '#3498db');
                    const success = quickAddDeduction(44444);
                    
                    if (success) {
                        log('✅ تم اختبار الخصم السريع بنجاح!', '#27ae60');
                        updateStatus('✅ الخصم السريع يعمل!', 'success');
                    } else {
                        log('❌ فشل اختبار الخصم السريع', '#e74c3c');
                        updateStatus('❌ فشل الخصم السريع', 'error');
                    }
                } else {
                    log('❌ دالة الخصم السريع غير موجودة', '#e74c3c');
                    updateStatus('❌ الدالة السريعة مفقودة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار الخصم السريع: ${error.message}`, '#e74c3c');
                updateStatus('❌ خطأ في الخصم السريع', 'error');
            }
        }

        // اختبار النافذة المحسنة
        function testAdvancesWindow() {
            updateStatus('جاري اختبار النافذة المحسنة...', 'warning');
            log('🪟 اختبار النافذة المحسنة', '#3498db');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 44444);
                if (!employee) {
                    log('❌ يرجى إنشاء الموظف التجريبي أولاً', '#e74c3c');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // فتح النافذة
                viewAdvancesDeductions(44444);
                log('✅ تم فتح النافذة المحسنة', '#27ae60');
                log('💡 استخدم زر "💾 حفظ مضمون" الأخضر في النافذة', '#f39c12');
                updateStatus('✅ النافذة مفتوحة - جرب الحفظ المضمون', 'success');
                
            } catch (error) {
                log(`❌ خطأ في فتح النافذة: ${error.message}`, '#e74c3c');
                updateStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // اختبار الحفظ المضمون
        function testGuaranteedSave() {
            updateStatus('جاري اختبار الحفظ المضمون...', 'warning');
            log('🛡️ اختبار الحفظ المضمون', '#3498db');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 44444);
                if (!employee) {
                    log('❌ يرجى إنشاء الموظف التجريبي أولاً', '#e74c3c');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // اختبار الدالة المضمونة
                if (typeof saveAdvanceDeductionSimple === 'function') {
                    log('🔄 استدعاء دالة الحفظ المضمون...', '#3498db');
                    const success = saveAdvanceDeductionSimple(44444);
                    
                    if (success) {
                        log('✅ تم اختبار الحفظ المضمون بنجاح!', '#27ae60');
                        updateStatus('✅ الحفظ المضمون يعمل!', 'success');
                    } else {
                        log('❌ فشل اختبار الحفظ المضمون', '#e74c3c');
                        updateStatus('❌ فشل الحفظ المضمون', 'error');
                    }
                } else {
                    log('❌ دالة الحفظ المضمون غير موجودة', '#e74c3c');
                    updateStatus('❌ الدالة المضمونة مفقودة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار الحفظ المضمون: ${error.message}`, '#e74c3c');
                updateStatus('❌ خطأ في الحفظ المضمون', 'error');
            }
        }

        // فحص جميع البيانات
        function checkAllData() {
            updateStatus('جاري فحص جميع البيانات...', 'warning');
            log('🔍 فحص شامل لجميع البيانات المحفوظة', '#3498db');
            
            try {
                // فحص localStorage
                const savedData = localStorage.getItem('oscoAdvancesDeductions');
                if (savedData) {
                    const parsed = JSON.parse(savedData);
                    log(`📊 إجمالي السجلات: ${parsed.length}`, '#3498db');
                    
                    // فحص سجلات الموظف التجريبي
                    const employeeRecords = parsed.filter(r => r.employeeId === 44444);
                    log(`👤 سجلات الموظف التجريبي: ${employeeRecords.length}`, '#3498db');
                    
                    if (employeeRecords.length > 0) {
                        log('📋 تفاصيل السجلات:', '#f39c12');
                        employeeRecords.forEach((record, index) => {
                            const typeText = record.type === 'advance' ? 'سلفة' : 'خصم';
                            const date = new Date(record.date).toLocaleDateString();
                            log(`${index + 1}. ${typeText}: ${record.amount} ج.م - ${record.description || 'بدون وصف'} (${date})`, '#27ae60');
                        });
                        updateStatus('✅ البيانات محفوظة بنجاح!', 'success');
                    } else {
                        log('⚠️ لا توجد سجلات للموظف التجريبي', '#f39c12');
                        updateStatus('⚠️ لا توجد سجلات', 'warning');
                    }
                } else {
                    log('❌ لا توجد بيانات في localStorage', '#e74c3c');
                    updateStatus('❌ لا توجد بيانات محفوظة', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في فحص البيانات: ${error.message}`, '#e74c3c');
                updateStatus('❌ خطأ في فحص البيانات', 'error');
            }
        }

        // اختبار تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 مرحباً بك في الحل النهائي للسلف والخصومات', '#3498db');
                log('🎯 هذا النظام يوفر 4 طرق مختلفة للحفظ المضمون', '#3498db');
                
                // فحص النظام
                if (typeof employees !== 'undefined') {
                    log(`✅ النظام محمل - ${employees.length} موظف`, '#27ae60');
                } else {
                    log('❌ النظام غير محمل', '#e74c3c');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }
                
                // فحص الدوال الجديدة
                const newFunctions = ['quickAddAdvance', 'quickAddDeduction', 'saveAdvanceDeductionSimple'];
                let functionsOk = true;
                newFunctions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        log(`✅ دالة ${func}: متاحة`, '#27ae60');
                    } else {
                        log(`❌ دالة ${func}: مفقودة`, '#e74c3c');
                        functionsOk = false;
                    }
                });
                
                if (functionsOk) {
                    log('🎉 جميع الحلول متاحة ومضمونة!', '#27ae60');
                    updateStatus('✅ النظام جاهز - جميع الحلول متاحة', 'success');
                } else {
                    log('❌ بعض الحلول مفقودة', '#e74c3c');
                    updateStatus('❌ مشكلة في الحلول', 'error');
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
