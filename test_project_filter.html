<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فلتر المشروع - نافذة أيام الحضور</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #f8f9fa;
            color: #495057;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .header h1 {
            margin: 0;
            font-size: 1.8em;
        }

        .content {
            padding: 20px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .test-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.success:hover {
            background: #1e7e34;
        }

        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }

        .test-button.warning:hover {
            background: #e0a800;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .test-button.danger:hover {
            background: #c82333;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .status-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .status-label {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-filter"></i> اختبار فلتر المشروع - نافذة أيام الحضور</h1>
            <p>اختبار الفلتر الجديد لتصفية السجلات حسب المشروع</p>
        </div>

        <div class="content">
            <!-- قسم الاختبارات الأساسية -->
            <div class="test-section">
                <h3><i class="fas fa-play"></i> الاختبارات الأساسية</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="createTestData()">
                        <i class="fas fa-database"></i> إنشاء بيانات تجريبية
                    </button>

                    <button class="test-button" onclick="openDaysWindow()">
                        <i class="fas fa-calendar-alt"></i> فتح نافذة أيام الحضور
                    </button>

                    <button class="test-button warning" onclick="openRecordsWindow()">
                        <i class="fas fa-list"></i> فتح نافذة السجلات
                    </button>

                    <button class="test-button" onclick="testProjectFilter()">
                        <i class="fas fa-filter"></i> اختبار فلتر المشروع
                    </button>

                    <button class="test-button" onclick="window.open('/', '_blank')">
                        <i class="fas fa-home"></i> التطبيق الأصلي
                    </button>

                    <button class="test-button danger" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="test-section">
                <h3><i class="fas fa-chart-bar"></i> الإحصائيات</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-number" id="employeesCount">0</div>
                        <div class="status-label">عدد الموظفين</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="recordsCount">0</div>
                        <div class="status-label">عدد السجلات</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="projectsCount">0</div>
                        <div class="status-label">عدد المشروعات</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number" id="testsRun">0</div>
                        <div class="status-label">الاختبارات المنجزة</div>
                    </div>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> سجل الأحداث</h3>
                <div class="log" id="log">
                    <div class="log-entry log-info">[تحميل] جاهز لاختبار فلتر المشروع...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        let testsRun = 0;
        let testsSuccess = 0;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('employeesCount').textContent = employees ? employees.length : 0;
            document.getElementById('recordsCount').textContent = employeeDaysData ? employeeDaysData.length : 0;

            // حساب عدد المشروعات الفريدة
            const uniqueProjects = employeeDaysData ? [...new Set(employeeDaysData.map(record => record.projectName))] : [];
            document.getElementById('projectsCount').textContent = uniqueProjects.length;

            document.getElementById('testsRun').textContent = testsRun;
        }

        function createTestData() {
            log('➕ إنشاء بيانات تجريبية مع مشروعات متعددة...', 'info');
            testsRun++;

            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 12345,
                    name: 'أحمد محمد - اختبار الفلتر',
                    position: 'مطور',
                    employeeCode: 'FILTER12345',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };

                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 12345);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }

                employees.push(testEmployee);

                // إنشاء سجلات أيام متعددة لمشروعات مختلفة
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 12345,
                        projectName: 'مشروع تطوير الموقع',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 22,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 12345,
                        projectName: 'مشروع النظام المحاسبي',
                        startDate: '2024-02-01',
                        endDate: '2024-02-29',
                        calculatedDays: 20,
                        absenceDays: 1,
                        overtimeHours: 5,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 12345,
                        projectName: 'مشروع تطوير الموقع',
                        startDate: '2024-03-01',
                        endDate: '2024-03-31',
                        calculatedDays: 23,
                        absenceDays: 0,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 4,
                        employeeId: 12345,
                        projectName: 'مشروع التدريب',
                        startDate: '2024-04-01',
                        endDate: '2024-04-30',
                        calculatedDays: 21,
                        absenceDays: 3,
                        overtimeHours: 12,
                        createdAt: new Date().toISOString()
                    }
                ];

                // إزالة السجلات السابقة للموظف
                employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 12345);

                // إضافة السجلات الجديدة
                testRecords.forEach(record => {
                    employeeDaysData.push(record);
                });

                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();

                testsSuccess++;
                log(`✅ تم إنشاء الموظف: ${testEmployee.name}`, 'success');
                log(`✅ تم إنشاء ${testRecords.length} سجل أيام لـ 3 مشروعات مختلفة`, 'success');

            } catch (error) {
                log('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }

            updateStats();
        }

        function openDaysWindow() {
            log('📅 فتح نافذة أيام الحضور...', 'info');
            testsRun++;

            try {
                if (!employees.find(emp => emp.id === 12345)) {
                    log('⚠️ لا يوجد موظف تجريبي، سيتم إنشاؤه...', 'warning');
                    createTestData();
                }

                if (typeof createEnhancedDaysCalculator === 'function') {
                    createEnhancedDaysCalculator(12345);
                    testsSuccess++;
                    log('✅ تم فتح نافذة أيام الحضور', 'success');
                } else {
                    log('❌ دالة createEnhancedDaysCalculator غير موجودة', 'error');
                }

            } catch (error) {
                log('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }

            updateStats();
        }

        function openRecordsWindow() {
            log('📋 فتح نافذة السجلات مع فلتر المشروع...', 'info');
            testsRun++;

            try {
                if (!employees.find(emp => emp.id === 12345)) {
                    log('⚠️ لا يوجد موظف تجريبي، سيتم إنشاؤه...', 'warning');
                    createTestData();
                }

                if (typeof viewDaysRecords === 'function') {
                    viewDaysRecords(12345);
                    testsSuccess++;
                    log('✅ تم فتح نافذة السجلات مع فلتر المشروع', 'success');
                } else {
                    log('❌ دالة viewDaysRecords غير موجودة', 'error');
                }

            } catch (error) {
                log('❌ خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }

            updateStats();
        }

        function testProjectFilter() {
            log('🔍 اختبار فلتر المشروع...', 'info');
            testsRun++;

            try {
                // فتح النافذة أولاً
                if (!employees.find(emp => emp.id === 12345)) {
                    createTestData();
                }

                viewDaysRecords(12345);

                // فحص وجود فلتر المشروع
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        log('✅ تم فتح النافذة بنجاح', 'success');

                        // فحص عناصر الفلتر
                        const projectFilter = modal.querySelector('#projectFilter');
                        const filterStatus = modal.querySelector('#filterStatus');
                        const clearButton = modal.querySelector('button[onclick*="clearProjectFilter"]');

                        log(`🔍 فحص العناصر:`, 'info');
                        log(`- فلتر المشروع: ${projectFilter ? '✅ موجود' : '❌ مفقود'}`, projectFilter ? 'success' : 'error');
                        log(`- مؤشر الحالة: ${filterStatus ? '✅ موجود' : '❌ مفقود'}`, filterStatus ? 'success' : 'error');
                        log(`- زر المسح: ${clearButton ? '✅ موجود' : '❌ مفقود'}`, clearButton ? 'success' : 'error');

                        if (projectFilter && filterStatus && clearButton) {
                            log('✅ جميع عناصر فلتر المشروع موجودة', 'success');

                            // اختبار عدد الخيارات في الفلتر
                            const options = projectFilter.querySelectorAll('option');
                            log(`✅ فلتر المشروع يحتوي على ${options.length} خيار`, 'success');

                            // عرض الخيارات
                            options.forEach((option, index) => {
                                log(`  ${index + 1}. "${option.textContent}" (value: "${option.value}")`, 'info');
                            });

                            // اختبار تطبيق الفلتر
                            if (options.length > 1) {
                                const selectedProject = options[1].value;
                                log(`🔄 تطبيق فلتر للمشروع: "${selectedProject}"`, 'info');

                                projectFilter.value = selectedProject;

                                if (typeof filterRecordsByProject === 'function') {
                                    filterRecordsByProject(12345);
                                    log('✅ تم تطبيق الفلتر بنجاح', 'success');

                                    // فحص النتائج
                                    setTimeout(() => {
                                        const statusText = filterStatus.textContent;
                                        log(`📊 حالة الفلتر: ${statusText}`, 'info');
                                        testsSuccess++;
                                    }, 500);
                                } else {
                                    log('❌ دالة filterRecordsByProject غير موجودة', 'error');
                                }
                            } else {
                                log('⚠️ لا توجد مشروعات كافية للاختبار', 'warning');
                            }
                        } else {
                            log('❌ بعض عناصر فلتر المشروع مفقودة', 'error');

                            // فحص تفصيلي للعناصر المفقودة
                            if (!projectFilter) {
                                log('  - فلتر المشروع (#projectFilter) غير موجود', 'error');
                            }
                            if (!filterStatus) {
                                log('  - مؤشر الحالة (#filterStatus) غير موجود', 'error');
                            }
                            if (!clearButton) {
                                log('  - زر المسح غير موجود', 'error');
                            }
                        }

                        // فحص إضافي للنافذة
                        const filterSections = modal.querySelectorAll('div[style*="background: #f8f9fa"]');
                        log(`🔍 عدد الأقسام بخلفية #f8f9fa: ${filterSections.length}`, 'info');

                        // البحث عن قسم الفلتر تحديداً
                        let filterSection = null;
                        filterSections.forEach((section, index) => {
                            if (section.innerHTML.includes('فلتر المشروع')) {
                                filterSection = section;
                                log(`✅ وجد قسم فلتر المشروع في القسم رقم ${index + 1}`, 'success');
                            }
                        });

                        if (!filterSection) {
                            log('❌ قسم فلتر المشروع غير موجود', 'error');

                            // فحص تفصيلي للمحتوى
                            log('🔍 فحص محتوى النافذة للبحث عن "فلتر المشروع":', 'info');
                            if (modal.innerHTML.includes('فلتر المشروع')) {
                                log('✅ النص "فلتر المشروع" موجود في النافذة', 'success');
                            } else {
                                log('❌ النص "فلتر المشروع" غير موجود في النافذة', 'error');
                            }
                        }

                    } else {
                        log('❌ لم يتم فتح النافذة', 'error');
                    }
                }, 1500); // زيادة الوقت للتأكد من تحميل النافذة

            } catch (error) {
                log('❌ خطأ في اختبار الفلتر: ' + error.message, 'error');
            }

            updateStats();
        }

        function clearLog() {
            document.getElementById('log').innerHTML =
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
            testsRun = 0;
            testsSuccess = 0;
            updateStats();
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة اختبار فلتر المشروع', 'success');
            updateStats();

            setTimeout(() => {
                log('🔍 فحص النظام...', 'info');

                // فحص الدوال المطلوبة
                const requiredFunctions = [
                    'filterRecordsByProject',
                    'clearProjectFilter',
                    'updateFilteredRecordsTable'
                ];

                requiredFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        log(`✅ دالة ${funcName} موجودة`, 'success');
                    } else {
                        log(`❌ دالة ${funcName} غير موجودة`, 'error');
                    }
                });

                updateStats();
            }, 1000);
        });
    </script>
</body>
</html>
