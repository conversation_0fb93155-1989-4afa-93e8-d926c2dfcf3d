<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دوال الحفظ المحسنة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
        }

        .test-btn.primary { background: #3498db; }
        .test-btn.success { background: #27ae60; }
        .test-btn.warning { background: #f39c12; }
        .test-btn.danger { background: #e74c3c; }
        .test-btn.info { background: #9b59b6; }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .results {
            margin-top: 20px;
            padding: 15px;
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .function-test {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .function-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .function-result {
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .function-result.success { background: #d4edda; color: #155724; }
        .function-result.error { background: #f8d7da; color: #721c24; }
        .function-result.warning { background: #fff3cd; color: #856404; }

        .clear-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            float: left;
            margin-bottom: 10px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار دوال الحفظ المحسنة</h1>
            <p>اختبار شامل للدوال الجديدة: saveDaysCalculationEnhanced و saveDaysCalculationFast</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">إجمالي الاختبارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">اختبارات ناجحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">اختبارات فاشلة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">معدل النجاح</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 اختبار وجود الدوال</h3>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testFunctionExistence()">فحص وجود الدوال</button>
                <button class="test-btn info" onclick="testFunctionTypes()">فحص أنواع الدوال</button>
                <button class="test-btn warning" onclick="listAllSaveFunctions()">عرض جميع دوال الحفظ</button>
            </div>
        </div>

        <div class="test-section">
            <h3>⚡ اختبار الدوال المحسنة</h3>
            <div class="test-buttons">
                <button class="test-btn success" onclick="testSaveDaysCalculationEnhanced()">اختبار saveDaysCalculationEnhanced</button>
                <button class="test-btn success" onclick="testSaveDaysCalculationFast()">اختبار saveDaysCalculationFast</button>
                <button class="test-btn warning" onclick="testWithoutModal()">اختبار بدون نافذة مفتوحة</button>
                <button class="test-btn info" onclick="testWithMockData()">اختبار ببيانات وهمية</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 اختبارات متقدمة</h3>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testErrorHandling()">اختبار معالجة الأخطاء</button>
                <button class="test-btn warning" onclick="testEdgeCases()">اختبار الحالات الحدية</button>
                <button class="test-btn danger" onclick="testPerformance()">اختبار الأداء</button>
                <button class="test-btn info" onclick="runFullTestSuite()">تشغيل جميع الاختبارات</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🛠️ أدوات مساعدة</h3>
            <div class="test-buttons">
                <button class="test-btn success" onclick="createMockModal()">إنشاء نافذة وهمية</button>
                <button class="test-btn warning" onclick="fillMockData()">ملء بيانات وهمية</button>
                <button class="test-btn danger" onclick="cleanupTests()">تنظيف الاختبارات</button>
                <button class="test-btn info" onclick="openOriginalPage()">فتح الصفحة الأصلية</button>
            </div>
        </div>

        <div id="status" class="status info">جاهز لبدء الاختبارات...</div>

        <div class="results">
            <button class="clear-btn" onclick="clearResults()">مسح النتائج</button>
            <div id="results">📋 سجل نتائج الاختبارات:
جاهز لبدء اختبار الدوال المحسنة...</div>
        </div>
    </div>

    <script>
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('failedTests').textContent = failedTests;

            const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';

            results.innerHTML += `\n[${timestamp}] ${icon} ${message}`;
            results.scrollTop = results.scrollHeight;

            if (type === 'success') {
                passedTests++;
            } else if (type === 'error') {
                failedTests++;
            }

            if (type === 'success' || type === 'error') {
                totalTests++;
                updateStats();
            }
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '📋 سجل نتائج الاختبارات:\nتم مسح النتائج...';
            totalTests = 0;
            passedTests = 0;
            failedTests = 0;
            updateStats();
            updateStatus('تم مسح النتائج - جاهز لبدء اختبارات جديدة', 'info');
        }

        function testFunctionExistence() {
            addResult('🔍 بدء فحص وجود الدوال المحسنة...');
            updateStatus('جاري فحص وجود الدوال...', 'warning');

            const functions = [
                'saveDaysCalculation',
                'saveDaysCalculationEnhanced',
                'saveDaysCalculationFast'
            ];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            updateStatus('تم الانتهاء من فحص وجود الدوال', 'success');
        }

        function testFunctionTypes() {
            addResult('🔍 بدء فحص أنواع الدوال...');

            const functions = ['saveDaysCalculationEnhanced', 'saveDaysCalculationFast'];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    try {
                        const funcStr = window[funcName].toString();
                        const hasConsoleLog = funcStr.includes('console.log');
                        const hasErrorHandling = funcStr.includes('try') && funcStr.includes('catch');
                        const hasReturnValue = funcStr.includes('return');

                        addResult(`📊 ${funcName}:`, 'info');
                        addResult(`  - يحتوي على تسجيل: ${hasConsoleLog ? 'نعم' : 'لا'}`, hasConsoleLog ? 'success' : 'warning');
                        addResult(`  - يحتوي على معالجة أخطاء: ${hasErrorHandling ? 'نعم' : 'لا'}`, hasErrorHandling ? 'success' : 'warning');
                        addResult(`  - يحتوي على قيمة إرجاع: ${hasReturnValue ? 'نعم' : 'لا'}`, hasReturnValue ? 'success' : 'warning');
                    } catch (error) {
                        addResult(`❌ خطأ في فحص ${funcName}: ${error.message}`, 'error');
                    }
                } else {
                    addResult(`❌ الدالة ${funcName} غير موجودة`, 'error');
                }
            });
        }

        function listAllSaveFunctions() {
            addResult('📋 البحث عن جميع دوال الحفظ في النظام...');

            const saveFunctions = [];
            for (let prop in window) {
                if (typeof window[prop] === 'function' && prop.toLowerCase().includes('save')) {
                    saveFunctions.push(prop);
                }
            }

            addResult(`🔍 تم العثور على ${saveFunctions.length} دالة حفظ:`, 'info');
            saveFunctions.forEach(func => {
                addResult(`  - ${func}`, 'info');
            });
        }

        function testSaveDaysCalculationEnhanced() {
            addResult('⚡ اختبار saveDaysCalculationEnhanced...');
            updateStatus('جاري اختبار الدالة المحسنة...', 'warning');

            if (typeof saveDaysCalculationEnhanced !== 'function') {
                addResult('❌ الدالة saveDaysCalculationEnhanced غير موجودة', 'error');
                updateStatus('فشل الاختبار - الدالة غير موجودة', 'error');
                return;
            }

            try {
                // اختبار بمعرف موظف وهمي
                const result = saveDaysCalculationEnhanced(999);

                if (result === false) {
                    addResult('✅ الدالة تعمل وترجع false عند عدم وجود نافذة (متوقع)', 'success');
                } else if (result === true) {
                    addResult('🎉 الدالة تعمل وترجع true - نجح الحفظ!', 'success');
                } else {
                    addResult(`⚠️ الدالة ترجع قيمة غير متوقعة: ${result}`, 'warning');
                }

                updateStatus('تم اختبار الدالة المحسنة بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في اختبار saveDaysCalculationEnhanced: ${error.message}`, 'error');
                updateStatus('فشل اختبار الدالة المحسنة', 'error');
            }
        }

        function testSaveDaysCalculationFast() {
            addResult('⚡ اختبار saveDaysCalculationFast...');
            updateStatus('جاري اختبار الدالة السريعة...', 'warning');

            if (typeof saveDaysCalculationFast !== 'function') {
                addResult('❌ الدالة saveDaysCalculationFast غير موجودة', 'error');
                updateStatus('فشل الاختبار - الدالة غير موجودة', 'error');
                return;
            }

            try {
                const result = saveDaysCalculationFast();

                if (result === false) {
                    addResult('✅ الدالة تعمل وترجع false عند عدم وجود نافذة (متوقع)', 'success');
                } else if (result === true) {
                    addResult('🎉 الدالة تعمل وترجع true - نجح الحفظ!', 'success');
                } else {
                    addResult(`⚠️ الدالة ترجع قيمة غير متوقعة: ${result}`, 'warning');
                }

                updateStatus('تم اختبار الدالة السريعة بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في اختبار saveDaysCalculationFast: ${error.message}`, 'error');
                updateStatus('فشل اختبار الدالة السريعة', 'error');
            }
        }

        function testWithoutModal() {
            addResult('🔍 اختبار الدوال بدون نافذة مفتوحة...');
            updateStatus('جاري اختبار بدون نافذة...', 'warning');

            // التأكد من عدم وجود نوافذ مفتوحة
            const modals = document.querySelectorAll('[id*="modal"], [id*="Modal"]');
            modals.forEach(modal => {
                if (modal.style.display !== 'none') {
                    modal.style.display = 'none';
                }
            });

            try {
                const result1 = saveDaysCalculationEnhanced(999);
                const result2 = saveDaysCalculationFast();

                if (result1 === false && result2 === false) {
                    addResult('✅ الدوال تتعامل بشكل صحيح مع عدم وجود نافذة', 'success');
                } else {
                    addResult('⚠️ نتائج غير متوقعة عند عدم وجود نافذة', 'warning');
                }

                updateStatus('تم اختبار الحالة بدون نافذة', 'success');
            } catch (error) {
                addResult(`❌ خطأ في اختبار بدون نافذة: ${error.message}`, 'error');
                updateStatus('فشل اختبار بدون نافذة', 'error');
            }
        }

        function testWithMockData() {
            addResult('🎭 اختبار ببيانات وهمية...');
            updateStatus('جاري إنشاء بيانات وهمية...', 'warning');

            try {
                // إنشاء موظفين وهميين إذا لم يكونوا موجودين
                if (!window.employees) {
                    window.employees = [];
                }

                const mockEmployee = {
                    id: 999,
                    name: 'موظف اختبار محسن',
                    position: 'مطور نظم',
                    employeeCode: 'TEST999',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };

                // إضافة الموظف الوهمي إذا لم يكن موجوداً
                if (!window.employees.find(emp => emp.id === 999)) {
                    window.employees.push(mockEmployee);
                    addResult('✅ تم إنشاء موظف وهمي للاختبار', 'success');
                }

                // إنشاء مصفوفة بيانات الأيام إذا لم تكن موجودة
                if (!window.employeeDaysData) {
                    window.employeeDaysData = [];
                    addResult('✅ تم إنشاء مصفوفة بيانات الأيام', 'success');
                }

                updateStatus('تم إنشاء البيانات الوهمية بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في إنشاء البيانات الوهمية: ${error.message}`, 'error');
                updateStatus('فشل إنشاء البيانات الوهمية', 'error');
            }
        }

        function createMockModal() {
            addResult('🏗️ إنشاء نافذة وهمية للاختبار...');
            updateStatus('جاري إنشاء نافذة وهمية...', 'warning');

            try {
                // إزالة النافذة الوهمية إذا كانت موجودة
                const existingModal = document.getElementById('enhancedDaysModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // إنشاء نافذة وهمية
                const mockModal = document.createElement('div');
                mockModal.id = 'enhancedDaysModal';
                mockModal.style.display = 'block';
                mockModal.innerHTML = `
                    <h2>حساب الأيام - موظف اختبار محسن</h2>
                    <select id="daysMonth">
                        <option value="12">ديسمبر</option>
                    </select>
                    <select id="daysYear">
                        <option value="2024">2024</option>
                    </select>
                    <select id="daysProject">
                        <option value="1">مشروع اختبار</option>
                    </select>
                    <input type="number" id="actualDaysDisplay" value="25">
                    <input type="number" id="overtimeHours" value="8">
                `;

                document.body.appendChild(mockModal);
                addResult('✅ تم إنشاء نافذة وهمية بنجاح', 'success');
                updateStatus('تم إنشاء النافذة الوهمية', 'success');
            } catch (error) {
                addResult(`❌ خطأ في إنشاء النافذة الوهمية: ${error.message}`, 'error');
                updateStatus('فشل إنشاء النافذة الوهمية', 'error');
            }
        }

        function fillMockData() {
            addResult('📝 ملء البيانات الوهمية في النافذة...');

            const fields = [
                { id: 'daysMonth', value: '12' },
                { id: 'daysYear', value: '2024' },
                { id: 'daysProject', value: '1' },
                { id: 'actualDaysDisplay', value: '25' },
                { id: 'overtimeHours', value: '8' }
            ];

            let filledCount = 0;
            fields.forEach(field => {
                const element = document.getElementById(field.id);
                if (element) {
                    element.value = field.value;
                    filledCount++;
                    addResult(`✓ تم ملء ${field.id}: ${field.value}`, 'info');
                } else {
                    addResult(`⚠️ لم يتم العثور على ${field.id}`, 'warning');
                }
            });

            addResult(`✅ تم ملء ${filledCount}/${fields.length} حقل`, 'success');
        }

        function testErrorHandling() {
            addResult('🛡️ اختبار معالجة الأخطاء...');
            updateStatus('جاري اختبار معالجة الأخطاء...', 'warning');

            try {
                // اختبار مع معرف موظف غير صحيح
                const result1 = saveDaysCalculationEnhanced(null);
                const result2 = saveDaysCalculationEnhanced(undefined);
                const result3 = saveDaysCalculationEnhanced('invalid');

                if (result1 === false && result2 === false && result3 === false) {
                    addResult('✅ معالجة الأخطاء تعمل بشكل صحيح', 'success');
                } else {
                    addResult('⚠️ معالجة الأخطاء قد تحتاج تحسين', 'warning');
                }

                updateStatus('تم اختبار معالجة الأخطاء', 'success');
            } catch (error) {
                addResult(`❌ خطأ في اختبار معالجة الأخطاء: ${error.message}`, 'error');
                updateStatus('فشل اختبار معالجة الأخطاء', 'error');
            }
        }

        function testEdgeCases() {
            addResult('🎯 اختبار الحالات الحدية...');
            updateStatus('جاري اختبار الحالات الحدية...', 'warning');

            const edgeCases = [
                { name: 'معرف موظف صفر', value: 0 },
                { name: 'معرف موظف سالب', value: -1 },
                { name: 'معرف موظف كبير جداً', value: 999999999 },
                { name: 'نص بدلاً من رقم', value: 'abc' },
                { name: 'كائن', value: {} },
                { name: 'مصفوفة', value: [] }
            ];

            let passedCases = 0;
            edgeCases.forEach(testCase => {
                try {
                    const result = saveDaysCalculationEnhanced(testCase.value);
                    if (result === false) {
                        addResult(`✅ ${testCase.name}: تم التعامل معه بشكل صحيح`, 'success');
                        passedCases++;
                    } else {
                        addResult(`⚠️ ${testCase.name}: نتيجة غير متوقعة`, 'warning');
                    }
                } catch (error) {
                    addResult(`❌ ${testCase.name}: خطأ - ${error.message}`, 'error');
                }
            });

            addResult(`📊 نجح ${passedCases}/${edgeCases.length} من الحالات الحدية`, 'info');
            updateStatus('تم اختبار الحالات الحدية', 'success');
        }

        function testPerformance() {
            addResult('⚡ اختبار الأداء...');
            updateStatus('جاري اختبار الأداء...', 'warning');

            try {
                const iterations = 100;
                const startTime = performance.now();

                for (let i = 0; i < iterations; i++) {
                    saveDaysCalculationFast();
                }

                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const avgTime = totalTime / iterations;

                addResult(`📊 نتائج اختبار الأداء:`, 'info');
                addResult(`  - عدد التكرارات: ${iterations}`, 'info');
                addResult(`  - الوقت الإجمالي: ${totalTime.toFixed(2)} مللي ثانية`, 'info');
                addResult(`  - متوسط الوقت: ${avgTime.toFixed(2)} مللي ثانية`, 'info');

                if (avgTime < 10) {
                    addResult('✅ الأداء ممتاز (أقل من 10 مللي ثانية)', 'success');
                } else if (avgTime < 50) {
                    addResult('⚠️ الأداء جيد (10-50 مللي ثانية)', 'warning');
                } else {
                    addResult('❌ الأداء بطيء (أكثر من 50 مللي ثانية)', 'error');
                }

                updateStatus('تم اختبار الأداء', 'success');
            } catch (error) {
                addResult(`❌ خطأ في اختبار الأداء: ${error.message}`, 'error');
                updateStatus('فشل اختبار الأداء', 'error');
            }
        }

        function runFullTestSuite() {
            addResult('🚀 بدء تشغيل جميع الاختبارات...');
            updateStatus('جاري تشغيل جميع الاختبارات...', 'warning');

            const tests = [
                { name: 'فحص وجود الدوال', func: testFunctionExistence },
                { name: 'فحص أنواع الدوال', func: testFunctionTypes },
                { name: 'إنشاء بيانات وهمية', func: testWithMockData },
                { name: 'إنشاء نافذة وهمية', func: createMockModal },
                { name: 'ملء البيانات', func: fillMockData },
                { name: 'اختبار الدالة المحسنة', func: testSaveDaysCalculationEnhanced },
                { name: 'اختبار الدالة السريعة', func: testSaveDaysCalculationFast },
                { name: 'اختبار بدون نافذة', func: testWithoutModal },
                { name: 'اختبار معالجة الأخطاء', func: testErrorHandling },
                { name: 'اختبار الحالات الحدية', func: testEdgeCases },
                { name: 'اختبار الأداء', func: testPerformance }
            ];

            let currentTest = 0;

            function runNextTest() {
                if (currentTest < tests.length) {
                    const test = tests[currentTest];
                    addResult(`🔄 تشغيل: ${test.name}...`, 'info');

                    try {
                        test.func();
                        currentTest++;
                        setTimeout(runNextTest, 500);
                    } catch (error) {
                        addResult(`❌ فشل في ${test.name}: ${error.message}`, 'error');
                        currentTest++;
                        setTimeout(runNextTest, 500);
                    }
                } else {
                    addResult('🎉 تم الانتهاء من جميع الاختبارات!', 'success');
                    updateStatus('تم الانتهاء من جميع الاختبارات', 'success');
                }
            }

            runNextTest();
        }

        function cleanupTests() {
            addResult('🧹 تنظيف الاختبارات...');

            // إزالة النوافذ الوهمية
            const mockModal = document.getElementById('enhancedDaysModal');
            if (mockModal) {
                mockModal.remove();
                addResult('✅ تم إزالة النافذة الوهمية', 'success');
            }

            // إزالة البيانات الوهمية
            if (window.employees) {
                window.employees = window.employees.filter(emp => emp.id !== 999);
                addResult('✅ تم إزالة الموظف الوهمي', 'success');
            }

            updateStatus('تم تنظيف الاختبارات', 'success');
        }

        function openOriginalPage() {
            addResult('🌐 فتح الصفحة الأصلية...', 'info');
            window.open('index.html', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة اختبار الدوال المحسنة - جاهز للبدء!', 'success');
            addResult('🚀 تم تحميل صفحة الاختبار');
            addResult('📋 جاهز لاختبار الدوال المحسنة');
            updateStats();

            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testFunctionExistence();
            }, 1000);
        });
    </script>
</body>
</html>
