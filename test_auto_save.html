<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحفظ التلقائي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .feature-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            background: #28a745;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e3f2fd;
        }
        
        .step-number {
            background: #1976d2;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
            font-size: 12px;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }
        
        .demo-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .demo-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .demo-table th,
        .demo-table td {
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        
        .demo-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .demo-table input {
            width: 100%;
            border: none;
            background: transparent;
            text-align: center;
            padding: 5px;
        }
        
        .demo-table input:focus {
            outline: 2px solid #007bff;
            border-radius: 3px;
        }
        
        .modified-row {
            background-color: #fff3cd !important;
        }
        
        .saved-row {
            background-color: #d4edda !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-save"></i> اختبار الحفظ التلقائي</h1>
            <p>اختبار ميزة الحفظ التلقائي الجديدة في نظام إدارة الأيام</p>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-list-ol"></i> كيفية اختبار الحفظ التلقائي:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء بيانات تجريبية</strong> - انقر على "إنشاء بيانات تجريبية"
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة السجلات</strong> - انقر على "فتح نافذة السجلات"
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>تفعيل الحفظ التلقائي</strong> - ضع علامة ✓ على "الحفظ التلقائي"
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>تجربة التعديل</strong> - عدل أي حقل وراقب الحفظ التلقائي
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button success" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>
            
            <button class="test-button warning" onclick="testAutoSaveDemo()">
                <i class="fas fa-magic"></i> عرض توضيحي للحفظ التلقائي
            </button>
            
            <button class="test-button danger" onclick="openOriginalApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
            </button>
        </div>

        <div class="feature-list">
            <h4><i class="fas fa-star"></i> ميزات الحفظ التلقائي الجديدة:</h4>
            
            <div class="feature-item">
                <div class="feature-icon">✓</div>
                <div>
                    <strong>حفظ فوري</strong>
                    <br><small>التغييرات تحفظ فور إدخالها بدون الحاجة لزر الحفظ</small>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✓</div>
                <div>
                    <strong>مؤشر بصري</strong>
                    <br><small>إشعار منبثق يظهر عند حفظ كل تغيير</small>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✓</div>
                <div>
                    <strong>تحكم مرن</strong>
                    <br><small>يمكن تفعيل أو إلغاء الحفظ التلقائي في أي وقت</small>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✓</div>
                <div>
                    <strong>أمان البيانات</strong>
                    <br><small>لا فقدان للبيانات حتى لو أُغلق المتصفح بالخطأ</small>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✓</div>
                <div>
                    <strong>تجربة محسنة</strong>
                    <br><small>واجهة أكثر سلاسة وسهولة في الاستخدام</small>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4><i class="fas fa-desktop"></i> عرض توضيحي تفاعلي:</h4>
            
            <div class="demo-controls">
                <div class="demo-checkbox">
                    <input type="checkbox" id="demoAutoSave" onchange="toggleDemoAutoSave()">
                    <label for="demoAutoSave">الحفظ التلقائي</label>
                </div>
                <div style="font-size: 12px; color: #6c757d;">
                    جرب تعديل البيانات في الجدول أدناه
                </div>
            </div>
            
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>المشروع</th>
                        <th>الأيام المحسوبة</th>
                        <th>أيام الغياب</th>
                        <th>الساعات الإضافية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr id="demo-row-1">
                        <td><input type="text" value="مشروع التطوير الأول" onchange="handleDemoChange(1, 'project', this.value)"></td>
                        <td><input type="number" value="30" onchange="handleDemoChange(1, 'days', this.value)"></td>
                        <td><input type="number" value="2" onchange="handleDemoChange(1, 'absence', this.value)"></td>
                        <td><input type="number" value="10" onchange="handleDemoChange(1, 'overtime', this.value)"></td>
                        <td id="demo-status-1">جاهز</td>
                    </tr>
                    <tr id="demo-row-2">
                        <td><input type="text" value="مشروع التطوير الثاني" onchange="handleDemoChange(2, 'project', this.value)"></td>
                        <td><input type="number" value="28" onchange="handleDemoChange(2, 'days', this.value)"></td>
                        <td><input type="number" value="1" onchange="handleDemoChange(2, 'absence', this.value)"></td>
                        <td><input type="number" value="15" onchange="handleDemoChange(2, 'overtime', this.value)"></td>
                        <td id="demo-status-2">جاهز</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار الحفظ التلقائي...</p>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let demoAutoSave = false;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 888)) {
                    employees.push({
                        id: 888,
                        name: 'أحمد محمد - اختبار الحفظ التلقائي',
                        position: 'مهندس برمجيات',
                        employeeCode: 'AUTO888',
                        employmentType: 'monthly',
                        basicSalary: 7000
                    });
                }
                
                // إنشاء سجلات أيام تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 10,
                        employeeId: 888,
                        projectName: 'مشروع اختبار الحفظ التلقائي',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 1,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 20,
                        employeeId: 888,
                        projectName: 'مشروع التطوير المتقدم',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 0,
                        overtimeHours: 12,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });
                
                // حفظ البيانات
                saveDaysDataToLocalStorage();
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح! (موظف واحد + سجلين)', 'success');
                
            } catch (error) {
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 888)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                viewDaysRecords(888);
                updateStatus('تم فتح نافذة السجلات! فعل الحفظ التلقائي وجرب التعديل', 'success');
                
            } catch (error) {
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
        }

        function testAutoSaveDemo() {
            updateStatus('عرض توضيحي للحفظ التلقائي في الجدول أدناه', 'info');
            
            // تفعيل الحفظ التلقائي في العرض التوضيحي
            const checkbox = document.getElementById('demoAutoSave');
            checkbox.checked = true;
            toggleDemoAutoSave();
        }

        function toggleDemoAutoSave() {
            const checkbox = document.getElementById('demoAutoSave');
            demoAutoSave = checkbox.checked;
            
            if (demoAutoSave) {
                updateStatus('تم تفعيل الحفظ التلقائي في العرض التوضيحي - جرب تعديل البيانات', 'success');
            } else {
                updateStatus('تم إلغاء الحفظ التلقائي في العرض التوضيحي', 'warning');
            }
        }

        function handleDemoChange(rowId, field, value) {
            const row = document.getElementById(`demo-row-${rowId}`);
            const status = document.getElementById(`demo-status-${rowId}`);
            
            if (demoAutoSave) {
                // محاكاة الحفظ التلقائي
                row.className = 'saved-row';
                status.textContent = 'تم الحفظ تلقائياً';
                status.style.color = '#28a745';
                
                // إظهار مؤشر الحفظ
                showDemoSaveIndicator();
                
                // إعادة تعيين اللون بعد ثانيتين
                setTimeout(() => {
                    row.className = '';
                    status.textContent = 'جاهز';
                    status.style.color = '';
                }, 2000);
                
            } else {
                // محاكاة الحفظ اليدوي
                row.className = 'modified-row';
                status.textContent = 'في انتظار الحفظ';
                status.style.color = '#856404';
            }
        }

        function showDemoSaveIndicator() {
            // إزالة المؤشر السابق
            const existing = document.querySelector('.demo-save-indicator');
            if (existing) existing.remove();
            
            // إنشاء مؤشر جديد
            const indicator = document.createElement('div');
            indicator.className = 'demo-save-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
                z-index: 10002;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                animation: fadeInOut 2s ease-in-out;
            `;
            
            indicator.innerHTML = '<i class="fas fa-check"></i> تم الحفظ تلقائياً (عرض توضيحي)';
            document.body.appendChild(indicator);
            
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 2000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة اختبار الحفظ التلقائي - جاهز للبدء!', 'info');
        });
    </script>
</body>
</html>
