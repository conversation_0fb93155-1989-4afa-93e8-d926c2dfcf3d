<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص عميق لمشكلة عدم تتبع التغييرات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .test-button.warning { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; }
        .test-button.danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .test-button.info { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #8e44ad;
            margin: 20px 0;
        }
        
        .status-card.success { border-left-color: #28a745; background: #d4edda; }
        .status-card.warning { border-left-color: #ffc107; background: #fff3cd; }
        .status-card.error { border-left-color: #dc3545; background: #f8d7da; }
        
        .debug-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .debug-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .debug-panel h5 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
        }
        
        .debug-value {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 5px 0;
            word-break: break-all;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .step-by-step {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }
        
        .step {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e3f2fd;
            font-size: 13px;
        }
        
        .step-number {
            background: #1976d2;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 8px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microscope"></i> تشخيص عميق لمشكلة عدم تتبع التغييرات</h1>
            <p>فحص شامل لجميع جوانب نظام تتبع التغييرات</p>
        </div>

        <div class="step-by-step">
            <h4><i class="fas fa-list-ol"></i> خطوات التشخيص العميق:</h4>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>فحص DOM:</strong> التأكد من وجود الحقول وأحداث onchange
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>مراقبة الأحداث:</strong> تتبع استدعاء updateRecordField
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>فحص البيانات:</strong> التحقق من حالة recordsChanges
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>اختبار يدوي:</strong> محاكاة التعديل خطوة بخطوة
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="test-button danger" onclick="runDeepDiagnostic()">
                <i class="fas fa-search"></i> تشخيص عميق شامل
            </button>
            
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات
            </button>
            
            <button class="test-button warning" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح النافذة
            </button>
            
            <button class="test-button info" onclick="inspectDOM()">
                <i class="fas fa-code"></i> فحص DOM
            </button>
            
            <button class="test-button success" onclick="manualTest()">
                <i class="fas fa-hand-pointer"></i> اختبار يدوي
            </button>
        </div>

        <div class="debug-grid">
            <div class="debug-panel">
                <h5><i class="fas fa-database"></i> حالة البيانات</h5>
                <div class="debug-value" id="recordsChangesState">recordsChanges: {}</div>
                <div class="debug-value" id="autoSaveState">autoSaveEnabled: false</div>
                <div class="debug-value" id="employeeDataCount">employeeDaysData: 0 سجل</div>
            </div>
            
            <div class="debug-panel">
                <h5><i class="fas fa-cogs"></i> حالة الدوال</h5>
                <div class="debug-value" id="updateRecordFieldStatus">updateRecordField: غير محدد</div>
                <div class="debug-value" id="saveAllChangesStatus">saveAllRecordsChanges: غير محدد</div>
                <div class="debug-value" id="updateSaveButtonStatus">updateSaveButton: غير محدد</div>
            </div>
            
            <div class="debug-panel">
                <h5><i class="fas fa-window-maximize"></i> حالة النافذة</h5>
                <div class="debug-value" id="modalStatus">النافذة: مغلقة</div>
                <div class="debug-value" id="saveButtonStatus">زر الحفظ: غير موجود</div>
                <div class="debug-value" id="inputFieldsCount">حقول الإدخال: 0</div>
            </div>
            
            <div class="debug-panel">
                <h5><i class="fas fa-mouse-pointer"></i> آخر حدث</h5>
                <div class="debug-value" id="lastEvent">لا توجد أحداث</div>
                <div class="debug-value" id="lastChange">لا توجد تغييرات</div>
                <div class="debug-value" id="lastError">لا توجد أخطاء</div>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء التشخيص العميق...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [DEEP_DEBUG] مراقب التشخيص العميق جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let originalUpdateRecordField = null;
        let eventCounter = 0;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function updateDebugPanels() {
            // حالة البيانات
            document.getElementById('recordsChangesState').textContent = 
                `recordsChanges: ${JSON.stringify(recordsChanges || {})}`;
            document.getElementById('autoSaveState').textContent = 
                `autoSaveEnabled: ${autoSaveEnabled || false}`;
            document.getElementById('employeeDataCount').textContent = 
                `employeeDaysData: ${(employeeDaysData || []).length} سجل`;
            
            // حالة الدوال
            document.getElementById('updateRecordFieldStatus').textContent = 
                `updateRecordField: ${typeof updateRecordField === 'function' ? 'موجودة' : 'مفقودة'}`;
            document.getElementById('saveAllChangesStatus').textContent = 
                `saveAllRecordsChanges: ${typeof saveAllRecordsChanges === 'function' ? 'موجودة' : 'مفقودة'}`;
            document.getElementById('updateSaveButtonStatus').textContent = 
                `updateSaveButton: ${typeof updateSaveButton === 'function' ? 'موجودة' : 'مفقودة'}`;
            
            // حالة النافذة
            const modal = document.getElementById('daysRecordsModal');
            const saveBtn = modal ? modal.querySelector('#saveChangesBtn') : null;
            const inputFields = modal ? modal.querySelectorAll('input[onchange*="updateRecordField"]') : [];
            
            document.getElementById('modalStatus').textContent = 
                `النافذة: ${modal ? 'مفتوحة' : 'مغلقة'}`;
            document.getElementById('saveButtonStatus').textContent = 
                `زر الحفظ: ${saveBtn ? 'موجود' : 'غير موجود'}`;
            document.getElementById('inputFieldsCount').textContent = 
                `حقول الإدخال: ${inputFields.length}`;
        }

        function createTestData() {
            addLog('🔧 إنشاء بيانات تجريبية للتشخيص العميق...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 333)) {
                    employees.push({
                        id: 333,
                        name: 'سارة محمد - تشخيص عميق',
                        position: 'محللة نظم',
                        employeeCode: 'DEEP333',
                        employmentType: 'monthly',
                        basicSalary: 6200
                    });
                    addLog('✓ تم إنشاء موظف تجريبي');
                }
                
                // إنشاء سجل أيام تجريبي
                const testRecord = {
                    id: Date.now() + 3000,
                    employeeId: 333,
                    projectName: 'مشروع التشخيص العميق',
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: 30,
                    absenceDays: 2,
                    overtimeHours: 8,
                    createdAt: new Date().toISOString()
                };
                
                if (!employeeDaysData.find(r => r.id === testRecord.id)) {
                    employeeDaysData.push(testRecord);
                }
                
                saveDaysDataToLocalStorage();
                addLog('✓ تم حفظ البيانات التجريبية');
                updateDebugPanels();
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 333)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة السجلات...');
                viewDaysRecords(333);
                
                setTimeout(() => {
                    updateDebugPanels();
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        addLog('✓ تم فتح نافذة السجلات بنجاح');
                        updateStatus('تم فتح نافذة السجلات!', 'success');
                    } else {
                        addLog('✗ فشل في فتح نافذة السجلات');
                        updateStatus('فشل في فتح نافذة السجلات', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function inspectDOM() {
            addLog('🔍 فحص DOM للنافذة...');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                addLog('❌ النافذة غير مفتوحة');
                updateStatus('يرجى فتح نافذة السجلات أولاً', 'error');
                return;
            }
            
            // فحص حقول الإدخال
            const inputFields = modal.querySelectorAll('input');
            addLog(`📊 تم العثور على ${inputFields.length} حقل إدخال`);
            
            inputFields.forEach((input, index) => {
                const onchange = input.getAttribute('onchange');
                addLog(`حقل ${index + 1}: ${input.type} - onchange: ${onchange || 'غير موجود'}`);
            });
            
            // فحص زر الحفظ
            const saveBtn = modal.querySelector('#saveChangesBtn');
            if (saveBtn) {
                const onclick = saveBtn.getAttribute('onclick');
                addLog(`✓ زر الحفظ موجود - onclick: ${onclick}`);
            } else {
                addLog('❌ زر الحفظ غير موجود');
            }
            
            // فحص الجدول
            const table = modal.querySelector('#recordsTable');
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                addLog(`📋 الجدول يحتوي على ${rows.length} صف`);
            } else {
                addLog('❌ الجدول غير موجود');
            }
            
            updateDebugPanels();
            updateStatus('تم فحص DOM - راجع console للتفاصيل', 'info');
        }

        function manualTest() {
            addLog('🧪 بدء الاختبار اليدوي...');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                addLog('❌ النافذة غير مفتوحة');
                updateStatus('يرجى فتح نافذة السجلات أولاً', 'error');
                return;
            }
            
            // البحث عن أول حقل إدخال
            const firstInput = modal.querySelector('input[onchange*="updateRecordField"]');
            if (!firstInput) {
                addLog('❌ لم يتم العثور على حقول إدخال مع updateRecordField');
                updateStatus('لم يتم العثور على حقول إدخال صالحة', 'error');
                return;
            }
            
            addLog('✓ تم العثور على حقل إدخال صالح');
            
            // محاكاة التعديل
            const originalValue = firstInput.value;
            const newValue = originalValue + ' - معدل';
            
            addLog(`🔄 تغيير القيمة من "${originalValue}" إلى "${newValue}"`);
            
            // تغيير القيمة وإطلاق حدث change
            firstInput.value = newValue;
            firstInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            // فحص النتيجة
            setTimeout(() => {
                updateDebugPanels();
                const changesCount = Object.keys(recordsChanges || {}).length;
                
                if (changesCount > 0) {
                    addLog(`✅ تم تسجيل ${changesCount} تغيير بنجاح`);
                    updateStatus('✅ الاختبار اليدوي نجح - تم تسجيل التغيير!', 'success');
                } else {
                    addLog('❌ لم يتم تسجيل أي تغيير');
                    updateStatus('❌ الاختبار اليدوي فشل - لم يتم تسجيل التغيير', 'error');
                }
                
                document.getElementById('lastChange').textContent = 
                    `آخر تغيير: ${newValue}`;
            }, 500);
        }

        function runDeepDiagnostic() {
            updateStatus('🔍 بدء التشخيص العميق الشامل...', 'warning');
            addLog('=== بدء التشخيص العميق الشامل ===');
            
            // خطوة 1: إنشاء البيانات
            setTimeout(() => {
                createTestData();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openRecordsWindow();
            }, 2000);
            
            // خطوة 3: فحص DOM
            setTimeout(() => {
                inspectDOM();
            }, 3500);
            
            // خطوة 4: الاختبار اليدوي
            setTimeout(() => {
                manualTest();
            }, 5000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء التشخيص العميق ===');
                updateDebugPanels();
                
                const changesCount = Object.keys(recordsChanges || {}).length;
                if (changesCount > 0) {
                    addLog('🎉 التشخيص: النظام يعمل بشكل صحيح');
                    updateStatus('🎉 التشخيص مكتمل - النظام يعمل بشكل صحيح!', 'success');
                } else {
                    addLog('⚠️ التشخيص: هناك مشكلة في تتبع التغييرات');
                    updateStatus('⚠️ التشخيص مكتمل - هناك مشكلة تحتاج إصلاح', 'warning');
                }
            }, 7000);
        }

        // مراقبة دالة updateRecordField
        function interceptUpdateRecordField() {
            if (typeof updateRecordField === 'function' && !originalUpdateRecordField) {
                originalUpdateRecordField = updateRecordField;
                
                window.updateRecordField = function(recordId, fieldName, newValue) {
                    eventCounter++;
                    addLog(`🔔 حدث ${eventCounter}: استدعاء updateRecordField(${recordId}, "${fieldName}", "${newValue}")`);
                    document.getElementById('lastEvent').textContent = 
                        `حدث ${eventCounter}: updateRecordField استدعيت`;
                    
                    try {
                        const result = originalUpdateRecordField.apply(this, arguments);
                        addLog(`✅ تم تنفيذ updateRecordField بنجاح`);
                        updateDebugPanels();
                        return result;
                    } catch (error) {
                        addLog(`❌ خطأ في updateRecordField: ${error.message}`);
                        document.getElementById('lastError').textContent = 
                            `خطأ: ${error.message}`;
                        throw error;
                    }
                };
                
                addLog('✓ تم تفعيل مراقبة updateRecordField');
            }
        }

        // تحديث دوري للوحات التشخيص
        setInterval(updateDebugPanels, 2000);

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل أداة التشخيص العميق - جاهز للبدء!', 'info');
            addLog('تم تحميل أداة التشخيص العميق');
            updateDebugPanels();
            interceptUpdateRecordField();
        });
    </script>
</body>
</html>
