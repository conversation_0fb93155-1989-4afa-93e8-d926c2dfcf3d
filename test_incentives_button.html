<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .employee-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.2s;
        }
        .incentives-btn {
            background-color: #FFD700;
            color: #333;
        }
        .incentives-btn:hover {
            background-color: #FFC107;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار زر الحوافز والمكافآت</h1>
        
        <div class="employee-card">
            <h3>موظف تجريبي</h3>
            <p>الكود: EMP001</p>
            
            <div class="action-buttons">
                <button class="action-btn incentives-btn" onclick="testIncentivesButton()" title="إدارة الحوافز والمكافآت">
                    <i class="fas fa-trophy"></i>
                    <span>حوافز ومكافآت</span>
                </button>
            </div>
        </div>
        
        <div id="testResult" class="test-result" style="display: none;"></div>
        
        <h3>تعليمات الاختبار:</h3>
        <ol>
            <li>انقر على زر "حوافز ومكافآت" أعلاه</li>
            <li>إذا فتحت نافذة الحوافز والمكافآت، فالاختبار نجح</li>
            <li>إذا ظهرت رسالة خطأ، فهناك مشكلة في الكود</li>
        </ol>
    </div>

    <script>
        // بيانات تجريبية
        var employees = [
            {
                id: 1,
                name: "موظف تجريبي",
                employeeCode: "EMP001"
            }
        ];
        
        var incentivesData = [];
        var customIncentiveTypes = [];
        
        function testIncentivesButton() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            
            try {
                // محاولة استدعاء دالة فتح نافذة الحوافز
                if (typeof openIncentivesModal === 'function') {
                    openIncentivesModal(1);
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ نجح الاختبار! تم استدعاء دالة openIncentivesModal بنجاح';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ فشل الاختبار! دالة openIncentivesModal غير موجودة';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ فشل الاختبار! خطأ: ${error.message}`;
            }
        }
        
        // تحميل ملف app.js للاختبار
        const script = document.createElement('script');
        script.src = 'app.js';
        script.onload = function() {
            console.log('تم تحميل app.js بنجاح');
        };
        script.onerror = function() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result error';
            resultDiv.innerHTML = '❌ فشل في تحميل ملف app.js';
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
