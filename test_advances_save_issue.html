<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة حفظ السلف والخصومات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545, #c82333);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 26px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: calc(50% - 20px);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص مشكلة حفظ السلف والخصومات</h1>
            <p>فحص شامل لمعرفة سبب عدم حفظ السلف والخصومات</p>
        </div>

        <div class="status" id="status">
            جاهز لتشخيص مشكلة الحفظ...
        </div>

        <div class="content">
            <div class="grid">
                <button class="test-button" onclick="testSystemLoad()">
                    <i class="fas fa-search"></i> فحص تحميل النظام
                </button>
                
                <button class="test-button success" onclick="testAdvancesFunctions()">
                    <i class="fas fa-cogs"></i> فحص دوال السلف
                </button>
                
                <button class="test-button warning" onclick="testSaveProcess()">
                    <i class="fas fa-save"></i> اختبار عملية الحفظ
                </button>
                
                <button class="test-button danger" onclick="testLocalStorage()">
                    <i class="fas fa-database"></i> فحص localStorage
                </button>
            </div>

            <div class="grid">
                <button class="test-button" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                
                <button class="test-button" onclick="testAdvancesWindow()">
                    <i class="fas fa-window-maximize"></i> اختبار نافذة السلف
                </button>
                
                <button class="test-button" onclick="manualSaveTest()">
                    <i class="fas fa-hand-paper"></i> اختبار حفظ يدوي
                </button>
                
                <button class="test-button" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#333';
            if (type === 'success') color = '#28a745';
            else if (type === 'error') color = '#dc3545';
            else if (type === 'warning') color = '#ffc107';
            else if (type === 'info') color = '#17a2b8';
            
            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // فحص تحميل النظام
        function testSystemLoad() {
            updateStatus('جاري فحص تحميل النظام...', 'warning');
            addResult('🔍 بدء فحص تحميل النظام', 'info');
            
            // فحص المتغيرات الأساسية
            if (typeof employees !== 'undefined') {
                addResult(`✅ متغير employees: موجود (${employees.length} موظف)`, 'success');
            } else {
                addResult('❌ متغير employees: مفقود', 'error');
                updateStatus('❌ النظام غير محمل', 'error');
                return;
            }
            
            if (typeof advancesDeductionsData !== 'undefined') {
                addResult(`✅ متغير advancesDeductionsData: موجود (${advancesDeductionsData.length} سجل)`, 'success');
            } else {
                addResult('❌ متغير advancesDeductionsData: مفقود', 'error');
            }
            
            updateStatus('✅ تم فحص تحميل النظام', 'success');
        }

        // فحص دوال السلف
        function testAdvancesFunctions() {
            updateStatus('جاري فحص دوال السلف...', 'warning');
            addResult('🔧 بدء فحص دوال السلف والخصومات', 'info');
            
            const functions = [
                'viewAdvancesDeductions',
                'saveAdvanceDeduction',
                'deleteAdvanceDeduction',
                'closeAdvancesDeductionsModal',
                'loadAdvancesDeductionsFromLocalStorage',
                'saveAdvancesDeductionsToLocalStorage'
            ];
            
            let functionsFound = 0;
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });
            
            if (functionsFound === functions.length) {
                updateStatus('✅ جميع دوال السلف موجودة', 'success');
            } else {
                updateStatus(`❌ ${functions.length - functionsFound} دالة مفقودة`, 'error');
            }
        }

        // اختبار عملية الحفظ
        function testSaveProcess() {
            updateStatus('جاري اختبار عملية الحفظ...', 'warning');
            addResult('💾 بدء اختبار عملية الحفظ', 'info');
            
            try {
                // إنشاء سجل تجريبي
                const testRecord = {
                    id: Date.now(),
                    employeeId: 99999,
                    type: 'advance',
                    month: 12,
                    year: 2024,
                    amount: 1000,
                    description: 'اختبار الحفظ',
                    date: new Date().toISOString()
                };
                
                // إضافة السجل
                if (typeof advancesDeductionsData !== 'undefined') {
                    const beforeCount = advancesDeductionsData.length;
                    advancesDeductionsData.push(testRecord);
                    const afterCount = advancesDeductionsData.length;
                    
                    if (afterCount > beforeCount) {
                        addResult('✅ تم إضافة السجل إلى المصفوفة', 'success');
                    } else {
                        addResult('❌ فشل في إضافة السجل إلى المصفوفة', 'error');
                    }
                    
                    // اختبار الحفظ في localStorage
                    if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                        saveAdvancesDeductionsToLocalStorage();
                        addResult('✅ تم استدعاء دالة الحفظ', 'success');
                        
                        // التحقق من الحفظ
                        const saved = localStorage.getItem('oscoAdvancesDeductions');
                        if (saved) {
                            const parsed = JSON.parse(saved);
                            const found = parsed.find(r => r.id === testRecord.id);
                            if (found) {
                                addResult('✅ تم حفظ السجل في localStorage', 'success');
                                updateStatus('✅ عملية الحفظ تعمل بشكل صحيح', 'success');
                            } else {
                                addResult('❌ السجل غير موجود في localStorage', 'error');
                                updateStatus('❌ مشكلة في حفظ localStorage', 'error');
                            }
                        } else {
                            addResult('❌ لا توجد بيانات في localStorage', 'error');
                            updateStatus('❌ فشل في الحفظ', 'error');
                        }
                    } else {
                        addResult('❌ دالة الحفظ غير موجودة', 'error');
                        updateStatus('❌ دالة الحفظ مفقودة', 'error');
                    }
                } else {
                    addResult('❌ متغير البيانات غير موجود', 'error');
                    updateStatus('❌ متغير البيانات مفقود', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار الحفظ: ${error.message}`, 'error');
                updateStatus('❌ فشل اختبار الحفظ', 'error');
            }
        }

        // فحص localStorage
        function testLocalStorage() {
            updateStatus('جاري فحص localStorage...', 'warning');
            addResult('🗄️ بدء فحص localStorage', 'info');
            
            try {
                // اختبار كتابة وقراءة
                const testKey = 'test_advances_save';
                const testValue = { test: 'data', timestamp: Date.now() };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = localStorage.getItem(testKey);
                
                if (retrieved) {
                    const parsed = JSON.parse(retrieved);
                    if (parsed.test === 'data') {
                        addResult('✅ localStorage يعمل بشكل صحيح', 'success');
                        localStorage.removeItem(testKey);
                    } else {
                        addResult('❌ مشكلة في تحليل البيانات', 'error');
                    }
                } else {
                    addResult('❌ فشل في قراءة localStorage', 'error');
                }
                
                // فحص البيانات الموجودة
                const existingData = localStorage.getItem('oscoAdvancesDeductions');
                if (existingData) {
                    const parsed = JSON.parse(existingData);
                    addResult(`📊 البيانات الموجودة: ${parsed.length} سجل`, 'info');
                } else {
                    addResult('📊 لا توجد بيانات سلف محفوظة', 'warning');
                }
                
                updateStatus('✅ تم فحص localStorage', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في localStorage: ${error.message}`, 'error');
                updateStatus('❌ مشكلة في localStorage', 'error');
            }
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('👤 إنشاء موظف تجريبي...', 'info');
            
            const testEmployee = {
                id: 88888,
                name: "موظف تجريبي للسلف",
                position: "مختبر السلف",
                employeeCode: "ADV_TEST",
                nationalId: "12345678901234",
                phone: "01000000000",
                photo: "https://via.placeholder.com/150/dc3545/ffffff?text=ADV",
                employmentType: "monthly",
                salary: { basic: 5000, total: 5000 },
                dateAdded: new Date().toISOString()
            };
            
            const existing = employees.find(emp => emp.id === 88888);
            if (!existing) {
                employees.push(testEmployee);
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                addResult('✅ تم إنشاء الموظف التجريبي', 'success');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            } else {
                addResult('⚠️ الموظف التجريبي موجود بالفعل', 'warning');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            }
        }

        // اختبار نافذة السلف
        function testAdvancesWindow() {
            addResult('🪟 اختبار فتح نافذة السلف...', 'info');
            
            try {
                if (typeof viewAdvancesDeductions !== 'function') {
                    addResult('❌ دالة فتح النافذة غير موجودة', 'error');
                    updateStatus('❌ دالة النافذة مفقودة', 'error');
                    return;
                }
                
                // التحقق من وجود موظف تجريبي
                const testEmployee = employees.find(emp => emp.id === 88888);
                if (!testEmployee) {
                    addResult('⚠️ يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return;
                }
                
                viewAdvancesDeductions(88888);
                addResult('✅ تم فتح نافذة السلف والخصومات', 'success');
                updateStatus('✅ النافذة مفتوحة - جرب إضافة سلفة', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // اختبار حفظ يدوي
        function manualSaveTest() {
            addResult('✋ اختبار الحفظ اليدوي...', 'info');
            
            try {
                // محاكاة عملية الحفظ
                const testData = {
                    id: Date.now(),
                    employeeId: 88888,
                    type: 'advance',
                    month: 12,
                    year: 2024,
                    amount: 500,
                    description: 'اختبار يدوي',
                    date: new Date().toISOString()
                };
                
                if (typeof saveAdvanceDeduction === 'function') {
                    // محاكاة ملء النموذج
                    document.body.innerHTML += `
                        <div style="display: none;">
                            <select id="advanceDeductionType"><option value="advance" selected>سلفة</option></select>
                            <input id="advanceDeductionAmount" value="500">
                            <input id="advanceDeductionDescription" value="اختبار يدوي">
                            <input id="advanceDeductionMonth" value="12">
                            <input id="advanceDeductionYear" value="2024">
                        </div>
                    `;
                    
                    saveAdvanceDeduction(88888);
                    addResult('✅ تم استدعاء دالة الحفظ اليدوي', 'success');
                    updateStatus('✅ تم اختبار الحفظ اليدوي', 'success');
                } else {
                    addResult('❌ دالة الحفظ غير موجودة', 'error');
                    updateStatus('❌ دالة الحفظ مفقودة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الحفظ اليدوي: ${error.message}`, 'error');
                updateStatus('❌ فشل الحفظ اليدوي', 'error');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 بدء الفحص التلقائي...', 'info');
                testSystemLoad();
                
                setTimeout(() => {
                    testAdvancesFunctions();
                }, 1000);
                
                setTimeout(() => {
                    testLocalStorage();
                }, 2000);
                
            }, 1000);
        });
    </script>
</body>
</html>
