<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح البدلات في حساب المرتبات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 14px;
        }

        .result-item.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-item.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result-item.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .result-item.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-money-check-alt"></i> اختبار إصلاح البدلات في حساب المرتبات</h1>
            <p>اختبار شامل للتأكد من ظهور البدلات اليومية في نافذة حساب الراتب</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> إعداد البيانات التجريبية</h3>
            <button class="test-button" onclick="setupTestData()">
                <i class="fas fa-database"></i> إنشاء بيانات تجريبية
            </button>
            <button class="test-button success" onclick="addDailyAllowancesToAll()">
                <i class="fas fa-plus-circle"></i> إضافة البدلات اليومية لجميع الموظفين
            </button>
            <button class="test-button warning" onclick="checkEmployeeAllowances()">
                <i class="fas fa-search"></i> فحص البدلات الموجودة
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-calculator"></i> اختبار حساب الراتب</h3>
            <button class="test-button" onclick="testSalaryCalculation()">
                <i class="fas fa-play"></i> اختبار حساب الراتب مع البدلات
            </button>
            <button class="test-button success" onclick="testWithRealData()">
                <i class="fas fa-chart-line"></i> اختبار مع بيانات حقيقية
            </button>
            <button class="test-button warning" onclick="testWithoutDaysData()">
                <i class="fas fa-exclamation-triangle"></i> اختبار بدون بيانات أيام
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-tools"></i> أدوات التشخيص</h3>
            <button class="test-button danger" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
            <button class="test-button" onclick="openOriginalApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
            </button>
        </div>

        <div id="status" class="status">جاهز لبدء الاختبار...</div>
        <div id="results" class="results"></div>
    </div>

    <!-- تحميل ملف التطبيق الأصلي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `<i class="fas fa-${getIcon(type)}"></i> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }

        // دالة للحصول على الأيقونة المناسبة
        function getIcon(type) {
            switch(type) {
                case 'success': return 'check-circle';
                case 'error': return 'times-circle';
                case 'warning': return 'exclamation-triangle';
                default: return 'info-circle';
            }
        }

        // دالة لإنشاء بيانات تجريبية
        function setupTestData() {
            addResult('🔧 بدء إنشاء البيانات التجريبية...', 'info');
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');

            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 12345,
                    name: "أحمد محمد علي",
                    position: "مهندس برمجيات",
                    employeeCode: "EMP12345",
                    nationalId: "12345678901234",
                    phone: "01234567890",
                    photo: "https://via.placeholder.com/150/007bff/ffffff?text=TEST",
                    employmentType: "monthly",
                    dateAdded: new Date().toISOString(),
                    salary: {
                        basic: 8000,
                        total: 8000,
                        allowances: {
                            housing: 500,
                            transport: 300,
                            expat: 0,
                            meal: 200,
                            other: 0,
                            total: 1000
                        },
                        dailyAllowances: {
                            expatDaily: 50,
                            transportDaily: 30
                        }
                    }
                };

                // إضافة الموظف إذا لم يكن موجوداً
                if (!employees.find(emp => emp.id === 12345)) {
                    employees.push(testEmployee);
                    saveEmployeesToLocalStorage();
                    addResult('✅ تم إنشاء موظف تجريبي بنجاح', 'success');
                } else {
                    addResult('ℹ️ الموظف التجريبي موجود بالفعل', 'info');
                }

                // إنشاء بيانات أيام عمل تجريبية
                const testDaysData = {
                    id: Date.now(),
                    employeeId: 12345,
                    projectName: "مشروع اختبار البدلات",
                    startDate: "2024-01-01",
                    endDate: "2024-01-31",
                    calculatedDays: 25,
                    absenceDays: 3,
                    actualDays: 22,
                    overtimeHours: 16,
                    createdAt: new Date().toISOString()
                };

                // إضافة بيانات الأيام
                if (!employeeDaysData.find(record => record.employeeId === 12345)) {
                    employeeDaysData.push(testDaysData);
                    saveDaysDataToLocalStorage();
                    addResult('✅ تم إنشاء بيانات أيام العمل التجريبية', 'success');
                } else {
                    addResult('ℹ️ بيانات أيام العمل موجودة بالفعل', 'info');
                }

                updateStatus('✅ تم إنشاء البيانات التجريبية بنجاح', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء البيانات التجريبية: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء البيانات التجريبية', 'error');
            }
        }

        // دالة لإضافة البدلات اليومية لجميع الموظفين
        function addDailyAllowancesToAll() {
            addResult('🔧 إضافة البدلات اليومية لجميع الموظفين...', 'info');
            updateStatus('جاري إضافة البدلات اليومية...', 'warning');

            try {
                const result = addDailyAllowancesToAllEmployees();
                if (result > 0) {
                    addResult(`✅ تم إضافة البدلات اليومية لـ ${result} موظف`, 'success');
                    updateStatus(`✅ تم تحديث ${result} موظف`, 'success');
                } else {
                    addResult('ℹ️ جميع الموظفين لديهم بدلات يومية بالفعل', 'info');
                    updateStatus('ℹ️ لا حاجة لتحديث', 'info');
                }
            } catch (error) {
                addResult(`❌ خطأ في إضافة البدلات: ${error.message}`, 'error');
                updateStatus('❌ فشل في إضافة البدلات', 'error');
            }
        }

        // دالة لفحص البدلات الموجودة
        function checkEmployeeAllowances() {
            addResult('🔍 فحص البدلات اليومية للموظفين...', 'info');
            updateStatus('جاري فحص البدلات...', 'warning');

            try {
                let employeesWithAllowances = 0;
                let employeesWithoutAllowances = 0;

                employees.forEach(employee => {
                    if (employee.salary && employee.salary.dailyAllowances) {
                        employeesWithAllowances++;
                        addResult(`✅ ${employee.name}: بدل اغتراب ${employee.salary.dailyAllowances.expatDaily} ج.م، بدل انتقالات ${employee.salary.dailyAllowances.transportDaily} ج.م`, 'success');
                    } else {
                        employeesWithoutAllowances++;
                        addResult(`❌ ${employee.name}: لا توجد بدلات يومية`, 'error');
                    }
                });

                addResult(`📊 الإحصائيات: ${employeesWithAllowances} موظف لديه بدلات، ${employeesWithoutAllowances} موظف بدون بدلات`, 'info');
                updateStatus(`✅ تم فحص ${employees.length} موظف`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في فحص البدلات: ${error.message}`, 'error');
                updateStatus('❌ فشل في فحص البدلات', 'error');
            }
        }

        // دالة لاختبار حساب الراتب
        function testSalaryCalculation() {
            addResult('🧮 اختبار حساب الراتب مع البدلات...', 'info');
            updateStatus('جاري اختبار حساب الراتب...', 'warning');

            try {
                // البحث عن الموظف التجريبي
                const testEmployee = employees.find(emp => emp.id === 12345);
                if (!testEmployee) {
                    addResult('❌ لم يتم العثور على الموظف التجريبي. يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    updateStatus('❌ الموظف التجريبي غير موجود', 'error');
                    return;
                }

                addResult(`✅ تم العثور على الموظف: ${testEmployee.name}`, 'success');

                // فتح نافذة حساب الراتب
                openSalaryCalculator(12345);
                addResult('✅ تم فتح نافذة حساب الراتب', 'success');
                addResult('💡 تحقق من النافذة المفتوحة - يجب أن تظهر البدلات اليومية بوضوح', 'info');
                updateStatus('✅ تم فتح نافذة حساب الراتب', 'success');

            } catch (error) {
                addResult(`❌ خطأ في اختبار حساب الراتب: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار حساب الراتب', 'error');
            }
        }

        // دالة لاختبار مع بيانات حقيقية
        function testWithRealData() {
            addResult('📊 اختبار مع بيانات حقيقية...', 'info');
            updateStatus('جاري اختبار البيانات الحقيقية...', 'warning');

            try {
                if (employees.length === 0) {
                    addResult('❌ لا يوجد موظفين في النظام', 'error');
                    updateStatus('❌ لا يوجد موظفين', 'error');
                    return;
                }

                // اختبار أول موظف في النظام
                const firstEmployee = employees[0];
                addResult(`🧪 اختبار الموظف: ${firstEmployee.name}`, 'info');

                // التحقق من وجود البدلات اليومية
                if (firstEmployee.salary && firstEmployee.salary.dailyAllowances) {
                    addResult('✅ الموظف لديه بدلات يومية', 'success');
                } else {
                    addResult('⚠️ الموظف لا يملك بدلات يومية - سيتم إضافتها', 'warning');
                    updateEmployeeDailyAllowances(firstEmployee.id, 50, 30);
                }

                // فتح نافذة حساب الراتب
                openSalaryCalculator(firstEmployee.id);
                addResult('✅ تم فتح نافذة حساب الراتب للموظف الحقيقي', 'success');
                updateStatus('✅ تم اختبار البيانات الحقيقية', 'success');

            } catch (error) {
                addResult(`❌ خطأ في اختبار البيانات الحقيقية: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار البيانات الحقيقية', 'error');
            }
        }

        // دالة لاختبار بدون بيانات أيام
        function testWithoutDaysData() {
            addResult('⚠️ اختبار بدون بيانات أيام العمل...', 'warning');
            updateStatus('جاري اختبار بدون بيانات أيام...', 'warning');

            try {
                // إنشاء موظف بدون بيانات أيام
                const testEmployeeNoDays = {
                    id: 99999,
                    name: "موظف بدون أيام عمل",
                    position: "اختبار",
                    employeeCode: "NODAYS",
                    salary: {
                        basic: 5000,
                        dailyAllowances: {
                            expatDaily: 40,
                            transportDaily: 25
                        }
                    }
                };

                // إضافة الموظف مؤقتاً
                if (!employees.find(emp => emp.id === 99999)) {
                    employees.push(testEmployeeNoDays);
                }

                // فتح نافذة حساب الراتب
                openSalaryCalculator(99999);
                addResult('✅ تم فتح نافذة حساب الراتب بدون بيانات أيام', 'success');
                addResult('💡 يجب أن تظهر البيانات الافتراضية مع البدلات', 'info');
                updateStatus('✅ تم اختبار بدون بيانات أيام', 'success');

            } catch (error) {
                addResult(`❌ خطأ في اختبار بدون بيانات أيام: ${error.message}`, 'error');
                updateStatus('❌ فشل في اختبار بدون بيانات أيام', 'error');
            }
        }

        // دالة لمسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // دالة لفتح التطبيق الأصلي
        function openOriginalApp() {
            window.open('index.html', '_blank');
            addResult('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في اختبار إصلاح البدلات', 'info');
                addResult('💡 تم إصلاح مشكلة عدم ظهور البدلات في حساب المرتبات', 'success');
                addResult('🔧 الآن البدلات تُحسب من بيانات الموظف الفعلية وأيام الحضور', 'info');
                updateStatus('✅ النظام جاهز للاختبار', 'success');
            }, 500);
        });
    </script>
</body>
</html>
