<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مضمون - السلف والخصومات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #27ae60;
        }

        .status.error {
            background: #e74c3c;
        }

        .status.warning {
            background: #f39c12;
            color: #333;
        }

        .step {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .step h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .step p {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> اختبار مضمون للسلف والخصومات</h1>
            <p>حل نهائي ومضمون لمشكلة عدم الحفظ</p>
        </div>

        <div class="status" id="status">
            جاهز للاختبار المضمون...
        </div>

        <div class="content">
            <div class="step">
                <h3>📋 الخطوات المطلوبة:</h3>
                <p>1. إنشاء موظف تجريبي<br>
                2. فتح نافذة السلف والخصومات<br>
                3. استخدام زر "حفظ مضمون" الأخضر<br>
                4. التحقق من حفظ البيانات</p>
            </div>

            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> 1. إنشاء موظف تجريبي
            </button>

            <button class="test-button success" onclick="openAdvancesWindow()">
                <i class="fas fa-window-maximize"></i> 2. فتح نافذة السلف
            </button>

            <button class="test-button warning" onclick="testDirectSave()">
                <i class="fas fa-bolt"></i> 3. اختبار الحفظ المباشر
            </button>

            <button class="test-button danger" onclick="checkSavedData()">
                <i class="fas fa-database"></i> 4. فحص البيانات المحفوظة
            </button>

            <div style="margin: 20px 0; padding: 15px; background: #d1ecf1; border-radius: 8px; border: 1px solid #bee5eb;">
                <h4 style="color: #0c5460; margin-bottom: 10px;">💡 تعليمات مهمة:</h4>
                <p style="color: #0c5460; margin: 0; line-height: 1.5;">
                    • استخدم زر <strong>"💾 حفظ مضمون"</strong> الأخضر في النافذة<br>
                    • تجنب زر "🔄 حفظ عادي" الأزرق إذا كان لا يعمل<br>
                    • ستظهر رسالة تأكيد عند نجاح الحفظ
                </p>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#333';
            if (type === 'success') color = '#27ae60';
            else if (type === 'error') color = '#e74c3c';
            else if (type === 'warning') color = '#f39c12';
            else if (type === 'info') color = '#3498db';
            
            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('👤 إنشاء موظف تجريبي للاختبار المضمون', 'info');
            
            try {
                const testEmployee = {
                    id: 55555,
                    name: "موظف الاختبار المضمون",
                    position: "مختبر الحفظ المضمون",
                    employeeCode: "GUARANTEED",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/27ae60/ffffff?text=SAVE",
                    employmentType: "monthly",
                    salary: { basic: 5000, total: 5000 },
                    dateAdded: new Date().toISOString()
                };
                
                // إزالة الموظف إذا كان موجود
                const existingIndex = employees.findIndex(emp => emp.id === 55555);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    addResult('🗑️ تم إزالة الموظف الموجود', 'warning');
                }
                
                employees.push(testEmployee);
                
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                
                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
                
                // التأكد من إنشاء مصفوفة السلف
                if (!window.advancesDeductionsData) {
                    window.advancesDeductionsData = [];
                    addResult('📋 تم إنشاء مصفوفة السلف والخصومات', 'info');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف', 'error');
            }
        }

        // فتح نافذة السلف
        function openAdvancesWindow() {
            updateStatus('جاري فتح نافذة السلف...', 'warning');
            addResult('🪟 فتح نافذة السلف والخصومات', 'info');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 55555);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // فتح النافذة
                viewAdvancesDeductions(55555);
                addResult('✅ تم فتح نافذة السلف والخصومات', 'success');
                
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        addResult('🎉 النافذة مفتوحة! استخدم زر "💾 حفظ مضمون" الأخضر', 'success');
                        updateStatus('✅ النافذة مفتوحة - جرب الحفظ المضمون', 'success');
                    } else {
                        addResult('❌ النافذة لم تظهر', 'error');
                        updateStatus('❌ مشكلة في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // اختبار الحفظ المباشر
        function testDirectSave() {
            updateStatus('جاري اختبار الحفظ المباشر...', 'warning');
            addResult('⚡ اختبار الحفظ المباشر', 'info');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 55555);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // إنشاء عناصر النموذج مؤقتاً
                const tempForm = document.createElement('div');
                tempForm.style.display = 'none';
                tempForm.innerHTML = `
                    <select id="advanceDeductionType">
                        <option value="advance" selected>سلفة</option>
                    </select>
                    <input id="advanceDeductionAmount" value="2500">
                    <input id="advanceDeductionDescription" value="اختبار الحفظ المضمون">
                `;
                document.body.appendChild(tempForm);
                
                // اختبار الدالة المضمونة
                if (typeof saveAdvanceDeductionSimple === 'function') {
                    const beforeCount = advancesDeductionsData ? advancesDeductionsData.length : 0;
                    addResult(`📊 عدد السجلات قبل الحفظ: ${beforeCount}`, 'info');
                    
                    const success = saveAdvanceDeductionSimple(55555);
                    
                    if (success) {
                        addResult('✅ تم الحفظ المباشر بنجاح!', 'success');
                        updateStatus('✅ تم الحفظ المضمون بنجاح!', 'success');
                    } else {
                        addResult('❌ فشل الحفظ المباشر', 'error');
                        updateStatus('❌ فشل الحفظ المضمون', 'error');
                    }
                } else {
                    addResult('❌ دالة الحفظ المضمون غير موجودة', 'error');
                    updateStatus('❌ الدالة المضمونة مفقودة', 'error');
                }
                
                // إزالة النموذج المؤقت
                tempForm.remove();
                
            } catch (error) {
                addResult(`❌ خطأ في الحفظ المباشر: ${error.message}`, 'error');
                updateStatus('❌ فشل الحفظ المباشر', 'error');
            }
        }

        // فحص البيانات المحفوظة
        function checkSavedData() {
            updateStatus('جاري فحص البيانات...', 'warning');
            addResult('🔍 فحص البيانات المحفوظة', 'info');
            
            try {
                // فحص localStorage
                const savedData = localStorage.getItem('oscoAdvancesDeductions');
                if (savedData) {
                    const parsed = JSON.parse(savedData);
                    addResult(`📊 إجمالي السجلات: ${parsed.length}`, 'info');
                    
                    // فحص سجلات الموظف التجريبي
                    const employeeRecords = parsed.filter(r => r.employeeId === 55555);
                    addResult(`👤 سجلات الموظف التجريبي: ${employeeRecords.length}`, 'info');
                    
                    if (employeeRecords.length > 0) {
                        employeeRecords.forEach((record, index) => {
                            const typeText = record.type === 'advance' ? 'سلفة' : 'خصم';
                            addResult(`${index + 1}. ${typeText}: ${record.amount} ج.م - ${record.description || 'بدون وصف'}`, 'success');
                        });
                        updateStatus('✅ البيانات محفوظة بنجاح!', 'success');
                    } else {
                        addResult('⚠️ لا توجد سجلات للموظف التجريبي', 'warning');
                        updateStatus('⚠️ لا توجد سجلات محفوظة', 'warning');
                    }
                } else {
                    addResult('❌ لا توجد بيانات في localStorage', 'error');
                    updateStatus('❌ لا توجد بيانات محفوظة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في فحص البيانات', 'error');
            }
        }

        // اختبار تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في الاختبار المضمون للسلف والخصومات', 'info');
                addResult('💡 هذا الاختبار يستخدم دالة حفظ محسنة ومضمونة', 'info');
                
                // فحص النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }
                
                // فحص الدالة المضمونة
                if (typeof saveAdvanceDeductionSimple === 'function') {
                    addResult('✅ دالة الحفظ المضمون متاحة', 'success');
                    updateStatus('✅ النظام جاهز للاختبار المضمون', 'success');
                } else {
                    addResult('❌ دالة الحفظ المضمون غير متاحة', 'error');
                    updateStatus('❌ الدالة المضمونة مفقودة', 'error');
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
