<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التعديل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.secondary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> اختبار أزرار التعديل</h1>
            <p>اختبار سريع لوجود أزرار التعديل في نافذة السجلات</p>
        </div>

        <div class="instructions">
            <h3><i class="fas fa-list-ol"></i> خطوات الاختبار:</h3>
            <div class="step">
                <strong>1.</strong> انقر على "إنشاء بيانات تجريبية" لإنشاء موظف وسجلات أيام
            </div>
            <div class="step">
                <strong>2.</strong> انقر على "فتح نافذة السجلات" لفتح نافذة سجلات الأيام
            </div>
            <div class="step">
                <strong>3.</strong> قم بإلغاء تفعيل "التعديل المباشر في الجدول" لرؤية أزرار التعديل
            </div>
            <div class="step">
                <strong>4.</strong> تحقق من وجود زر "تعديل" بجانب كل سجل
            </div>
            <div class="step">
                <strong>5.</strong> انقر على زر "تعديل" لفتح نافذة التعديل المنفصلة
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>
            <button class="test-button secondary" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>
        </div>

        <div id="status" class="status info">
            <i class="fas fa-info-circle"></i> جاهز لبدء الاختبار...
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h4><i class="fas fa-question-circle"></i> ماذا تتوقع أن ترى؟</h4>
            <ul style="text-align: right; margin: 15px 0;">
                <li><strong>في الوضع المباشر:</strong> حقول إدخال قابلة للتعديل + زر حذف فقط</li>
                <li><strong>في الوضع التقليدي:</strong> نص عادي + زر تعديل + زر حذف</li>
                <li><strong>عند النقر على "تعديل":</strong> نافذة منفصلة للتعديل</li>
            </ul>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            
            statusDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'info');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 999)) {
                    employees.push({
                        id: 999,
                        name: 'أحمد محمد علي',
                        position: 'مهندس برمجيات',
                        employeeCode: 'EMP001',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                }
                
                // إنشاء سجلات أيام تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 999,
                        projectName: 'مشروع تطوير النظام',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 999,
                        projectName: 'مشروع الصيانة',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 1,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 999,
                        projectName: 'مشروع التدريب',
                        startDate: '2024-03-01',
                        endDate: '2024-03-31',
                        calculatedDays: 31,
                        absenceDays: 0,
                        overtimeHours: 15,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });
                
                updateStatus('تم إنشاء البيانات التجريبية بنجاح! (موظف واحد + 3 سجلات أيام)', 'success');
                
            } catch (error) {
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                // التحقق من وجود البيانات
                if (!employees.find(emp => emp.id === 999)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                // فتح نافذة السجلات
                viewDaysRecords(999);
                updateStatus('تم فتح نافذة السجلات! قم بإلغاء تفعيل "التعديل المباشر" لرؤية أزرار التعديل', 'success');
                
            } catch (error) {
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('صفحة الاختبار جاهزة! ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
