<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص المشكلة الفعلية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        .header h1 {
            color: #dc3545;
            margin: 0;
        }
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .debug-section h3 {
            color: #495057;
            margin-top: 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #68d391; }
        .log-error { color: #fc8181; }
        .log-warning { color: #f6e05e; }
        .log-info { color: #63b3ed; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug"></i> تشخيص المشكلة الفعلية</h1>
            <p>سأكتشف السبب الحقيقي وراء عدم ظهور فلتر المشروع</p>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-play"></i> خطوات التشخيص:</h3>
            <button class="button success" onclick="step1()">1. إنشاء بيانات تجريبية</button>
            <button class="button" onclick="step2()">2. فتح النافذة</button>
            <button class="button" onclick="step3()">3. فحص HTML الفعلي</button>
            <button class="button" onclick="step4()">4. فحص دالة viewDaysRecords</button>
            <button class="button danger" onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-terminal"></i> سجل التشخيص:</h3>
            <div class="log" id="debugLog">
                <div class="log-entry log-info">[تحميل] جاهز للتشخيص...</div>
            </div>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-code"></i> HTML الفعلي للنافذة:</h3>
            <div class="code-block" id="actualHTML">سيتم عرض HTML الفعلي هنا...</div>
        </div>

        <div class="debug-section">
            <h3><i class="fas fa-search"></i> تحليل المشكلة:</h3>
            <div id="analysis" style="padding: 15px; background: #fff3cd; border: 1px solid #ffc107; border-radius: 4px;">
                سيتم عرض تحليل المشكلة هنا...
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function step1() {
            log('🔧 الخطوة 1: إنشاء بيانات تجريبية...', 'info');
            
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 55555,
                    name: 'موظف التشخيص',
                    position: 'مطور',
                    employeeCode: 'DEBUG55555',
                    employmentType: 'monthly',
                    basicSalary: 5000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 55555);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    log('🗑️ تم حذف الموظف السابق', 'warning');
                }
                
                employees.push(testEmployee);
                log('✅ تم إنشاء الموظف: ' + testEmployee.name, 'success');
                
                // إنشاء سجلات أيام عمل
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 55555,
                        projectName: 'مشروع التشخيص الأول',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 25,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 55555,
                        projectName: 'مشروع التشخيص الثاني',
                        startDate: '2024-02-01',
                        endDate: '2024-02-29',
                        calculatedDays: 22,
                        absenceDays: 1,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 55555);
                
                // إضافة السجلات الجديدة
                testRecords.forEach(record => {
                    employeeDaysData.push(record);
                    log(`📋 تم إضافة سجل: ${record.projectName}`, 'success');
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();
                
                log(`✅ تم إنشاء ${testRecords.length} سجل لمشروعين مختلفين`, 'success');
                
            } catch (error) {
                log('❌ خطأ في الخطوة 1: ' + error.message, 'error');
            }
        }

        function step2() {
            log('🔧 الخطوة 2: فتح نافذة السجلات...', 'info');
            
            try {
                if (!employees.find(emp => emp.id === 55555)) {
                    log('❌ لا يوجد موظف تجريبي. قم بتنفيذ الخطوة 1 أولاً', 'error');
                    return;
                }
                
                log('📋 استدعاء دالة viewDaysRecords...', 'info');
                viewDaysRecords(55555);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        log('✅ تم فتح النافذة بنجاح', 'success');
                        log('🔍 جاري فحص محتوى النافذة...', 'info');
                        step3();
                    } else {
                        log('❌ فشل في فتح النافذة', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                log('❌ خطأ في الخطوة 2: ' + error.message, 'error');
            }
        }

        function step3() {
            log('🔧 الخطوة 3: فحص HTML الفعلي...', 'info');
            
            try {
                const modal = document.getElementById('daysRecordsModal');
                if (!modal) {
                    log('❌ النافذة غير موجودة', 'error');
                    return;
                }
                
                // عرض HTML الفعلي
                const actualHTMLDiv = document.getElementById('actualHTML');
                actualHTMLDiv.textContent = modal.innerHTML;
                
                // فحص عناصر الفلتر
                const filterContainer = modal.querySelector('#projectFilterContainer');
                const projectFilter = modal.querySelector('#projectFilter');
                const filterStatus = modal.querySelector('#filterStatus');
                
                log('🔍 فحص عناصر الفلتر:', 'info');
                log(`- #projectFilterContainer: ${filterContainer ? '✅ موجود' : '❌ مفقود'}`, filterContainer ? 'success' : 'error');
                log(`- #projectFilter: ${projectFilter ? '✅ موجود' : '❌ مفقود'}`, projectFilter ? 'success' : 'error');
                log(`- #filterStatus: ${filterStatus ? '✅ موجود' : '❌ مفقود'}`, filterStatus ? 'success' : 'error');
                
                if (projectFilter) {
                    const options = projectFilter.querySelectorAll('option');
                    log(`📊 عدد خيارات الفلتر: ${options.length}`, 'info');
                    options.forEach((option, index) => {
                        log(`  ${index + 1}. "${option.textContent}" (value: "${option.value}")`, 'info');
                    });
                }
                
                // فحص النص في HTML
                const htmlContent = modal.innerHTML;
                const hasFilterText = htmlContent.includes('فلتر المشروع');
                log(`🔍 النص "فلتر المشروع" في HTML: ${hasFilterText ? '✅ موجود' : '❌ مفقود'}`, hasFilterText ? 'success' : 'error');
                
                // تحليل المشكلة
                analyzeProblems(filterContainer, projectFilter, filterStatus, hasFilterText);
                
            } catch (error) {
                log('❌ خطأ في الخطوة 3: ' + error.message, 'error');
            }
        }

        function step4() {
            log('🔧 الخطوة 4: فحص دالة viewDaysRecords...', 'info');
            
            try {
                // فحص وجود الدالة
                if (typeof viewDaysRecords === 'function') {
                    log('✅ دالة viewDaysRecords موجودة', 'success');
                    
                    // فحص الكود المصدري للدالة
                    const functionCode = viewDaysRecords.toString();
                    const hasFilterCode = functionCode.includes('projectFilterContainer');
                    log(`🔍 كود الفلتر في الدالة: ${hasFilterCode ? '✅ موجود' : '❌ مفقود'}`, hasFilterCode ? 'success' : 'error');
                    
                    if (hasFilterCode) {
                        log('🔍 البحث عن سبب عدم ظهور الفلتر...', 'warning');
                        
                        // فحص المتغيرات
                        const employeeRecords = employeeDaysData.filter(record => record.employeeId === 55555);
                        log(`📊 عدد سجلات الموظف: ${employeeRecords.length}`, 'info');
                        
                        if (employeeRecords.length > 0) {
                            const uniqueProjects = [...new Set(employeeRecords.map(record => record.projectName).filter(name => name && name.trim() !== ''))];
                            log(`📊 عدد المشروعات الفريدة: ${uniqueProjects.length}`, 'info');
                            uniqueProjects.forEach((project, index) => {
                                log(`  ${index + 1}. "${project}"`, 'info');
                            });
                        }
                    }
                    
                } else {
                    log('❌ دالة viewDaysRecords غير موجودة', 'error');
                }
                
            } catch (error) {
                log('❌ خطأ في الخطوة 4: ' + error.message, 'error');
            }
        }

        function analyzeProblems(filterContainer, projectFilter, filterStatus, hasFilterText) {
            const analysisDiv = document.getElementById('analysis');
            let analysis = '<h4>🔍 تحليل المشكلة:</h4>';
            
            if (!hasFilterText) {
                analysis += '<p style="color: #dc3545;"><strong>المشكلة الأساسية:</strong> النص "فلتر المشروع" غير موجود في HTML، مما يعني أن الكود لا يتم تنفيذه أصلاً.</p>';
                analysis += '<p><strong>الحلول المحتملة:</strong></p>';
                analysis += '<ul>';
                analysis += '<li>التأكد من أن دالة viewDaysRecords تحتوي على كود الفلتر</li>';
                analysis += '<li>فحص وجود أخطاء JavaScript تمنع تنفيذ الكود</li>';
                analysis += '<li>التأكد من أن template string يتم تنفيذه بشكل صحيح</li>';
                analysis += '</ul>';
            } else if (!filterContainer) {
                analysis += '<p style="color: #ffc107;"><strong>المشكلة:</strong> النص موجود لكن العنصر #projectFilterContainer غير موجود.</p>';
                analysis += '<p>هذا يعني أن هناك مشكلة في تنفيذ HTML أو CSS.</p>';
            } else if (!projectFilter) {
                analysis += '<p style="color: #ffc107;"><strong>المشكلة:</strong> الحاوي موجود لكن قائمة المشروعات #projectFilter غير موجودة.</p>';
            } else {
                analysis += '<p style="color: #28a745;"><strong>النتيجة:</strong> جميع العناصر موجودة! الفلتر يجب أن يكون ظاهراً.</p>';
                analysis += '<p>إذا كان الفلتر غير ظاهر، فقد تكون المشكلة في CSS أو في موقع العنصر.</p>';
            }
            
            analysisDiv.innerHTML = analysis;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = 
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewDaysRecords === 'function') {
                    log('✅ النظام جاهز للتشخيص', 'success');
                    log('📋 ابدأ بالخطوة 1: إنشاء بيانات تجريبية', 'info');
                } else {
                    log('❌ خطأ: النظام غير جاهز', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
