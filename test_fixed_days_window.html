<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النافذة المُصلحة لحساب الأيام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            margin-top: 20px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #34495e;
        }

        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار النافذة المُصلحة لحساب الأيام</h1>
            <p>التأكد من عمل دالة openDaysCalculator والنافذة البسيطة الجديدة</p>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز للاختبار
        </div>

        <div class="test-section">
            <h3>🔍 إعداد البيانات</h3>
            <button class="btn" onclick="createTestEmployee()">إنشاء موظف تجريبي</button>
            <button class="btn warning" onclick="createTestProjects()">إنشاء مشروعات تجريبية</button>
        </div>

        <div class="test-section">
            <h3>🚀 اختبار النافذة</h3>
            <button class="btn success" onclick="testOpenWindow()">فتح النافذة البسيطة</button>
            <button class="btn" onclick="checkWindowElements()">فحص عناصر النافذة</button>
            <button class="btn warning" onclick="testFunctionality()">اختبار الوظائف</button>
        </div>

        <div class="test-section">
            <h3>📋 اختبارات شاملة</h3>
            <button class="btn success" onclick="runFullTest()">تشغيل جميع الاختبارات</button>
            <button class="btn" onclick="clearData()">مسح البيانات</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">[FIXED_WINDOW_TEST] نظام اختبار النافذة المُصلحة جاهز...</div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function createTestEmployee() {
            addLog('📋 إنشاء موظف تجريبي...', 'info');
            
            try {
                const testEmployee = {
                    id: 4001,
                    name: "موظف تجريبي - النافذة المُصلحة",
                    position: "مطور",
                    employeeCode: "FIXED_4001",
                    employmentType: "monthly",
                    salary: { basic: 6000, total: 6000 }
                };
                
                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 4001);
                    employees.push(testEmployee);
                } else {
                    window.employees = [testEmployee];
                }
                
                addLog('✅ تم إنشاء الموظف التجريبي: ' + testEmployee.name, 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                
            } catch (error) {
                addLog('❌ خطأ في إنشاء الموظف: ' + error.message, 'error');
                updateStatus('خطأ في إنشاء الموظف التجريبي', 'error');
            }
        }

        function createTestProjects() {
            addLog('🏗️ إنشاء مشروعات تجريبية...', 'info');
            
            try {
                const testProjects = [
                    {
                        id: 'fixed_proj_001',
                        name: 'مشروع النافذة المُصلحة 1',
                        description: 'مشروع للاختبار',
                        status: 'in-progress'
                    },
                    {
                        id: 'fixed_proj_002', 
                        name: 'مشروع النافذة المُصلحة 2',
                        description: 'مشروع آخر للاختبار',
                        status: 'planning'
                    }
                ];
                
                if (typeof projects !== 'undefined') {
                    projects = projects.filter(p => !p.id.startsWith('fixed_proj_'));
                    projects.push(...testProjects);
                } else {
                    window.projects = testProjects;
                }
                
                addLog(`✅ تم إنشاء ${testProjects.length} مشروع تجريبي`, 'success');
                updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');
                
            } catch (error) {
                addLog('❌ خطأ في إنشاء المشروعات: ' + error.message, 'error');
            }
        }

        function testOpenWindow() {
            addLog('🚀 اختبار فتح النافذة...', 'info');
            
            try {
                // التحقق من وجود الموظف
                if (!employees || !employees.find(emp => emp.id === 4001)) {
                    addLog('⚠️ يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return;
                }
                
                // التحقق من وجود الدالة
                if (typeof openDaysCalculator !== 'function') {
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    updateStatus('❌ دالة فتح النافذة غير موجودة', 'error');
                    return;
                }
                
                // فتح النافذة
                addLog('🔧 فتح النافذة للموظف 4001...', 'info');
                openDaysCalculator(4001);
                
                // التحقق من فتح النافذة
                setTimeout(() => {
                    const modal = document.getElementById('daysModal');
                    if (modal) {
                        addLog('✅ تم فتح النافذة بنجاح!', 'success');
                        updateStatus('✅ النافذة مفتوحة - يمكنك الآن اختبار العناصر', 'success');
                    } else {
                        addLog('❌ لم يتم فتح النافذة', 'error');
                        updateStatus('❌ فشل في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('❌ خطأ في فتح النافذة: ' + error.message, 'error');
                updateStatus('❌ خطأ في فتح النافذة', 'error');
            }
        }

        function checkWindowElements() {
            addLog('🔍 فحص عناصر النافذة...', 'info');
            
            const modal = document.getElementById('daysModal');
            if (!modal) {
                addLog('⚠️ يرجى فتح النافذة أولاً', 'warning');
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }
            
            const requiredElements = [
                { id: 'daysMonth', name: 'قائمة الشهر' },
                { id: 'daysYear', name: 'قائمة السنة' },
                { id: 'daysProject', name: 'قائمة المشروعات' },
                { id: 'calculatedDays', name: 'حقل أيام العمل' },
                { id: 'daysEmployeeId', name: 'معرف الموظف المخفي' }
            ];
            
            let foundElements = 0;
            requiredElements.forEach(element => {
                const el = document.getElementById(element.id);
                if (el) {
                    foundElements++;
                    addLog(`✅ عنصر موجود: ${element.name}`, 'success');
                } else {
                    addLog(`❌ عنصر مفقود: ${element.name}`, 'error');
                }
            });
            
            if (foundElements === requiredElements.length) {
                addLog(`🎉 جميع العناصر موجودة (${foundElements}/${requiredElements.length})`, 'success');
                updateStatus('✅ جميع عناصر النافذة موجودة', 'success');
            } else {
                addLog(`⚠️ بعض العناصر مفقودة (${foundElements}/${requiredElements.length})`, 'warning');
                updateStatus(`⚠️ ${requiredElements.length - foundElements} عنصر مفقود`, 'warning');
            }
        }

        function testFunctionality() {
            addLog('⚙️ اختبار وظائف النافذة...', 'info');
            
            try {
                // اختبار دالة الحفظ
                if (typeof saveDaysCalculation === 'function') {
                    addLog('✅ دالة الحفظ موجودة', 'success');
                } else {
                    addLog('❌ دالة الحفظ غير موجودة', 'error');
                }
                
                // اختبار دالة الإغلاق
                if (typeof closeDaysModal === 'function') {
                    addLog('✅ دالة الإغلاق موجودة', 'success');
                } else {
                    addLog('❌ دالة الإغلاق غير موجودة', 'error');
                }
                
                // اختبار تحميل المشروعات
                if (typeof loadProjectsList === 'function') {
                    addLog('✅ دالة تحميل المشروعات موجودة', 'success');
                } else {
                    addLog('❌ دالة تحميل المشروعات غير موجودة', 'error');
                }
                
                updateStatus('تم فحص الوظائف الأساسية', 'info');
                
            } catch (error) {
                addLog('❌ خطأ في اختبار الوظائف: ' + error.message, 'error');
            }
        }

        function runFullTest() {
            addLog('🚀 تشغيل جميع الاختبارات...', 'info');
            updateStatus('تشغيل جميع الاختبارات...', 'info');
            
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1500);
            setTimeout(() => testOpenWindow(), 2500);
            setTimeout(() => checkWindowElements(), 4000);
            setTimeout(() => testFunctionality(), 5500);
            
            setTimeout(() => {
                addLog('🎉 انتهاء جميع الاختبارات!', 'success');
                updateStatus('🎉 تم الانتهاء من جميع الاختبارات', 'success');
            }, 7000);
        }

        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح الموظفين التجريبيين
                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 4001);
                }
                
                // مسح المشروعات التجريبية
                if (typeof projects !== 'undefined') {
                    projects = projects.filter(p => !p.id.startsWith('fixed_proj_'));
                }
                
                // إغلاق النافذة
                const modal = document.getElementById('daysModal');
                if (modal) {
                    modal.remove();
                }
                
                // مسح السجل
                document.getElementById('logContainer').innerHTML = 
                    '<div class="log-entry info">[FIXED_WINDOW_TEST] تم مسح البيانات - النظام جاهز للاختبار...</div>';
                
                updateStatus('تم مسح جميع البيانات التجريبية', 'success');
            }
        }
    </script>
</body>
</html>
