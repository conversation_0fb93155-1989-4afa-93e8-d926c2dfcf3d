<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي لمشكلة التحقق</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #8e44ad;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .test-step {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-step:last-child {
            border-bottom: none;
        }
        
        .step-icon {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        
        .step-icon.success { background: #28a745; }
        .step-icon.error { background: #dc3545; }
        .step-icon.warning { background: #ffc107; color: #212529; }
        .step-icon.info { background: #17a2b8; }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .field-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .field-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .field-item.found {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        
        .field-item.missing {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .field-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .field-value {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microscope"></i> الاختبار النهائي لمشكلة التحقق</h1>
            <p>اختبار شامل ومفصل لحل مشكلة "يرجى ملء باقي الحقول"</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button danger" onclick="runCompleteValidationTest()">
                <i class="fas fa-play"></i> اختبار شامل
            </button>
            
            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف
            </button>
            
            <button class="test-button warning" onclick="openDaysWindow()">
                <i class="fas fa-calculator"></i> فتح النافذة
            </button>
            
            <button class="test-button success" onclick="testFieldDetection()">
                <i class="fas fa-search"></i> فحص الحقول
            </button>
            
            <button class="test-button" onclick="testSaveWithData()">
                <i class="fas fa-save"></i> اختبار الحفظ
            </button>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء الاختبار النهائي...</p>
        </div>

        <div class="field-status" id="fieldStatus" style="display: none;">
            <h4>حالة الحقول:</h4>
        </div>

        <div class="test-results" id="testResults">
            <h4><i class="fas fa-list"></i> نتائج الاختبار:</h4>
            <div id="testSteps">لم يبدأ الاختبار بعد...</div>
        </div>

        <div class="console-output" id="consoleOutput">
            [FINAL_VALIDATION_TEST] الاختبار النهائي لمشكلة التحقق جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let testStepCounter = 0;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function addTestStep(message, type = 'info') {
            testStepCounter++;
            const stepsDiv = document.getElementById('testSteps');
            
            let iconClass = 'info';
            let icon = 'ℹ️';
            
            if (type === 'success') {
                iconClass = 'success';
                icon = '✅';
            } else if (type === 'error') {
                iconClass = 'error';
                icon = '❌';
            } else if (type === 'warning') {
                iconClass = 'warning';
                icon = '⚠️';
            }
            
            const stepDiv = document.createElement('div');
            stepDiv.className = 'test-step';
            stepDiv.innerHTML = `
                <div class="step-icon ${iconClass}">${testStepCounter}</div>
                <div>
                    <strong>${icon} خطوة ${testStepCounter}:</strong> ${message}
                </div>
            `;
            
            stepsDiv.appendChild(stepDiv);
            stepsDiv.scrollTop = stepsDiv.scrollHeight;
        }

        function createTestEmployee() {
            addTestStep('إنشاء موظف تجريبي...', 'info');
            addLog('🔧 إنشاء موظف تجريبي...');
            
            try {
                if (!employees.find(emp => emp.id === 555)) {
                    employees.push({
                        id: 555,
                        name: 'محمد أحمد - اختبار التحقق النهائي',
                        position: 'مطور برمجيات',
                        employeeCode: 'FINAL555',
                        employmentType: 'monthly',
                        basicSalary: 7500
                    });
                    addTestStep('تم إنشاء الموظف التجريبي بنجاح', 'success');
                    addLog('✓ تم إنشاء موظف تجريبي');
                } else {
                    addTestStep('الموظف التجريبي موجود مسبقاً', 'warning');
                }
                
                updateStatus('تم إنشاء الموظف التجريبي بنجاح!', 'success');
                
            } catch (error) {
                addTestStep('فشل في إنشاء الموظف التجريبي: ' + error.message, 'error');
                addLog('✗ خطأ في إنشاء الموظف: ' + error.message);
                updateStatus('خطأ في إنشاء الموظف التجريبي: ' + error.message, 'error');
            }
        }

        function openDaysWindow() {
            addTestStep('فتح نافذة حساب الأيام...', 'info');
            
            try {
                if (!employees.find(emp => emp.id === 555)) {
                    addTestStep('الموظف التجريبي غير موجود - سيتم إنشاؤه', 'warning');
                    createTestEmployee();
                    
                    setTimeout(() => {
                        openDaysWindow();
                    }, 1000);
                    return;
                }
                
                addLog('🔧 فتح نافذة حساب الأيام...');
                createEnhancedDaysCalculator(555);
                
                setTimeout(() => {
                    const modal = document.getElementById('enhancedDaysModal');
                    if (modal) {
                        addTestStep('تم فتح نافذة حساب الأيام بنجاح', 'success');
                        addLog('✓ تم فتح نافذة حساب الأيام بنجاح');
                        updateStatus('تم فتح نافذة حساب الأيام! الآن يمكنك اختبار الحقول', 'success');
                    } else {
                        addTestStep('فشل في فتح نافذة حساب الأيام', 'error');
                        addLog('✗ فشل في فتح نافذة حساب الأيام');
                        updateStatus('فشل في فتح نافذة حساب الأيام', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addTestStep('خطأ في فتح النافذة: ' + error.message, 'error');
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح نافذة حساب الأيام: ' + error.message, 'error');
            }
        }

        function testFieldDetection() {
            addTestStep('فحص اكتشاف الحقول...', 'info');
            addLog('🔍 فحص اكتشاف الحقول...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addTestStep('النافذة غير مفتوحة - يرجى فتحها أولاً', 'error');
                updateStatus('يرجى فتح نافذة حساب الأيام أولاً', 'error');
                return;
            }
            
            // اختبار دالة findElementByPossibleIds
            const fieldsToTest = [
                { name: 'معرف الموظف', ids: ['employeeId', 'daysEmployeeId', 'salaryEmployeeId'] },
                { name: 'المشروع', ids: ['projectSelect', 'daysProjectSelect', 'project'] },
                { name: 'الشهر', ids: ['monthSelect', 'daysMonthSelect', 'month'] },
                { name: 'السنة', ids: ['yearSelect', 'daysYearSelect', 'year'] },
                { name: 'الأيام المحسوبة', ids: ['calculatedDays', 'daysCalculatedDays', 'totalDays'] },
                { name: 'أيام الغياب', ids: ['absenceDays', 'daysAbsenceDays', 'absence'] },
                { name: 'الساعات الإضافية', ids: ['overtimeHours', 'daysOvertimeHours', 'overtime'] }
            ];
            
            const fieldStatusDiv = document.getElementById('fieldStatus');
            fieldStatusDiv.style.display = 'block';
            fieldStatusDiv.innerHTML = '<h4>حالة الحقول:</h4>';
            
            let foundFields = 0;
            let totalFields = fieldsToTest.length;
            
            fieldsToTest.forEach(field => {
                const element = findElementByPossibleIds(field.ids);
                const fieldDiv = document.createElement('div');
                fieldDiv.className = `field-item ${element ? 'found' : 'missing'}`;
                
                if (element) {
                    foundFields++;
                    fieldDiv.innerHTML = `
                        <div class="field-name">✅ ${field.name}</div>
                        <div class="field-value">ID: ${element.id} | القيمة: "${element.value || 'فارغ'}"</div>
                    `;
                    addTestStep(`تم العثور على حقل ${field.name} (${element.id})`, 'success');
                } else {
                    fieldDiv.innerHTML = `
                        <div class="field-name">❌ ${field.name}</div>
                        <div class="field-value">لم يتم العثور على أي من: ${field.ids.join(', ')}</div>
                    `;
                    addTestStep(`لم يتم العثور على حقل ${field.name}`, 'error');
                }
                
                fieldStatusDiv.appendChild(fieldDiv);
            });
            
            const percentage = (foundFields / totalFields) * 100;
            addTestStep(`تم العثور على ${foundFields}/${totalFields} حقل (${percentage.toFixed(0)}%)`, 
                       percentage >= 80 ? 'success' : percentage >= 50 ? 'warning' : 'error');
            
            if (percentage >= 80) {
                updateStatus(`ممتاز! تم العثور على ${foundFields}/${totalFields} حقل`, 'success');
            } else {
                updateStatus(`تحذير: تم العثور على ${foundFields}/${totalFields} حقل فقط`, 'warning');
            }
        }

        function fillFormWithTestData() {
            addTestStep('ملء النموذج ببيانات تجريبية...', 'info');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addTestStep('النافذة غير مفتوحة', 'error');
                return false;
            }
            
            // ملء الحقول
            const projectSelect = modal.querySelector('#projectSelect');
            const monthSelect = modal.querySelector('#monthSelect');
            const yearSelect = modal.querySelector('#yearSelect');
            const calculatedDays = modal.querySelector('#calculatedDays');
            const absenceDays = modal.querySelector('#absenceDays');
            const overtimeHours = modal.querySelector('#overtimeHours');
            
            let filledFields = 0;
            
            if (projectSelect) {
                projectSelect.value = 'مشروع اختبار التحقق النهائي';
                filledFields++;
                addTestStep('تم ملء حقل المشروع', 'success');
            }
            
            if (monthSelect) {
                monthSelect.value = '1';
                filledFields++;
                addTestStep('تم ملء حقل الشهر', 'success');
            }
            
            if (yearSelect) {
                yearSelect.value = '2024';
                filledFields++;
                addTestStep('تم ملء حقل السنة', 'success');
            }
            
            if (calculatedDays) {
                calculatedDays.value = '28';
                filledFields++;
                addTestStep('تم ملء حقل الأيام المحسوبة', 'success');
            }
            
            if (absenceDays) {
                absenceDays.value = '1';
                filledFields++;
                addTestStep('تم ملء حقل أيام الغياب', 'success');
            }
            
            if (overtimeHours) {
                overtimeHours.value = '6';
                filledFields++;
                addTestStep('تم ملء حقل الساعات الإضافية', 'success');
            }
            
            addTestStep(`تم ملء ${filledFields} حقل بنجاح`, filledFields >= 4 ? 'success' : 'warning');
            return filledFields >= 4;
        }

        function testSaveWithData() {
            addTestStep('اختبار الحفظ مع البيانات المملوءة...', 'info');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addTestStep('النافذة غير مفتوحة - يرجى فتحها أولاً', 'error');
                updateStatus('يرجى فتح نافذة حساب الأيام أولاً', 'error');
                return;
            }
            
            // ملء النموذج
            if (fillFormWithTestData()) {
                addTestStep('تم ملء النموذج - الآن سيتم اختبار الحفظ', 'info');
                
                setTimeout(() => {
                    addLog('🔄 اختبار دالة الحفظ...');
                    
                    try {
                        const result = saveDaysCalculationFast();
                        
                        if (result) {
                            addTestStep('✅ نجح الحفظ! المشكلة تم حلها', 'success');
                            updateStatus('✅ ممتاز! تم حل المشكلة - الحفظ يعمل بشكل صحيح', 'success');
                        } else {
                            addTestStep('❌ فشل الحفظ - المشكلة لا تزال موجودة', 'error');
                            updateStatus('❌ المشكلة لا تزال موجودة - الحفظ لا يعمل', 'error');
                        }
                    } catch (error) {
                        addTestStep('❌ خطأ في دالة الحفظ: ' + error.message, 'error');
                        updateStatus('❌ خطأ في دالة الحفظ: ' + error.message, 'error');
                    }
                }, 1000);
            } else {
                addTestStep('فشل في ملء النموذج', 'error');
                updateStatus('فشل في ملء النموذج', 'error');
            }
        }

        function runCompleteValidationTest() {
            updateStatus('🔍 بدء الاختبار الشامل...', 'warning');
            addLog('=== بدء الاختبار الشامل لمشكلة التحقق ===');
            
            // إعادة تعيين العداد
            testStepCounter = 0;
            document.getElementById('testSteps').innerHTML = '';
            
            addTestStep('بدء الاختبار الشامل لمشكلة التحقق', 'info');
            
            // خطوة 1: إنشاء الموظف
            setTimeout(() => {
                createTestEmployee();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openDaysWindow();
            }, 2000);
            
            // خطوة 3: فحص الحقول
            setTimeout(() => {
                testFieldDetection();
            }, 4000);
            
            // خطوة 4: اختبار الحفظ
            setTimeout(() => {
                testSaveWithData();
            }, 7000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addTestStep('انتهاء الاختبار الشامل', 'info');
                addLog('=== انتهاء الاختبار الشامل ===');
                addLog('📋 راجع النتائج أعلاه لمعرفة حالة المشكلة');
            }, 10000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل الاختبار النهائي - جاهز لفحص المشكلة!', 'info');
            addLog('تم تحميل الاختبار النهائي لمشكلة التحقق');
        });
    </script>
</body>
</html>
