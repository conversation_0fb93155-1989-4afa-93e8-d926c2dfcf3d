<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التحقق من الحقول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border-radius: 10px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
            margin: 20px 0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .fix-summary {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .fix-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        
        .fix-item:last-child {
            border-bottom: none;
        }
        
        .fix-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            background: #28a745;
        }
        
        .console-output {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .test-scenario {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        
        .scenario-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .scenario-steps {
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bug-slash"></i> اختبار إصلاح التحقق من الحقول</h1>
            <p>التأكد من أن النظام يتعرف على الحقول المملوءة بشكل صحيح</p>
        </div>

        <div class="fix-summary">
            <h4><i class="fas fa-wrench"></i> الإصلاحات المطبقة:</h4>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>تحسين دالة saveDaysCalculationFast</strong>
                    <br><small>استخدام findElementByPossibleIds للبحث عن الحقول بمعرفات مختلفة</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>تحسين رسائل الخطأ</strong>
                    <br><small>رسائل واضحة ومفصلة تحدد الحقول المطلوبة بدقة</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إضافة تسجيل مفصل</strong>
                    <br><small>console.log لتتبع البيانات المجمعة والأخطاء</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>دعم معرفات متعددة</strong>
                    <br><small>البحث في معرفات مختلفة للحقول لضمان التوافق</small>
                </div>
            </div>
        </div>

        <div class="test-scenario">
            <div class="scenario-title">🧪 سيناريوهات الاختبار:</div>
            <div class="scenario-steps">
                <strong>السيناريو 1:</strong> اختبار مع جميع الحقول مملوءة<br>
                <strong>السيناريو 2:</strong> اختبار مع حقول فارغة لرؤية رسائل الخطأ<br>
                <strong>السيناريو 3:</strong> اختبار الحفظ الناجح<br>
                <strong>السيناريو 4:</strong> اختبار التحقق من البيانات
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button danger" onclick="runValidationTest()">
                <i class="fas fa-play"></i> اختبار التحقق الشامل
            </button>
            
            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
            </button>
            
            <button class="test-button warning" onclick="openDaysWindow()">
                <i class="fas fa-calculator"></i> فتح نافذة حساب الأيام
            </button>
            
            <button class="test-button success" onclick="testFilledForm()">
                <i class="fas fa-check-circle"></i> اختبار نموذج مملوء
            </button>
            
            <button class="test-button" onclick="testEmptyForm()">
                <i class="fas fa-times-circle"></i> اختبار نموذج فارغ
            </button>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لاختبار إصلاح التحقق من الحقول...</p>
        </div>

        <div class="console-output" id="consoleOutput">
            [VALIDATION_FIX_TEST] مختبر إصلاح التحقق من الحقول جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;
            
            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';
            
            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function addLog(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
        }

        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addLog('🔧 إنشاء موظف تجريبي...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 777)) {
                    employees.push({
                        id: 777,
                        name: 'سارة أحمد - اختبار التحقق',
                        position: 'محاسبة',
                        employeeCode: 'VAL777',
                        employmentType: 'monthly',
                        basicSalary: 7000
                    });
                    addLog('✓ تم إنشاء موظف تجريبي');
                }
                
                updateStatus('تم إنشاء الموظف التجريبي بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء الموظف: ' + error.message);
                updateStatus('خطأ في إنشاء الموظف التجريبي: ' + error.message, 'error');
            }
        }

        function openDaysWindow() {
            try {
                if (!employees.find(emp => emp.id === 777)) {
                    updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                addLog('🔧 فتح نافذة حساب الأيام...');
                createEnhancedDaysCalculator(777);
                
                setTimeout(() => {
                    const modal = document.getElementById('enhancedDaysModal');
                    if (modal) {
                        addLog('✓ تم فتح نافذة حساب الأيام بنجاح');
                        updateStatus('تم فتح نافذة حساب الأيام! الآن يمكنك اختبار التحقق', 'success');
                    } else {
                        addLog('✗ فشل في فتح نافذة حساب الأيام');
                        updateStatus('فشل في فتح نافذة حساب الأيام', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة: ' + error.message);
                updateStatus('خطأ في فتح نافذة حساب الأيام: ' + error.message, 'error');
            }
        }

        function fillFormFields() {
            addLog('📝 ملء حقول النموذج...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addLog('❌ النافذة غير مفتوحة');
                return false;
            }
            
            // ملء الحقول
            const projectSelect = modal.querySelector('#projectSelect');
            const monthSelect = modal.querySelector('#monthSelect');
            const yearSelect = modal.querySelector('#yearSelect');
            const calculatedDays = modal.querySelector('#calculatedDays');
            const absenceDays = modal.querySelector('#absenceDays');
            const overtimeHours = modal.querySelector('#overtimeHours');
            
            if (projectSelect) {
                projectSelect.value = 'مشروع اختبار التحقق';
                addLog('✓ تم ملء حقل المشروع');
            }
            
            if (monthSelect) {
                monthSelect.value = '1';
                addLog('✓ تم ملء حقل الشهر');
            }
            
            if (yearSelect) {
                yearSelect.value = '2024';
                addLog('✓ تم ملء حقل السنة');
            }
            
            if (calculatedDays) {
                calculatedDays.value = '25';
                addLog('✓ تم ملء حقل الأيام المحسوبة');
            }
            
            if (absenceDays) {
                absenceDays.value = '2';
                addLog('✓ تم ملء حقل أيام الغياب');
            }
            
            if (overtimeHours) {
                overtimeHours.value = '8';
                addLog('✓ تم ملء حقل الساعات الإضافية');
            }
            
            return true;
        }

        function testFilledForm() {
            addLog('🧪 اختبار النموذج المملوء...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                updateStatus('يرجى فتح نافذة حساب الأيام أولاً', 'error');
                return;
            }
            
            // ملء النموذج
            if (fillFormFields()) {
                addLog('✓ تم ملء جميع الحقول');
                
                // اختبار الحفظ
                setTimeout(() => {
                    addLog('🔄 اختبار الحفظ...');
                    
                    try {
                        const result = saveDaysCalculation();
                        if (result) {
                            addLog('✅ نجح الحفظ! النموذج المملوء يعمل بشكل صحيح');
                            updateStatus('✅ نجح الاختبار! النموذج المملوء يعمل بشكل صحيح', 'success');
                        } else {
                            addLog('❌ فشل الحفظ رغم ملء النموذج');
                            updateStatus('❌ فشل الاختبار! النموذج المملوء لا يعمل', 'error');
                        }
                    } catch (error) {
                        addLog('❌ خطأ في اختبار الحفظ: ' + error.message);
                        updateStatus('❌ خطأ في اختبار الحفظ: ' + error.message, 'error');
                    }
                }, 500);
            }
        }

        function testEmptyForm() {
            addLog('🧪 اختبار النموذج الفارغ...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                updateStatus('يرجى فتح نافذة حساب الأيام أولاً', 'error');
                return;
            }
            
            // مسح جميع الحقول
            const fields = modal.querySelectorAll('input, select');
            fields.forEach(field => {
                if (field.id !== 'employeeId') { // لا نمسح معرف الموظف
                    field.value = '';
                }
            });
            
            addLog('✓ تم مسح جميع الحقول');
            
            // اختبار الحفظ
            setTimeout(() => {
                addLog('🔄 اختبار الحفظ مع النموذج الفارغ...');
                
                try {
                    const result = saveDaysCalculation();
                    if (!result) {
                        addLog('✅ ممتاز! النظام رفض الحفظ وعرض رسائل خطأ مناسبة');
                        updateStatus('✅ نجح الاختبار! النظام يتحقق من الحقول بشكل صحيح', 'success');
                    } else {
                        addLog('❌ مشكلة! النظام سمح بالحفظ رغم وجود حقول فارغة');
                        updateStatus('❌ فشل الاختبار! النظام لا يتحقق من الحقول', 'error');
                    }
                } catch (error) {
                    addLog('❌ خطأ في اختبار النموذج الفارغ: ' + error.message);
                    updateStatus('❌ خطأ في اختبار النموذج الفارغ: ' + error.message, 'error');
                }
            }, 500);
        }

        function runValidationTest() {
            updateStatus('🔍 بدء اختبار التحقق الشامل...', 'warning');
            addLog('=== بدء اختبار التحقق الشامل ===');
            
            // خطوة 1: إنشاء الموظف
            setTimeout(() => {
                createTestEmployee();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openDaysWindow();
            }, 2000);
            
            // خطوة 3: اختبار النموذج الفارغ
            setTimeout(() => {
                testEmptyForm();
            }, 4000);
            
            // خطوة 4: اختبار النموذج المملوء
            setTimeout(() => {
                testFilledForm();
            }, 7000);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء اختبار التحقق الشامل ===');
                addLog('📋 راجع النتائج أعلاه للتأكد من نجاح الإصلاح');
            }, 10000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل مختبر إصلاح التحقق من الحقول - جاهز للاختبار!', 'info');
            addLog('تم تحميل مختبر إصلاح التحقق من الحقول');
        });
    </script>
</body>
</html>
