<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصدير PDF</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #dc3545;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .test-button {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        }

        .result-item.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .result-item.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .result-item.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }

        .pdf-preview {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }

        .demo-table th,
        .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .demo-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-file-pdf"></i> اختبار تصدير PDF</h1>
            <p>اختبار شامل لوظيفة تصدير سجل الموظفين إلى PDF</p>
        </div>

        <div class="content">
            <div class="test-grid">
                <div class="test-card">
                    <h3><i class="fas fa-cogs"></i> اختبارات النظام الأساسي</h3>
                    <button class="test-button" onclick="testSystemLoad()">
                        <i class="fas fa-power-off"></i> اختبار تحميل النظام
                    </button>
                    <button class="test-button" onclick="testPDFLibraries()">
                        <i class="fas fa-book"></i> اختبار مكتبات PDF
                    </button>
                    <button class="test-button" onclick="testEmployeeData()">
                        <i class="fas fa-database"></i> اختبار بيانات الموظفين
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-file-pdf"></i> اختبارات PDF</h3>
                    <button class="test-button" onclick="testPDFFunction()">
                        <i class="fas fa-code"></i> اختبار دالة PDF
                    </button>
                    <button class="test-button" onclick="testPDFGeneration()">
                        <i class="fas fa-file-export"></i> اختبار إنشاء PDF
                    </button>
                    <button class="test-button" onclick="testTableStructure()">
                        <i class="fas fa-table"></i> اختبار هيكل الجدول
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-download"></i> اختبارات التصدير</h3>
                    <button class="test-button" onclick="createTestData()">
                        <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                    </button>
                    <button class="test-button" onclick="exportTestPDF()">
                        <i class="fas fa-file-download"></i> تصدير PDF تجريبي
                    </button>
                    <button class="test-button" onclick="testSerialColumn()">
                        <i class="fas fa-list-ol"></i> اختبار عمود المسلسل
                    </button>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-tools"></i> أدوات متقدمة</h3>
                    <button class="test-button success" onclick="runFullPDFTest()">
                        <i class="fas fa-play"></i> اختبار شامل
                    </button>
                    <button class="test-button warning" onclick="clearResults()">
                        <i class="fas fa-eraser"></i> مسح النتائج
                    </button>
                    <button class="test-button info" onclick="openMainApp()">
                        <i class="fas fa-external-link-alt"></i> فتح التطبيق
                    </button>
                </div>
            </div>

            <!-- معاينة الجدول -->
            <div class="pdf-preview">
                <h4><i class="fas fa-eye"></i> معاينة الجدول المتوقع في PDF</h4>
                <table class="demo-table">
                    <thead>
                        <tr>
                            <th>م</th>
                            <th>الكود</th>
                            <th>الاسم</th>
                            <th>الوظيفة</th>
                            <th>الرقم القومي</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>تاريخ الالتحاق</th>
                        </tr>
                    </thead>
                    <tbody id="demoTableBody">
                        <tr>
                            <td>1</td>
                            <td>EMP001</td>
                            <td>أحمد محمد</td>
                            <td>مطور</td>
                            <td>12345678901234</td>
                            <td>01000000000</td>
                            <td>القاهرة</td>
                            <td>2023-01-01</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>EMP002</td>
                            <td>فاطمة علي</td>
                            <td>محاسبة</td>
                            <td>98765432109876</td>
                            <td>01111111111</td>
                            <td>الجيزة</td>
                            <td>2023-02-01</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="results" id="results">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="resultsList">
                    <p>جاهز لبدء اختبارات تصدير PDF...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            testResults.push({ message, type, timestamp });
            
            const resultsList = document.getElementById('resultsList');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            resultItem.innerHTML = `
                <span class="status-indicator status-${type}"></span>
                <strong>[${timestamp}]</strong> ${message}
            `;
            
            resultsList.appendChild(resultItem);
            resultsList.scrollTop = resultsList.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            document.getElementById('resultsList').innerHTML = '<p>تم مسح النتائج...</p>';
            addResult('تم مسح جميع النتائج', 'warning');
        }

        function testSystemLoad() {
            addResult('🔍 بدء اختبار تحميل النظام...', 'info');
            
            // اختبار المتغيرات العامة
            if (typeof employees !== 'undefined') {
                addResult('✅ متغير employees محمل بنجاح', 'success');
                addResult(`📊 عدد الموظفين الحالي: ${employees.length}`, 'info');
            } else {
                addResult('❌ متغير employees غير محمل', 'error');
            }

            // اختبار دوال النظام الأساسي
            const basicFunctions = ['populateEmployees', 'saveEmployeesToLocalStorage', 'showTemporaryMessage'];
            basicFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: موجودة`, 'success');
                } else {
                    addResult(`❌ دالة ${funcName}: مفقودة`, 'error');
                }
            });

            addResult('✅ اكتمل اختبار تحميل النظام', 'success');
        }

        function testPDFLibraries() {
            addResult('🔍 اختبار مكتبات PDF...', 'info');
            
            // اختبار مكتبة jsPDF
            if (typeof window.jsPDF !== 'undefined') {
                addResult('✅ مكتبة jsPDF محملة بنجاح', 'success');
                
                try {
                    const { jsPDF } = window.jsPDF;
                    const testDoc = new jsPDF();
                    addResult('✅ يمكن إنشاء مستند PDF تجريبي', 'success');
                } catch (error) {
                    addResult(`❌ خطأ في إنشاء مستند PDF: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ مكتبة jsPDF غير محملة', 'error');
            }

            // اختبار مكتبة autoTable
            if (typeof window.jsPDF !== 'undefined' && window.jsPDF.jsPDF.prototype.autoTable) {
                addResult('✅ مكتبة autoTable محملة بنجاح', 'success');
            } else {
                addResult('❌ مكتبة autoTable غير محملة', 'error');
            }

            addResult('✅ اكتمل اختبار مكتبات PDF', 'success');
        }

        function testEmployeeData() {
            addResult('🔍 اختبار بيانات الموظفين...', 'info');
            
            if (typeof employees !== 'undefined' && Array.isArray(employees)) {
                addResult(`📊 عدد الموظفين: ${employees.length}`, 'info');
                
                if (employees.length > 0) {
                    const firstEmployee = employees[0];
                    const requiredFields = ['id', 'name', 'employeeCode', 'position'];
                    
                    requiredFields.forEach(field => {
                        if (firstEmployee.hasOwnProperty(field)) {
                            addResult(`✅ حقل ${field}: موجود`, 'success');
                        } else {
                            addResult(`❌ حقل ${field}: مفقود`, 'error');
                        }
                    });
                } else {
                    addResult('⚠️ لا توجد بيانات موظفين للاختبار', 'warning');
                }
            } else {
                addResult('❌ مصفوفة الموظفين غير صحيحة', 'error');
            }
        }

        function testPDFFunction() {
            addResult('🔍 اختبار دالة تصدير PDF...', 'info');
            
            if (typeof exportEmployeesToPDF === 'function') {
                addResult('✅ دالة exportEmployeesToPDF متاحة', 'success');
            } else {
                addResult('❌ دالة exportEmployeesToPDF غير متاحة', 'error');
            }
        }

        function testPDFGeneration() {
            addResult('🔍 اختبار إنشاء PDF...', 'info');
            
            if (typeof window.jsPDF === 'undefined') {
                addResult('❌ مكتبة jsPDF غير محملة', 'error');
                return;
            }

            try {
                const { jsPDF } = window.jsPDF;
                const doc = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });

                // اختبار إضافة نص
                doc.setFont('helvetica');
                doc.setFontSize(16);
                doc.text('Test PDF Document', 148, 20, { align: 'center' });
                
                addResult('✅ تم إنشاء مستند PDF تجريبي بنجاح', 'success');

                // اختبار إنشاء جدول
                if (doc.autoTable) {
                    const testHeaders = ['Serial', 'Name', 'Position'];
                    const testData = [
                        ['1', 'Test Employee', 'Test Position']
                    ];

                    doc.autoTable({
                        head: [testHeaders],
                        body: testData,
                        startY: 30
                    });

                    addResult('✅ تم إنشاء جدول تجريبي بنجاح', 'success');
                } else {
                    addResult('❌ دالة autoTable غير متاحة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في إنشاء PDF: ${error.message}`, 'error');
            }
        }

        function testTableStructure() {
            addResult('🔍 اختبار هيكل الجدول...', 'info');
            
            const expectedColumns = [
                'Serial', 'Code', 'Name', 'Position', 
                'National ID', 'Phone', 'Address', 'Join Date'
            ];

            addResult(`📋 الأعمدة المتوقعة: ${expectedColumns.length}`, 'info');
            expectedColumns.forEach((col, index) => {
                addResult(`${index + 1}. ${col}`, 'info');
            });

            addResult('✅ تم فحص هيكل الجدول', 'success');
        }

        function createTestData() {
            addResult('🔍 إنشاء بيانات تجريبية...', 'info');
            
            const testEmployees = [
                {
                    id: 9001,
                    employeeCode: 'PDF001',
                    name: 'موظف تجريبي للPDF',
                    position: 'مختبر PDF',
                    nationalId: '12345678901234',
                    phone: '01000000000',
                    address: 'عنوان تجريبي',
                    joinDate: '2023-01-01'
                },
                {
                    id: 9002,
                    employeeCode: 'PDF002',
                    name: 'موظف تجريبي ثاني',
                    position: 'مختبر ثاني',
                    nationalId: '98765432109876',
                    phone: '01111111111',
                    address: 'عنوان تجريبي ثاني',
                    joinDate: '2023-02-01'
                }
            ];

            // إزالة البيانات التجريبية السابقة
            if (typeof employees !== 'undefined') {
                const initialCount = employees.length;
                employees = employees.filter(emp => !emp.employeeCode.startsWith('PDF'));
                
                // إضافة البيانات الجديدة
                employees.push(...testEmployees);
                
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                
                if (typeof populateEmployees === 'function') {
                    populateEmployees();
                }

                addResult(`✅ تم إنشاء ${testEmployees.length} موظف تجريبي`, 'success');
                addResult(`📊 إجمالي الموظفين الآن: ${employees.length}`, 'info');
            } else {
                addResult('❌ لا يمكن إضافة البيانات التجريبية', 'error');
            }
        }

        function exportTestPDF() {
            addResult('🔍 اختبار تصدير PDF...', 'info');
            
            if (typeof exportEmployeesToPDF === 'function') {
                try {
                    exportEmployeesToPDF();
                    addResult('✅ تم استدعاء دالة تصدير PDF بنجاح', 'success');
                    addResult('📄 يجب أن يتم تحميل ملف PDF الآن', 'info');
                } catch (error) {
                    addResult(`❌ خطأ في تصدير PDF: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ دالة تصدير PDF غير متاحة', 'error');
            }
        }

        function testSerialColumn() {
            addResult('🔍 اختبار عمود المسلسل...', 'info');
            
            if (typeof employees !== 'undefined' && employees.length > 0) {
                addResult('✅ سيتم إضافة عمود المسلسل تلقائياً', 'success');
                
                employees.forEach((emp, index) => {
                    addResult(`${index + 1}. ${emp.name || emp.employeeCode}`, 'info');
                });
                
                addResult(`📊 إجمالي الصفوف مع المسلسل: ${employees.length}`, 'success');
            } else {
                addResult('⚠️ لا توجد بيانات لاختبار عمود المسلسل', 'warning');
            }
        }

        function runFullPDFTest() {
            addResult('🚀 بدء الاختبار الشامل لتصدير PDF...', 'info');
            
            clearResults();
            
            setTimeout(() => testSystemLoad(), 500);
            setTimeout(() => testPDFLibraries(), 1000);
            setTimeout(() => testEmployeeData(), 1500);
            setTimeout(() => testPDFFunction(), 2000);
            setTimeout(() => testPDFGeneration(), 2500);
            setTimeout(() => testTableStructure(), 3000);
            setTimeout(() => createTestData(), 3500);
            setTimeout(() => testSerialColumn(), 4000);
            setTimeout(() => exportTestPDF(), 4500);
            
            setTimeout(() => {
                addResult('🎉 اكتمل الاختبار الشامل لتصدير PDF!', 'success');
                
                const successCount = testResults.filter(r => r.type === 'success').length;
                const errorCount = testResults.filter(r => r.type === 'error').length;
                const warningCount = testResults.filter(r => r.type === 'warning').length;
                
                addResult(`📊 ملخص النتائج:`, 'info');
                addResult(`✅ نجح: ${successCount}`, 'success');
                addResult(`❌ فشل: ${errorCount}`, errorCount > 0 ? 'error' : 'info');
                addResult(`⚠️ تحذيرات: ${warningCount}`, warningCount > 0 ? 'warning' : 'info');
                
                const successRate = Math.round((successCount / (successCount + errorCount + warningCount)) * 100);
                addResult(`🎯 معدل النجاح: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
            }, 5000);
        }

        function openMainApp() {
            addResult('🔗 فتح التطبيق الرئيسي...', 'info');
            window.open('index.html', '_blank');
        }

        // تحميل تلقائي عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 تم تحميل صفحة اختبار تصدير PDF', 'success');
            addResult('🚀 جاهز لبدء الاختبارات...', 'info');
            
            // اختبار سريع تلقائي
            setTimeout(() => {
                if (typeof exportEmployeesToPDF === 'function') {
                    addResult('✅ دالة تصدير PDF محملة ومتاحة', 'success');
                } else {
                    addResult('❌ دالة تصدير PDF غير محملة', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
