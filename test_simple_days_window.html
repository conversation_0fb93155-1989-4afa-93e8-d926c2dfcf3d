<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة حساب الأيام البسيطة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #1e7e34;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.warning:hover {
            background: #e0a800;
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        .log-entry.info { color: #007bff; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-top: 20px;
        }

        .stats h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .stats .number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>اختبار نافذة حساب الأيام البسيطة</h1>
            <p>اختبار النافذة المحدثة بتصميم بسيط وألوان قليلة</p>
        </div>

        <div class="content">
            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>إعداد البيانات</h3>
                <div class="grid">
                    <button class="btn success" onclick="createTestEmployee()">إنشاء موظف تجريبي</button>
                    <button class="btn" onclick="createTestProjects()">إنشاء مشروعات</button>
                    <button class="btn warning" onclick="checkSystem()">فحص النظام</button>
                </div>
            </div>

            <!-- قسم الاختبارات -->
            <div class="test-section">
                <h3>اختبارات النافذة</h3>
                <div class="grid">
                    <button class="btn" onclick="testOpenWindow()">فتح النافذة</button>
                    <button class="btn" onclick="testFillData()">ملء البيانات</button>
                    <button class="btn success" onclick="testSave()">اختبار الحفظ</button>
                    <button class="btn" onclick="testRecords()">عرض السجلات</button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn success" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
                    <button class="btn warning" onclick="testCalculations()">اختبار الحسابات</button>
                    <button class="btn danger" onclick="clearData()">مسح البيانات</button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لبدء الاختبارات...
            </div>

            <!-- إحصائيات -->
            <div class="stats">
                <h4>إحصائيات الاختبار</h4>
                <div class="grid">
                    <div>
                        <div class="number" id="testsRun">0</div>
                        <div>اختبارات تم تشغيلها</div>
                    </div>
                    <div>
                        <div class="number" id="testsSuccess">0</div>
                        <div>اختبارات ناجحة</div>
                    </div>
                    <div>
                        <div class="number" id="testsFailed">0</div>
                        <div>اختبارات فاشلة</div>
                    </div>
                    <div>
                        <div class="number" id="successRate">0%</div>
                        <div>معدل النجاح</div>
                    </div>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry info">تم تحميل صفحة اختبار نافذة حساب الأيام البسيطة</div>
                <div class="log-entry info">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsSuccess').textContent = testsSuccess;
            document.getElementById('testsFailed').textContent = testsFailed;
            
            const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function runTest(testName, testFunction) {
            testsRun++;
            addLog(`بدء اختبار: ${testName}`, 'info');
            
            try {
                const result = testFunction();
                if (result !== false) {
                    testsSuccess++;
                    addLog(`نجح اختبار: ${testName}`, 'success');
                } else {
                    testsFailed++;
                    addLog(`فشل اختبار: ${testName}`, 'error');
                }
            } catch (error) {
                testsFailed++;
                addLog(`خطأ في اختبار ${testName}: ${error.message}`, 'error');
            }
            
            updateStats();
        }

        // دوال الاختبار
        function createTestEmployee() {
            runTest('إنشاء موظف تجريبي', () => {
                const testEmployee = {
                    id: 999,
                    name: 'محمد أحمد التجريبي',
                    position: 'مهندس',
                    employmentType: 'monthly',
                    nationalId: '12345678901234',
                    phone: '01234567890',
                    basicSalary: 5000,
                    isActive: true
                };

                if (!employees.find(emp => emp.id === 999)) {
                    employees.push(testEmployee);
                    addLog('تم إنشاء الموظف التجريبي بنجاح', 'success');
                    updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                    return true;
                } else {
                    addLog('الموظف التجريبي موجود بالفعل', 'info');
                    updateStatus('الموظف التجريبي موجود بالفعل', 'info');
                    return true;
                }
            });
        }

        function createTestProjects() {
            runTest('إنشاء مشروعات تجريبية', () => {
                const testProjects = [
                    { id: 201, name: 'مشروع تجريبي 1', status: 'active' },
                    { id: 202, name: 'مشروع تجريبي 2', status: 'active' }
                ];

                testProjects.forEach(project => {
                    if (!projects.find(p => p.id === project.id)) {
                        projects.push(project);
                    }
                });

                addLog('تم إنشاء المشروعات التجريبية بنجاح', 'success');
                updateStatus('تم إنشاء المشروعات التجريبية بنجاح', 'success');
                return true;
            });
        }

        function checkSystem() {
            runTest('فحص النظام', () => {
                const checks = [
                    { name: 'دالة openDaysCalculationModal', test: () => typeof openDaysCalculationModal === 'function' },
                    { name: 'دالة saveDaysCalculation', test: () => typeof saveDaysCalculation === 'function' },
                    { name: 'دالة viewDaysRecords', test: () => typeof viewDaysRecords === 'function' },
                    { name: 'دالة calculateActualDays', test: () => typeof calculateActualDays === 'function' },
                    { name: 'نافذة حساب الأيام', test: () => document.getElementById('daysCalculationModal') !== null },
                    { name: 'وجود موظفين', test: () => employees && employees.length > 0 },
                    { name: 'وجود مشروعات', test: () => projects && projects.length > 0 }
                ];

                let passed = 0;
                checks.forEach(check => {
                    if (check.test()) {
                        addLog(`✓ ${check.name}`, 'success');
                        passed++;
                    } else {
                        addLog(`✗ ${check.name}`, 'error');
                    }
                });

                const status = passed === checks.length ? 'success' : 'warning';
                updateStatus(`فحص النظام: ${passed}/${checks.length} اختبارات نجحت`, status);
                return passed === checks.length;
            });
        }

        function testOpenWindow() {
            runTest('فتح نافذة حساب الأيام', () => {
                if (!employees.find(emp => emp.id === 999)) {
                    addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return false;
                }

                openDaysCalculationModal(999);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysCalculationModal');
                    if (modal && modal.style.display === 'block') {
                        addLog('تم فتح النافذة بنجاح', 'success');
                        updateStatus('تم فتح نافذة حساب الأيام بنجاح', 'success');
                    } else {
                        addLog('فشل في فتح النافذة', 'error');
                        updateStatus('فشل في فتح نافذة حساب الأيام', 'error');
                    }
                }, 500);

                return true;
            });
        }

        function testFillData() {
            runTest('ملء البيانات التجريبية', () => {
                const modal = document.getElementById('daysCalculationModal');
                if (!modal || modal.style.display !== 'block') {
                    addLog('يرجى فتح النافذة أولاً', 'warning');
                    return false;
                }

                // ملء البيانات
                const projectSelect = document.getElementById('daysProject');
                const calculatedDaysInput = document.getElementById('calculatedDays');
                const absenceDaysInput = document.getElementById('absenceDays');
                const overtimeHoursInput = document.getElementById('overtimeHours');

                if (projectSelect && projectSelect.options.length > 1) {
                    projectSelect.selectedIndex = 1;
                }
                if (calculatedDaysInput) calculatedDaysInput.value = '25';
                if (absenceDaysInput) absenceDaysInput.value = '1';
                if (overtimeHoursInput) overtimeHoursInput.value = '5';

                // تشغيل حساب الأيام الفعلية
                if (typeof calculateActualDays === 'function') {
                    calculateActualDays();
                }

                addLog('تم ملء البيانات التجريبية', 'success');
                updateStatus('تم ملء البيانات التجريبية في النافذة', 'success');
                return true;
            });
        }

        function testSave() {
            runTest('اختبار دالة الحفظ', () => {
                const modal = document.getElementById('daysCalculationModal');
                if (!modal || modal.style.display !== 'block') {
                    addLog('يرجى فتح النافذة وملء البيانات أولاً', 'warning');
                    return false;
                }

                const result = saveDaysCalculation(999);
                if (result !== false) {
                    addLog('تم حفظ البيانات بنجاح', 'success');
                    updateStatus('تم اختبار دالة الحفظ بنجاح', 'success');
                    return true;
                } else {
                    addLog('فشل في حفظ البيانات', 'error');
                    updateStatus('فشل في اختبار دالة الحفظ', 'error');
                    return false;
                }
            });
        }

        function testRecords() {
            runTest('اختبار عرض السجلات', () => {
                if (!employees.find(emp => emp.id === 999)) {
                    addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                    return false;
                }

                // فتح النافذة أولاً
                openDaysCalculationModal(999);
                
                setTimeout(() => {
                    viewDaysRecords();
                    addLog('تم اختبار عرض السجلات', 'success');
                    updateStatus('تم اختبار عرض السجلات بنجاح', 'success');
                }, 500);

                return true;
            });
        }

        function testCalculations() {
            runTest('اختبار الحسابات', () => {
                // اختبار دالة حساب الأيام الفعلية
                if (typeof calculateActualDays !== 'function') {
                    addLog('دالة calculateActualDays غير موجودة', 'error');
                    return false;
                }

                // إنشاء حقول وهمية للاختبار
                const testContainer = document.createElement('div');
                testContainer.innerHTML = `
                    <input type="number" id="calculatedDays" value="30">
                    <input type="number" id="absenceDays" value="2">
                    <input type="number" id="actualDaysDisplay" readonly>
                `;
                document.body.appendChild(testContainer);

                calculateActualDays();

                const actualDaysField = document.getElementById('actualDaysDisplay');
                const expectedResult = 28; // 30 - 2
                const actualResult = parseInt(actualDaysField.value);

                document.body.removeChild(testContainer);

                if (actualResult === expectedResult) {
                    addLog(`حساب الأيام الفعلية صحيح: ${actualResult}`, 'success');
                    updateStatus('اختبار الحسابات نجح', 'success');
                    return true;
                } else {
                    addLog(`خطأ في حساب الأيام الفعلية: توقع ${expectedResult} لكن حصل على ${actualResult}`, 'error');
                    updateStatus('اختبار الحسابات فشل', 'error');
                    return false;
                }
            });
        }

        function runAllTests() {
            addLog('بدء تشغيل جميع الاختبارات...', 'info');
            updateStatus('جاري تشغيل جميع الاختبارات...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => checkSystem(), 1500);
            setTimeout(() => testOpenWindow(), 2000);
            setTimeout(() => testFillData(), 3000);
            setTimeout(() => testSave(), 4000);
            setTimeout(() => testRecords(), 5000);
            setTimeout(() => testCalculations(), 6000);

            setTimeout(() => {
                addLog('انتهى تشغيل جميع الاختبارات', 'success');
                const successRate = testsRun > 0 ? Math.round((testsSuccess / testsRun) * 100) : 0;
                updateStatus(`انتهت الاختبارات - معدل النجاح: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
            }, 7000);
        }

        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح الموظفين التجريبيين
                employees = employees.filter(emp => emp.id !== 999);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![201, 202].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 999);
                }

                // إعادة تعيين الإحصائيات
                testsRun = 0;
                testsSuccess = 0;
                testsFailed = 0;
                updateStats();

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('تم مسح جميع البيانات التجريبية', 'warning');
                updateStatus('تم مسح جميع البيانات التجريبية', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة اختبار نافذة حساب الأيام البسيطة', 'info');
            updateStatus('جاهز لبدء الاختبارات - اضغط على الأزرار أعلاه', 'info');
        });
    </script>
</body>
</html>
