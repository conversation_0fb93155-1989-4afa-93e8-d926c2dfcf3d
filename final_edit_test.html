<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي - أزرار التعديل والحفظ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .checklist {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 20px 0;
        }

        .checklist-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .checklist-item:last-child {
            border-bottom: none;
        }

        .checklist-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .checklist-icon.success {
            background: #28a745;
        }

        .checklist-icon.pending {
            background: #6c757d;
        }

        .checklist-icon.error {
            background: #dc3545;
        }

        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bbdefb;
            margin: 20px 0;
        }

        .instructions h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e3f2fd;
        }

        .step-number {
            background: #1976d2;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> الاختبار النهائي</h1>
            <p>اختبار شامل لأزرار التعديل والحفظ في نظام إدارة الأيام</p>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-list-ol"></i> خطوات الاختبار النهائي:</h4>

            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء البيانات التجريبية</strong> - إنشاء موظف وسجلات أيام للاختبار
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة السجلات</strong> - فتح نافذة سجلات الأيام للموظف التجريبي
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>اختبار التعديل المباشر</strong> - تجربة التعديل المباشر في الجدول
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>اختبار التعديل بالنافذة</strong> - تجربة التعديل عبر نافذة منفصلة
            </div>

            <div class="step">
                <span class="step-number">5</span>
                <strong>اختبار الحفظ</strong> - التأكد من حفظ التغييرات بنجاح
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button success" onclick="runCompleteTest()">
                <i class="fas fa-play"></i> تشغيل الاختبار الشامل
            </button>

            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
            </button>

            <button class="test-button warning" onclick="openRecordsWindow()">
                <i class="fas fa-table"></i> فتح نافذة السجلات
            </button>

            <button class="test-button danger" onclick="openOriginalApp()">
                <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
            </button>
        </div>

        <div class="checklist">
            <h4><i class="fas fa-tasks"></i> قائمة التحقق:</h4>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check1">1</div>
                <div>
                    <strong>دوال localStorage موجودة</strong>
                    <br><small>saveDaysDataToLocalStorage و loadDaysDataFromLocalStorage</small>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check2">2</div>
                <div>
                    <strong>دوال التعديل المباشر موجودة</strong>
                    <br><small>updateRecordField و saveAllRecordsChanges و resetAllChanges</small>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check3">3</div>
                <div>
                    <strong>دوال التعديل بالنافذة موجودة</strong>
                    <br><small>editDaysRecordFromModal و saveEditedRecord</small>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check4">4</div>
                <div>
                    <strong>دوال التبديل بين الوضعين موجودة</strong>
                    <br><small>toggleInlineEditMode و refreshDaysRecordsTable</small>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check5">5</div>
                <div>
                    <strong>أزرار التعديل تظهر في الوضع التقليدي</strong>
                    <br><small>زر "تعديل" يظهر عند إلغاء التعديل المباشر</small>
                </div>
            </div>

            <div class="checklist-item">
                <div class="checklist-icon pending" id="check6">6</div>
                <div>
                    <strong>حفظ التغييرات يعمل بشكل صحيح</strong>
                    <br><small>التغييرات تحفظ في localStorage وتظهر بعد إعادة التحميل</small>
                </div>
            </div>
        </div>

        <div id="status" class="status-card">
            <h4><i class="fas fa-info-circle"></i> الحالة:</h4>
            <p>جاهز لبدء الاختبار النهائي...</p>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status-card ${type}`;

            let icon = 'fas fa-info-circle';
            if (type === 'success') icon = 'fas fa-check-circle';
            else if (type === 'error') icon = 'fas fa-exclamation-circle';
            else if (type === 'warning') icon = 'fas fa-exclamation-triangle';

            statusDiv.innerHTML = `
                <h4><i class="${icon}"></i> الحالة:</h4>
                <p>${message}</p>
            `;
        }

        function updateChecklistItem(itemNumber, status, message = '') {
            const icon = document.getElementById(`check${itemNumber}`);
            if (icon) {
                icon.className = `checklist-icon ${status}`;
                if (status === 'success') {
                    icon.innerHTML = '<i class="fas fa-check"></i>';
                } else if (status === 'error') {
                    icon.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    icon.innerHTML = itemNumber;
                }
            }
        }

        function createTestData() {
            updateStatus('جاري إنشاء البيانات التجريبية...', 'warning');

            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 999)) {
                    employees.push({
                        id: 999,
                        name: 'أحمد محمد علي - اختبار نهائي',
                        position: 'مهندس برمجيات',
                        employeeCode: 'FINAL001',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                }

                // إنشاء سجلات أيام تجريبية
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 999,
                        projectName: 'مشروع الاختبار النهائي',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 30,
                        absenceDays: 2,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 999,
                        projectName: 'مشروع التطوير المتقدم',
                        startDate: '2024-02-01',
                        endDate: '2024-02-28',
                        calculatedDays: 28,
                        absenceDays: 1,
                        overtimeHours: 15,
                        createdAt: new Date().toISOString()
                    }
                ];

                testRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });

                // حفظ البيانات
                saveDaysDataToLocalStorage();

                updateStatus('تم إنشاء البيانات التجريبية بنجاح! (موظف واحد + سجلين)', 'success');

            } catch (error) {
                updateStatus('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 999)) {
                    updateStatus('يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }

                viewDaysRecords(999);
                updateStatus('تم فتح نافذة السجلات! جرب التبديل بين وضعي التعديل', 'success');

            } catch (error) {
                updateStatus('خطأ في فتح نافذة السجلات: ' + error.message, 'error');
            }
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        function runCompleteTest() {
            updateStatus('بدء الاختبار الشامل...', 'warning');

            // اختبار 1: دوال localStorage
            setTimeout(() => {
                if (typeof saveDaysDataToLocalStorage === 'function' && typeof loadDaysDataFromLocalStorage === 'function') {
                    updateChecklistItem(1, 'success');
                } else {
                    updateChecklistItem(1, 'error');
                }
            }, 500);

            // اختبار 2: دوال التعديل المباشر
            setTimeout(() => {
                if (typeof updateRecordField === 'function' && typeof saveAllRecordsChanges === 'function' && typeof resetAllChanges === 'function') {
                    updateChecklistItem(2, 'success');
                } else {
                    updateChecklistItem(2, 'error');
                }
            }, 1000);

            // اختبار 3: دوال التعديل بالنافذة
            setTimeout(() => {
                if (typeof editDaysRecordFromModal === 'function' && typeof saveEditedRecord === 'function') {
                    updateChecklistItem(3, 'success');
                } else {
                    updateChecklistItem(3, 'error');
                }
            }, 1500);

            // اختبار 4: دوال التبديل
            setTimeout(() => {
                if (typeof toggleInlineEditMode === 'function' && typeof refreshDaysRecordsTable === 'function') {
                    updateChecklistItem(4, 'success');
                } else {
                    updateChecklistItem(4, 'error');
                }
            }, 2000);

            // اختبار 5: إنشاء البيانات واختبار النافذة
            setTimeout(() => {
                createTestData();
                setTimeout(() => {
                    openRecordsWindow();

                    // فحص أزرار التعديل بعد فتح النافذة
                    setTimeout(() => {
                        const modal = document.getElementById('daysRecordsModal');
                        if (modal) {
                            // تعطيل التعديل المباشر
                            const checkbox = modal.querySelector('#inlineEditMode');
                            if (checkbox) {
                                checkbox.checked = false;
                                toggleInlineEditMode();

                                setTimeout(() => {
                                    const editButtons = modal.querySelectorAll('.btn-edit');
                                    if (editButtons.length > 0) {
                                        updateChecklistItem(5, 'success');
                                    } else {
                                        updateChecklistItem(5, 'error');
                                    }
                                }, 500);
                            }
                        }
                    }, 1000);
                }, 1000);
            }, 2500);

            // اختبار 6: حفظ البيانات
            setTimeout(() => {
                try {
                    const testData = { test: 'final' };
                    localStorage.setItem('testFinal', JSON.stringify(testData));
                    const retrieved = JSON.parse(localStorage.getItem('testFinal'));

                    if (retrieved && retrieved.test === 'final') {
                        updateChecklistItem(6, 'success');
                        localStorage.removeItem('testFinal');
                    } else {
                        updateChecklistItem(6, 'error');
                    }
                } catch (error) {
                    updateChecklistItem(6, 'error');
                }
            }, 5000);

            // النتيجة النهائية
            setTimeout(() => {
                updateStatus('تم الانتهاء من الاختبار الشامل! تحقق من قائمة التحقق أعلاه', 'success');
            }, 6000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('تم تحميل صفحة الاختبار النهائي - جاهز للبدء!', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                updateStatus('تشغيل اختبار سريع تلقائي...', 'warning');

                // فحص الدوال الأساسية
                const basicFunctions = [
                    'saveDaysDataToLocalStorage',
                    'loadDaysDataFromLocalStorage',
                    'updateRecordField',
                    'editDaysRecordFromModal',
                    'toggleInlineEditMode'
                ];

                let foundFunctions = 0;
                basicFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        foundFunctions++;
                    }
                });

                const percentage = (foundFunctions / basicFunctions.length) * 100;

                if (percentage >= 80) {
                    updateStatus(`النظام جاهز! تم العثور على ${foundFunctions}/${basicFunctions.length} دالة (${percentage.toFixed(0)}%)`, 'success');
                } else {
                    updateStatus(`تحذير: تم العثور على ${foundFunctions}/${basicFunctions.length} دالة فقط (${percentage.toFixed(0)}%)`, 'warning');
                }
            }, 2000);
        });
    </script>
</body>
</html>
