<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - نافذة الحوافز والمكافآت</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 26px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: calc(50% - 20px);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #27ae60;
        }

        .status.error {
            background: #e74c3c;
        }

        .status.warning {
            background: #f39c12;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> اختبار نهائي للحوافز والمكافآت</h1>
            <p>التأكد من عمل النافذة بشكل صحيح</p>
        </div>

        <div class="status" id="status">
            جاهز للاختبار النهائي...
        </div>

        <div class="content">
            <div class="grid">
                <button class="test-button" onclick="testOriginal()">
                    <i class="fas fa-play"></i> اختبار النافذة الأصلية
                </button>
                
                <button class="test-button success" onclick="testAlternative()">
                    <i class="fas fa-rocket"></i> اختبار النافذة البديلة
                </button>
                
                <button class="test-button warning" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
                
                <button class="test-button danger" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <button class="test-button" onclick="openMainApp()" style="width: 80%;">
                    <i class="fas fa-external-link-alt"></i> فتح التطبيق الرئيسي
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الرئيسي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#333';
            if (type === 'success') color = '#27ae60';
            else if (type === 'error') color = '#e74c3c';
            else if (type === 'warning') color = '#f39c12';
            else if (type === 'info') color = '#3498db';
            
            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        // دالة لتحديث الحالة
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // اختبار النافذة الأصلية
        function testOriginal() {
            updateStatus('جاري اختبار النافذة الأصلية...', 'warning');
            addResult('🔍 اختبار النافذة الأصلية', 'info');
            
            try {
                if (typeof viewIncentivesRewards !== 'function') {
                    addResult('❌ دالة viewIncentivesRewards غير موجودة', 'error');
                    updateStatus('❌ الدالة الأصلية مفقودة', 'error');
                    return;
                }
                
                if (employees.length === 0) {
                    addResult('⚠️ لا يوجد موظفين - سيتم إنشاء موظف تجريبي', 'warning');
                    createTestEmployee();
                    return;
                }
                
                const employeeId = employees[0].id;
                addResult(`🎯 اختبار مع الموظف: ${employees[0].name}`, 'info');
                
                viewIncentivesRewards(employeeId);
                addResult('✅ تم استدعاء الدالة الأصلية', 'success');
                
                // فحص النافذة بعد فترة
                setTimeout(() => {
                    const modal = document.getElementById('incentivesRewardsModal');
                    if (modal) {
                        addResult('🎉 النافذة الأصلية مفتوحة!', 'success');
                        updateStatus('✅ النافذة الأصلية تعمل!', 'success');
                    } else {
                        addResult('❌ النافذة الأصلية لم تفتح', 'error');
                        updateStatus('❌ النافذة الأصلية لا تعمل', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                addResult(`❌ خطأ في النافذة الأصلية: ${error.message}`, 'error');
                updateStatus('❌ فشل اختبار النافذة الأصلية', 'error');
            }
        }

        // اختبار النافذة البديلة
        function testAlternative() {
            updateStatus('جاري اختبار النافذة البديلة...', 'warning');
            addResult('🚀 اختبار النافذة البديلة', 'info');
            
            try {
                if (typeof viewIncentivesRewardsAlternative !== 'function') {
                    addResult('❌ دالة viewIncentivesRewardsAlternative غير موجودة', 'error');
                    updateStatus('❌ الدالة البديلة مفقودة', 'error');
                    return;
                }
                
                if (employees.length === 0) {
                    addResult('⚠️ لا يوجد موظفين - سيتم إنشاء موظف تجريبي', 'warning');
                    createTestEmployee();
                    return;
                }
                
                const employeeId = employees[0].id;
                addResult(`🎯 اختبار مع الموظف: ${employees[0].name}`, 'info');
                
                viewIncentivesRewardsAlternative(employeeId);
                addResult('✅ تم استدعاء الدالة البديلة', 'success');
                
                // فحص النافذة بعد فترة
                setTimeout(() => {
                    const modal = document.getElementById('incentivesRewardsModal');
                    if (modal) {
                        addResult('🎉 النافذة البديلة مفتوحة!', 'success');
                        updateStatus('✅ النافذة البديلة تعمل!', 'success');
                    } else {
                        addResult('❌ النافذة البديلة لم تفتح', 'error');
                        updateStatus('❌ النافذة البديلة لا تعمل', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addResult(`❌ خطأ في النافذة البديلة: ${error.message}`, 'error');
                updateStatus('❌ فشل اختبار النافذة البديلة', 'error');
            }
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('👤 إنشاء موظف تجريبي...', 'info');
            
            const testEmployee = {
                id: 66666,
                name: "موظف تجريبي نهائي",
                position: "مختبر الحوافز",
                employeeCode: "FINAL_TEST",
                nationalId: "12345678901234",
                phone: "01000000000",
                photo: "https://via.placeholder.com/150/2ecc71/ffffff?text=FINAL",
                employmentType: "monthly",
                salary: { basic: 5000, total: 5000 },
                dateAdded: new Date().toISOString()
            };
            
            const existing = employees.find(emp => emp.id === 66666);
            if (!existing) {
                employees.push(testEmployee);
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                addResult('✅ تم إنشاء الموظف التجريبي', 'success');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            } else {
                addResult('⚠️ الموظف التجريبي موجود بالفعل', 'warning');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // فتح التطبيق الرئيسي
        function openMainApp() {
            window.open('index.html', '_blank');
            addResult('🔗 تم فتح التطبيق الرئيسي في نافذة جديدة', 'info');
        }

        // اختبار تلقائي عند التحميل
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 بدء الاختبار التلقائي...', 'info');
                
                // فحص النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }
                
                // فحص الدوال
                if (typeof viewIncentivesRewards === 'function') {
                    addResult('✅ الدالة الأصلية موجودة', 'success');
                } else {
                    addResult('❌ الدالة الأصلية مفقودة', 'error');
                }
                
                if (typeof viewIncentivesRewardsAlternative === 'function') {
                    addResult('✅ الدالة البديلة موجودة', 'success');
                } else {
                    addResult('❌ الدالة البديلة مفقودة', 'error');
                }
                
                addResult('✅ انتهى الفحص التلقائي', 'success');
                updateStatus('✅ النظام جاهز للاختبار', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
