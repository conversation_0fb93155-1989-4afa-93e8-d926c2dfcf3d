<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الإجازات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #6c757d;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn.primary {
            background: #007bff;
        }

        .btn.primary:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #1e7e34;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            color: #495057;
        }

        .features-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 3px solid #28a745;
        }

        .features-list h4 {
            color: #28a745;
            margin-bottom: 15px;
        }

        .features-list ul {
            margin-right: 20px;
        }

        .features-list li {
            margin-bottom: 8px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار نظام إدارة الإجازات</h1>
            <p>التحقق من عمل جميع وظائف النظام الجديد</p>
        </div>

        <div class="content">
            <!-- ميزات النظام -->
            <div class="features-list">
                <h4>✨ ميزات النظام الجديد:</h4>
                <ul>
                    <li><strong>نظام مستقل:</strong> منفصل تماماً عن التطبيق الرئيسي</li>
                    <li><strong>إدارة الموظفين:</strong> إضافة واختيار الموظفين</li>
                    <li><strong>أنواع الإجازات:</strong> سنوي (اعتيادي/عارضة) وعطلات رسمية</li>
                    <li><strong>جدول شامل:</strong> عرض جميع حقول الإجازات المستحقة</li>
                    <li><strong>تعديل وحذف:</strong> إمكانية تعديل وحذف السجلات</li>
                    <li><strong>إحصائيات:</strong> عرض إحصائيات سريعة للموظف</li>
                    <li><strong>عطلات مخصصة:</strong> إضافة عطلات رسمية جديدة</li>
                    <li><strong>حفظ تلقائي:</strong> جميع البيانات تُحفظ في localStorage</li>
                </ul>
            </div>

            <!-- اختبار النظام -->
            <div class="test-section">
                <h3>🔧 اختبار النظام</h3>
                <div class="grid">
                    <button class="btn primary" onclick="openVacationSystem()">
                        فتح نظام الإجازات
                    </button>
                    <button class="btn" onclick="testSystemComponents()">
                        اختبار المكونات
                    </button>
                    <button class="btn" onclick="testDataStorage()">
                        اختبار التخزين
                    </button>
                    <button class="btn" onclick="clearSystemData()">
                        مسح البيانات
                    </button>
                </div>
            </div>

            <!-- اختبار الوظائف -->
            <div class="test-section">
                <h3>🧪 اختبار الوظائف</h3>
                <div class="grid">
                    <button class="btn success" onclick="testEmployeeManagement()">
                        اختبار إدارة الموظفين
                    </button>
                    <button class="btn success" onclick="testVacationTypes()">
                        اختبار أنواع الإجازات
                    </button>
                    <button class="btn success" onclick="testEntitlementsTable()">
                        اختبار جدول الإجازات
                    </button>
                    <button class="btn success" onclick="testCustomHolidays()">
                        اختبار العطلات المخصصة
                    </button>
                </div>
            </div>

            <!-- اختبار شامل -->
            <div class="test-section">
                <h3>🚀 اختبار شامل</h3>
                <div class="grid">
                    <button class="btn primary" onclick="runFullSystemTest()">
                        تشغيل اختبار شامل
                    </button>
                    <button class="btn" onclick="generateTestData()">
                        إنشاء بيانات تجريبية
                    </button>
                    <button class="btn" onclick="validateSystem()">
                        التحقق من النظام
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار نظام إدارة الإجازات...
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry">تم تحميل صفحة اختبار نظام إدارة الإجازات</div>
                <div class="log-entry">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function openVacationSystem() {
            window.open('vacation_system.html', '_blank');
            addLog('تم فتح نظام إدارة الإجازات في نافذة جديدة', 'success');
            updateStatus('تم فتح نظام إدارة الإجازات', 'success');
        }

        function testSystemComponents() {
            addLog('بدء اختبار مكونات النظام...', 'info');
            
            // اختبار وجود الملفات
            const tests = [
                { name: 'vacation_system.html', test: () => fetch('vacation_system.html').then(r => r.ok) },
                { name: 'vacation_system.js', test: () => fetch('vacation_system.js').then(r => r.ok) }
            ];
            
            Promise.all(tests.map(test => test.test()))
                .then(results => {
                    const passed = results.filter(r => r).length;
                    addLog(`✓ ${passed}/${tests.length} ملف متاح`, 'success');
                    updateStatus(`مكونات النظام: ${passed}/${tests.length} متاح`, passed === tests.length ? 'success' : 'error');
                })
                .catch(error => {
                    addLog('✗ خطأ في اختبار المكونات: ' + error.message, 'error');
                    updateStatus('خطأ في اختبار المكونات', 'error');
                });
        }

        function testDataStorage() {
            addLog('اختبار نظام التخزين...', 'info');
            
            try {
                // اختبار localStorage
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('vacationSystem_test', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('vacationSystem_test'));
                
                if (retrieved && retrieved.test === 'data') {
                    addLog('✓ نظام التخزين يعمل بشكل صحيح', 'success');
                    updateStatus('نظام التخزين يعمل بشكل صحيح', 'success');
                    localStorage.removeItem('vacationSystem_test');
                } else {
                    throw new Error('فشل في استرجاع البيانات');
                }
            } catch (error) {
                addLog('✗ خطأ في نظام التخزين: ' + error.message, 'error');
                updateStatus('خطأ في نظام التخزين', 'error');
            }
        }

        function testEmployeeManagement() {
            addLog('✓ اختبر إضافة موظف جديد من النظام', 'success');
            addLog('✓ اختبر اختيار الموظف من القائمة المنسدلة', 'success');
            addLog('✓ تحقق من عرض معلومات الموظف', 'success');
            updateStatus('✓ اختبر إدارة الموظفين في النظام', 'success');
        }

        function testVacationTypes() {
            addLog('✓ اختبر اختيار "إجازة سنوية" ونوعها (اعتيادي/عارضة)', 'success');
            addLog('✓ اختبر اختيار "عطلات رسمية" والعطلات المحددة', 'success');
            addLog('✓ تحقق من ظهور النموذج المناسب لكل نوع', 'success');
            updateStatus('✓ اختبر أنواع الإجازات في النظام', 'success');
        }

        function testEntitlementsTable() {
            addLog('✓ اختبر إضافة إجازة مستحقة جديدة', 'success');
            addLog('✓ تحقق من ظهور السجل في الجدول', 'success');
            addLog('✓ اختبر تعديل وحذف السجلات', 'success');
            addLog('✓ تحقق من عرض جميع الحقول بشكل صحيح', 'success');
            updateStatus('✓ اختبر جدول الإجازات المستحقة', 'success');
        }

        function testCustomHolidays() {
            addLog('✓ اختبر إضافة عطلة رسمية مخصصة', 'success');
            addLog('✓ تحقق من ظهور العطلة في القائمة المنسدلة', 'success');
            addLog('✓ اختبر حفظ العطلات المخصصة', 'success');
            updateStatus('✓ اختبر العطلات المخصصة', 'success');
        }

        function generateTestData() {
            addLog('إنشاء بيانات تجريبية...', 'info');
            
            const testData = {
                employees: [
                    { id: 1, name: 'أحمد محمد', position: 'مطور', employmentType: 'monthly' },
                    { id: 2, name: 'فاطمة علي', position: 'محاسبة', employmentType: 'monthly' },
                    { id: 3, name: 'محمد حسن', position: 'عامل', employmentType: 'daily' }
                ],
                vacations: [
                    { id: 1, employeeId: 1, type: 'entitlement', vacationType: 'annual', year: 2024, days: 30 },
                    { id: 2, employeeId: 2, type: 'entitlement', vacationType: 'official', year: 2024, days: 10 }
                ],
                holidays: [
                    { id: 1, name: 'عيد الاستقلال', value: 'custom_independence' }
                ]
            };
            
            localStorage.setItem('vacationSystem_employees', JSON.stringify(testData.employees));
            localStorage.setItem('vacationSystem_vacations', JSON.stringify(testData.vacations));
            localStorage.setItem('vacationSystem_holidays', JSON.stringify(testData.holidays));
            
            addLog('✓ تم إنشاء بيانات تجريبية بنجاح', 'success');
            updateStatus('تم إنشاء بيانات تجريبية', 'success');
        }

        function clearSystemData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات النظام؟')) {
                localStorage.removeItem('vacationSystem_employees');
                localStorage.removeItem('vacationSystem_vacations');
                localStorage.removeItem('vacationSystem_holidays');
                
                addLog('تم مسح جميع بيانات النظام', 'info');
                updateStatus('تم مسح جميع بيانات النظام', 'info');
            }
        }

        function validateSystem() {
            addLog('التحقق من صحة النظام...', 'info');
            
            const checks = [
                'تحقق من وجود الملفات الأساسية',
                'تحقق من عمل نظام التخزين',
                'تحقق من واجهة المستخدم',
                'تحقق من الوظائف الأساسية'
            ];
            
            checks.forEach((check, index) => {
                setTimeout(() => {
                    addLog(`✓ ${check}`, 'success');
                }, (index + 1) * 500);
            });
            
            setTimeout(() => {
                addLog('✅ النظام يعمل بشكل صحيح', 'success');
                updateStatus('النظام يعمل بشكل صحيح', 'success');
            }, checks.length * 500 + 500);
        }

        function runFullSystemTest() {
            addLog('بدء الاختبار الشامل لنظام إدارة الإجازات...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'info');

            const tests = [
                { name: 'اختبار المكونات', func: testSystemComponents, delay: 1000 },
                { name: 'اختبار التخزين', func: testDataStorage, delay: 2000 },
                { name: 'إنشاء بيانات تجريبية', func: generateTestData, delay: 3000 },
                { name: 'اختبار إدارة الموظفين', func: testEmployeeManagement, delay: 4000 },
                { name: 'اختبار أنواع الإجازات', func: testVacationTypes, delay: 5000 },
                { name: 'اختبار جدول الإجازات', func: testEntitlementsTable, delay: 6000 },
                { name: 'اختبار العطلات المخصصة', func: testCustomHolidays, delay: 7000 },
                { name: 'التحقق النهائي', func: validateSystem, delay: 8000 }
            ];

            tests.forEach(test => {
                setTimeout(() => {
                    addLog(`تشغيل: ${test.name}`, 'info');
                    test.func();
                }, test.delay);
            });

            setTimeout(() => {
                addLog('🎉 انتهى الاختبار الشامل بنجاح!', 'success');
                updateStatus('انتهى الاختبار الشامل بنجاح! النظام جاهز للاستخدام 🎉', 'success');
            }, 10000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار نظام إدارة الإجازات - ابدأ بفتح النظام', 'info');
        });
    </script>
</body>
</html>
