<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التعديل الكامل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .button.primary { background: #007bff; }
        .button.warning { background: #ffc107; color: #212529; }
        .button.danger { background: #dc3545; }
        .button.info { background: #17a2b8; }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .test-step {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        
        .test-step.success { border-left-color: #28a745; }
        .test-step.error { border-left-color: #dc3545; }
        .test-step.warning { border-left-color: #ffc107; }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .system-summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .system-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        
        .system-item:last-child { border-bottom: none; }
        
        .system-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .function-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .function-card.available {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        
        .function-card.missing {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار نظام التعديل الكامل</h1>
            <p>اختبار شامل لجميع دوال التعديل والحفظ في نافذة السجلات</p>
        </div>

        <div class="system-summary">
            <h4>🛠️ النظام الجديد المطبق:</h4>
            
            <div class="system-item">
                <div class="system-icon">✓</div>
                <div>
                    <strong>دوال التعديل المباشر</strong>
                    <br><small>updateRecordField, saveAllRecordsChanges, resetAllChanges</small>
                </div>
            </div>
            
            <div class="system-item">
                <div class="system-icon">✓</div>
                <div>
                    <strong>نظام تتبع التغييرات</strong>
                    <br><small>متغير recordsChanges لتتبع جميع التعديلات</small>
                </div>
            </div>
            
            <div class="system-item">
                <div class="system-icon">✓</div>
                <div>
                    <strong>الحفظ التلقائي والیدوي</strong>
                    <br><small>إمكانية التبديل بين الحفظ الفوري والحفظ اليدوي</small>
                </div>
            </div>
            
            <div class="system-item">
                <div class="system-icon">✓</div>
                <div>
                    <strong>واجهة مستخدم محسنة</strong>
                    <br><small>زر حفظ ديناميكي وتمييز الصفوف المعدلة</small>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button danger" onclick="runCompleteSystemTest()">
                🚀 اختبار شامل
            </button>
            
            <button class="button" onclick="createTestEmployee()">
                👤 إنشاء موظف
            </button>
            
            <button class="button warning" onclick="openRecordsWindow()">
                📝 فتح نافذة السجلات
            </button>
            
            <button class="button primary" onclick="testFunctions()">
                🔍 فحص الدوال
            </button>
            
            <button class="button info" onclick="testEditAndSave()">
                💾 اختبار التعديل والحفظ
            </button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار الشامل...
        </div>

        <div id="functionsStatus" class="functions-grid" style="display: none;">
            <!-- سيتم ملؤها ديناميكياً -->
        </div>

        <div id="steps"></div>

        <div class="console" id="console">
            [COMPLETE_EDIT_SYSTEM_TEST] اختبار نظام التعديل الكامل جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function addStep(message, type = 'info') {
            const stepsDiv = document.getElementById('steps');
            const stepDiv = document.createElement('div');
            stepDiv.className = `test-step ${type}`;
            stepDiv.innerHTML = message;
            stepsDiv.appendChild(stepDiv);
        }

        function createTestEmployee() {
            addLog('إنشاء موظف تجريبي...');
            
            try {
                if (!employees.find(emp => emp.id === 888)) {
                    employees.push({
                        id: 888,
                        name: 'محمد علي - اختبار النظام الكامل',
                        position: 'مطور نظم',
                        employeeCode: 'COMPLETE888',
                        employmentType: 'monthly',
                        basicSalary: 8000
                    });
                    
                    // إضافة بعض السجلات التجريبية
                    const testRecords = [
                        {
                            id: Date.now() + 1,
                            employeeId: 888,
                            projectName: 'مشروع اختبار التعديل 1',
                            startDate: '2024-01-01',
                            endDate: '2024-01-31',
                            calculatedDays: 25,
                            absenceDays: 2,
                            overtimeHours: 8,
                            actualDays: 23,
                            createdAt: new Date().toISOString()
                        },
                        {
                            id: Date.now() + 2,
                            employeeId: 888,
                            projectName: 'مشروع اختبار التعديل 2',
                            startDate: '2024-02-01',
                            endDate: '2024-02-29',
                            calculatedDays: 28,
                            absenceDays: 1,
                            overtimeHours: 12,
                            actualDays: 27,
                            createdAt: new Date().toISOString()
                        }
                    ];
                    
                    testRecords.forEach(record => {
                        employeeDaysData.push(record);
                    });
                    
                    saveDaysDataToLocalStorage();
                    
                    addStep('✅ تم إنشاء الموظف التجريبي مع سجلات الأيام', 'success');
                    addLog('✓ تم إنشاء موظف تجريبي مع سجلات');
                    updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                } else {
                    addStep('⚠️ الموظف التجريبي موجود مسبقاً', 'warning');
                    updateStatus('الموظف التجريبي موجود مسبقاً', 'info');
                }
            } catch (error) {
                addStep('❌ فشل في إنشاء الموظف: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في إنشاء الموظف: ' + error.message, 'error');
            }
        }

        function testFunctions() {
            addLog('🔍 فحص وجود الدوال المطلوبة...');
            
            const requiredFunctions = [
                { name: 'updateRecordField', desc: 'تحديث حقل في السجل' },
                { name: 'saveAllRecordsChanges', desc: 'حفظ جميع التغييرات' },
                { name: 'resetAllChanges', desc: 'إعادة تعيين التغييرات' },
                { name: 'updateSaveButton', desc: 'تحديث زر الحفظ' },
                { name: 'toggleAutoSave', desc: 'تبديل الحفظ التلقائي' },
                { name: 'saveRecordChanges', desc: 'حفظ تغييرات سجل واحد' },
                { name: 'viewDaysRecords', desc: 'عرض سجلات الأيام' },
                { name: 'refreshDaysRecordsModal', desc: 'تحديث نافذة السجلات' }
            ];
            
            const functionsStatusDiv = document.getElementById('functionsStatus');
            functionsStatusDiv.style.display = 'grid';
            functionsStatusDiv.innerHTML = '';
            
            let availableCount = 0;
            
            requiredFunctions.forEach(func => {
                const isAvailable = typeof window[func.name] === 'function';
                if (isAvailable) availableCount++;
                
                const card = document.createElement('div');
                card.className = `function-card ${isAvailable ? 'available' : 'missing'}`;
                card.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 5px;">
                        ${isAvailable ? '✅' : '❌'} ${func.name}
                    </div>
                    <div style="font-size: 12px; color: #6c757d;">
                        ${func.desc}
                    </div>
                `;
                
                functionsStatusDiv.appendChild(card);
                
                addLog(`${isAvailable ? '✓' : '✗'} ${func.name}: ${isAvailable ? 'متاحة' : 'مفقودة'}`);
            });
            
            const percentage = (availableCount / requiredFunctions.length) * 100;
            
            if (percentage === 100) {
                addStep(`✅ جميع الدوال متاحة (${availableCount}/${requiredFunctions.length})`, 'success');
                updateStatus('✅ جميع الدوال المطلوبة متاحة!', 'success');
            } else if (percentage >= 75) {
                addStep(`⚠️ معظم الدوال متاحة (${availableCount}/${requiredFunctions.length})`, 'warning');
                updateStatus(`⚠️ ${availableCount}/${requiredFunctions.length} دالة متاحة`, 'warning');
            } else {
                addStep(`❌ دوال مفقودة (${availableCount}/${requiredFunctions.length})`, 'error');
                updateStatus(`❌ ${requiredFunctions.length - availableCount} دالة مفقودة`, 'error');
            }
        }

        function openRecordsWindow() {
            addLog('فتح نافذة السجلات...');
            
            try {
                if (!employees.find(emp => emp.id === 888)) {
                    addStep('❌ الموظف التجريبي غير موجود - يرجى إنشاؤه أولاً', 'error');
                    updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                viewDaysRecords(888);
                
                setTimeout(() => {
                    const modal = document.getElementById('daysRecordsModal');
                    if (modal) {
                        addStep('✅ تم فتح نافذة السجلات بنجاح', 'success');
                        addLog('✓ تم فتح النافذة بنجاح');
                        updateStatus('تم فتح نافذة السجلات - الآن يمكنك اختبار التعديل', 'success');
                    } else {
                        addStep('❌ فشل في فتح النافذة', 'error');
                        addLog('✗ فشل في فتح النافذة');
                        updateStatus('فشل في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addStep('❌ خطأ في فتح النافذة: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function testEditAndSave() {
            addLog('🧪 اختبار التعديل والحفظ...');
            
            const modal = document.getElementById('daysRecordsModal');
            if (!modal) {
                addStep('❌ النافذة غير مفتوحة - يرجى فتحها أولاً', 'error');
                updateStatus('يرجى فتح نافذة السجلات أولاً', 'error');
                return;
            }
            
            // البحث عن سجل للتعديل
            const testRecord = employeeDaysData.find(r => r.employeeId === 888);
            if (!testRecord) {
                addStep('❌ لم يتم العثور على سجل للاختبار', 'error');
                updateStatus('لم يتم العثور على سجل للاختبار', 'error');
                return;
            }
            
            addLog(`🔄 اختبار تعديل السجل ${testRecord.id}...`);
            
            try {
                // محاكاة تعديل
                const oldValue = testRecord.calculatedDays;
                const newValue = oldValue + 3;
                
                addLog(`تغيير الأيام المحسوبة من ${oldValue} إلى ${newValue}`);
                
                // استدعاء دالة التحديث
                const updateResult = updateRecordField(testRecord.id, 'calculatedDays', newValue);
                
                if (updateResult) {
                    addStep('✅ نجح تحديث الحقل', 'success');
                    addLog('✓ تم تحديث الحقل بنجاح');
                    
                    // فحص حالة recordsChanges
                    setTimeout(() => {
                        const changesCount = Object.keys(recordsChanges).length;
                        addLog(`📊 عدد التغييرات في recordsChanges: ${changesCount}`);
                        
                        if (changesCount > 0) {
                            addStep('✅ تم تتبع التغيير في recordsChanges', 'success');
                            
                            // اختبار الحفظ
                            setTimeout(() => {
                                addLog('🔄 اختبار دالة الحفظ...');
                                
                                try {
                                    // تجاوز تأكيد الحفظ
                                    const originalConfirm = window.confirm;
                                    window.confirm = () => {
                                        addLog('✓ تم تأكيد الحفظ');
                                        return true;
                                    };
                                    
                                    const saveResult = saveAllRecordsChanges(888);
                                    
                                    // استعادة confirm الأصلي
                                    window.confirm = originalConfirm;
                                    
                                    if (saveResult) {
                                        addStep('🎉 نجح الحفظ! النظام يعمل بشكل مثالي', 'success');
                                        updateStatus('🎉 ممتاز! النظام يعمل بشكل مثالي', 'success');
                                    } else {
                                        addStep('❌ فشل الحفظ', 'error');
                                        updateStatus('❌ فشل في الحفظ', 'error');
                                    }
                                    
                                } catch (error) {
                                    addStep('❌ خطأ في دالة الحفظ: ' + error.message, 'error');
                                    updateStatus('❌ خطأ في دالة الحفظ', 'error');
                                }
                            }, 1000);
                            
                        } else {
                            addStep('❌ لم يتم تتبع التغيير', 'error');
                            updateStatus('❌ مشكلة في تتبع التغييرات', 'error');
                        }
                    }, 500);
                    
                } else {
                    addStep('❌ فشل في تحديث الحقل', 'error');
                    updateStatus('❌ فشل في تحديث الحقل', 'error');
                }
                
            } catch (error) {
                addStep('❌ خطأ في اختبار التعديل: ' + error.message, 'error');
                addLog('❌ خطأ: ' + error.message);
                updateStatus('❌ خطأ في اختبار التعديل', 'error');
            }
        }

        function runCompleteSystemTest() {
            updateStatus('🚀 بدء الاختبار الشامل للنظام...', 'info');
            addLog('=== بدء الاختبار الشامل لنظام التعديل الكامل ===');
            
            // مسح الخطوات السابقة
            document.getElementById('steps').innerHTML = '';
            
            addStep('🔧 بدء الاختبار الشامل لنظام التعديل والحفظ', 'info');
            
            // خطوة 1: فحص الدوال
            setTimeout(() => {
                testFunctions();
            }, 500);
            
            // خطوة 2: إنشاء الموظف
            setTimeout(() => {
                createTestEmployee();
            }, 2000);
            
            // خطوة 3: فتح النافذة
            setTimeout(() => {
                openRecordsWindow();
            }, 4000);
            
            // خطوة 4: اختبار التعديل والحفظ
            setTimeout(() => {
                testEditAndSave();
            }, 6500);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء الاختبار الشامل ===');
                addStep('📋 انتهاء الاختبار - راجع النتائج أعلاه', 'info');
                addLog('🎯 إذا ظهرت رسالة "النظام يعمل بشكل مثالي" فقد تم حل جميع المشاكل');
            }, 10000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل اختبار نظام التعديل الكامل');
            updateStatus('جاهز لبدء الاختبار الشامل', 'info');
        });
    </script>
</body>
</html>
