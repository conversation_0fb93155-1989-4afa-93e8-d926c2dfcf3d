<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البدلات اليومية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #fd7e14, #e55a00);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #fd7e14, #e55a00);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.success:hover {
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .test-button.warning:hover {
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.danger:hover {
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }

        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .info-box p {
            color: #856404;
            margin: 0;
            line-height: 1.5;
        }

        .feature-list {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .feature-list h4 {
            color: #0c5460;
            margin-bottom: 10px;
        }

        .feature-list ul {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            color: #0c5460;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-coins"></i> اختبار البدلات اليومية</h1>
            <p>اختبار بدل الاغتراب وبدل الانتقالات اليومي</p>
        </div>

        <div class="status" id="status">
            جاهز لاختبار البدلات اليومية...
        </div>

        <div class="content">
            <div class="feature-list">
                <h4>🎯 البدلات اليومية الجديدة:</h4>
                <ul>
                    <li>بدل الاغتراب اليومي × عدد أيام الحضور</li>
                    <li>بدل الانتقالات اليومي × عدد أيام الحضور</li>
                    <li>حساب تلقائي في نافذة حساب الراتب</li>
                    <li>عرض تفصيلي في جدول الراتب</li>
                    <li>يعمل للموظفين اليوميين والشهريين</li>
                </ul>
            </div>

            <div class="info-box">
                <h4>💡 كيفية الاختبار:</h4>
                <p>
                    1. إنشاء موظف تجريبي مع بدلات يومية<br>
                    2. إضافة أيام عمل للموظف<br>
                    3. فتح نافذة حساب الراتب<br>
                    4. التحقق من حساب البدلات اليومية<br>
                    5. مراجعة التفاصيل في الجدول
                </p>
            </div>

            <button class="test-button success" onclick="createEmployeeWithAllowances()">
                <i class="fas fa-user-plus"></i> 1. إنشاء موظف مع بدلات يومية
            </button>

            <button class="test-button" onclick="addWorkDays()">
                <i class="fas fa-calendar-plus"></i> 2. إضافة أيام عمل
            </button>

            <button class="test-button warning" onclick="openSalaryCalculator()">
                <i class="fas fa-calculator"></i> 3. فتح حاسبة الراتب
            </button>

            <div class="grid">
                <button class="test-button" onclick="testDailyEmployee()">
                    <i class="fas fa-user-clock"></i> موظف يومي
                </button>

                <button class="test-button" onclick="testMonthlyEmployee()">
                    <i class="fas fa-user-tie"></i> موظف شهري
                </button>
            </div>

            <button class="test-button danger" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>

            <button class="test-button" onclick="forceShowAllowances()" style="background: linear-gradient(135deg, #6f42c1, #5a2d91);">
                <i class="fas fa-magic"></i> إجبار ظهور البدلات
            </button>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();

            let color = '#333';
            if (type === 'success') color = '#28a745';
            else if (type === 'error') color = '#dc3545';
            else if (type === 'warning') color = '#ffc107';
            else if (type === 'info') color = '#17a2b8';

            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف مع بدلات يومية
        function createEmployeeWithAllowances() {
            updateStatus('جاري إنشاء موظف مع بدلات يومية...', 'warning');
            addResult('👤 إنشاء موظف تجريبي مع بدلات يومية', 'info');

            try {
                // التحقق من وجود النظام
                if (typeof employees === 'undefined') {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }

                const testEmployee = {
                    id: 11111,
                    name: "موظف البدلات اليومية",
                    position: "مهندس مشاريع",
                    employeeCode: "DAILY_ALLOW",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/fd7e14/ffffff?text=ALLOWANCES",
                    employmentType: "monthly",
                    dateAdded: new Date().toISOString(),
                    salary: {
                        basic: 8000,
                        total: 8000,
                        allowances: {
                            housing: 500,
                            transport: 300,
                            expat: 0,
                            meal: 200,
                            other: 0,
                            total: 1000
                        },
                        dailyAllowances: {
                            expatDaily: 50,      // بدل الاغتراب اليومي: 50 ج.م
                            transportDaily: 30   // بدل الانتقالات اليومي: 30 ج.م
                        }
                    }
                };

                // إزالة الموظف إذا كان موجود
                const existingIndex = employees.findIndex(emp => emp.id === 11111);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    addResult('🗑️ تم إزالة الموظف الموجود', 'warning');
                }

                employees.push(testEmployee);

                // حفظ البيانات
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                    addResult('💾 تم حفظ البيانات في localStorage', 'success');
                } else {
                    addResult('⚠️ دالة الحفظ غير متاحة', 'warning');
                }

                // التحقق من حفظ البيانات
                const savedEmployee = employees.find(emp => emp.id === 11111);
                if (savedEmployee && savedEmployee.salary && savedEmployee.salary.dailyAllowances) {
                    addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    addResult(`💰 الراتب الأساسي: ${testEmployee.salary.basic} ج.م`, 'info');
                    addResult(`🏠 البدلات الثابتة: ${testEmployee.salary.allowances.total} ج.م`, 'info');
                    addResult(`🌍 بدل الاغتراب اليومي: ${testEmployee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                    addResult(`🚗 بدل الانتقالات اليومي: ${testEmployee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

                    // طباعة هيكل البيانات للتأكد
                    console.log('🔍 هيكل بيانات الموظف المحفوظ:', savedEmployee);
                    console.log('🔍 البدلات اليومية:', savedEmployee.salary.dailyAllowances);

                    updateStatus('✅ الموظف جاهز مع البدلات اليومية', 'success');
                } else {
                    addResult('❌ فشل في حفظ البدلات اليومية', 'error');
                    updateStatus('❌ فشل في حفظ البدلات', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف', 'error');
            }
        }

        // إضافة أيام عمل
        function addWorkDays() {
            updateStatus('جاري إضافة أيام عمل...', 'warning');
            addResult('📅 إضافة أيام عمل للموظف', 'info');

            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 11111);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }

                // التأكد من وجود مصفوفة أيام العمل
                if (!window.employeeDaysData) {
                    window.employeeDaysData = [];
                    addResult('📋 تم إنشاء مصفوفة أيام العمل', 'info');
                }

                // إضافة سجل أيام عمل تجريبي
                const workDaysRecord = {
                    id: Date.now(),
                    employeeId: 11111,
                    projectName: "مشروع اختبار البدلات",
                    startDate: "2024-01-01",
                    endDate: "2024-01-31",
                    calculatedDays: 25,      // 25 يوم عمل
                    absenceDays: 3,          // 3 أيام غياب
                    actualDays: 22,          // 22 يوم حضور فعلي
                    overtimeHours: 16,       // 16 ساعة إضافية
                    createdAt: new Date().toISOString()
                };

                // إزالة السجل إذا كان موجود
                const existingIndex = employeeDaysData.findIndex(record =>
                    record.employeeId === 11111 && record.projectName === "مشروع اختبار البدلات"
                );
                if (existingIndex !== -1) {
                    employeeDaysData.splice(existingIndex, 1);
                    addResult('🗑️ تم إزالة السجل الموجود', 'warning');
                }

                employeeDaysData.push(workDaysRecord);

                if (typeof saveDaysDataToLocalStorage === 'function') {
                    saveDaysDataToLocalStorage();
                }

                addResult('✅ تم إضافة أيام العمل بنجاح', 'success');
                addResult(`📊 إجمالي الأيام: ${workDaysRecord.calculatedDays}`, 'info');
                addResult(`❌ أيام الغياب: ${workDaysRecord.absenceDays}`, 'warning');
                addResult(`✅ أيام الحضور الفعلي: ${workDaysRecord.actualDays}`, 'success');
                addResult(`⏰ الساعات الإضافية: ${workDaysRecord.overtimeHours}`, 'info');

                // حساب البدلات المتوقعة
                const expatriateAllowance = employee.salary.dailyAllowances.expatDaily * workDaysRecord.actualDays;
                const transportAllowance = employee.salary.dailyAllowances.transportDaily * workDaysRecord.actualDays;
                const totalDailyAllowances = expatriateAllowance + transportAllowance;

                addResult(`🌍 بدل الاغتراب المتوقع: ${workDaysRecord.actualDays} × ${employee.salary.dailyAllowances.expatDaily} = ${expatriateAllowance} ج.م`, 'success');
                addResult(`🚗 بدل الانتقالات المتوقع: ${workDaysRecord.actualDays} × ${employee.salary.dailyAllowances.transportDaily} = ${transportAllowance} ج.م`, 'success');
                addResult(`💰 إجمالي البدلات اليومية المتوقعة: ${totalDailyAllowances} ج.م`, 'success');

                updateStatus('✅ أيام العمل جاهزة', 'success');

            } catch (error) {
                addResult(`❌ خطأ في إضافة أيام العمل: ${error.message}`, 'error');
                updateStatus('❌ فشل في إضافة أيام العمل', 'error');
            }
        }

        // فتح حاسبة الراتب
        function openSalaryCalculator() {
            updateStatus('جاري فتح حاسبة الراتب...', 'warning');
            addResult('🧮 فتح حاسبة الراتب لاختبار البدلات اليومية', 'info');

            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 11111);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }

                // التحقق من البدلات اليومية في بيانات الموظف
                addResult('🔍 فحص بيانات البدلات اليومية...', 'info');
                console.log('🔍 بيانات الموظف الكاملة:', employee);

                if (employee.salary && employee.salary.dailyAllowances) {
                    addResult('✅ البدلات اليومية موجودة في بيانات الموظف', 'success');
                    addResult(`🌍 بدل الاغتراب: ${employee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                    addResult(`🚗 بدل الانتقالات: ${employee.salary.dailyAllowances.transportDaily} ج.م`, 'success');
                } else {
                    addResult('❌ البدلات اليومية غير موجودة في بيانات الموظف', 'error');
                    addResult('💡 سيتم إضافة البدلات اليومية الآن...', 'info');

                    // إضافة البدلات اليومية إذا لم تكن موجودة
                    if (!employee.salary) {
                        employee.salary = {};
                    }
                    employee.salary.dailyAllowances = {
                        expatDaily: 50,
                        transportDaily: 30
                    };

                    // حفظ التحديث
                    if (typeof saveEmployeesToLocalStorage === 'function') {
                        saveEmployeesToLocalStorage();
                    }

                    addResult('✅ تم إضافة البدلات اليومية للموظف', 'success');
                }

                // التحقق من وجود أيام العمل
                if (!employeeDaysData || employeeDaysData.length === 0) {
                    addResult('❌ يرجى إضافة أيام العمل أولاً', 'error');
                    updateStatus('❌ لا توجد أيام عمل', 'error');
                    return;
                }

                addResult('✅ فتح نافذة حساب الراتب...', 'success');
                addResult('💡 تحقق من البدلات اليومية في الجدول', 'info');

                // فتح النافذة
                if (typeof window.openSalaryCalculator === 'function') {
                    window.openSalaryCalculator(11111);
                    updateStatus('✅ النافذة مفتوحة - تحقق من البدلات', 'success');

                    // إضافة تأخير للتحقق من ظهور البدلات
                    setTimeout(() => {
                        addResult('🔍 تحقق من وحدة التحكم (F12) لرؤية رسائل التشخيص', 'info');
                        addResult('📋 ابحث عن "البدلات اليومية" في جدول تفاصيل الراتب', 'info');
                    }, 1000);

                } else {
                    addResult('❌ دالة openSalaryCalculator غير متاحة', 'error');
                    updateStatus('❌ الدالة غير متاحة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في فتح حاسبة الراتب: ${error.message}`, 'error');
                updateStatus('❌ فشل في فتح الحاسبة', 'error');
            }
        }

        // اختبار موظف يومي
        function testDailyEmployee() {
            addResult('👷 اختبار موظف يومي مع بدلات', 'info');

            try {
                const dailyEmployee = {
                    id: 22222,
                    name: "موظف يومي مع بدلات",
                    position: "عامل مشاريع",
                    employeeCode: "DAILY_WORKER",
                    employmentType: "daily",
                    salary: {
                        dailyWage: 200,
                        basic: 200,
                        total: 200,
                        dailyAllowances: {
                            expatDaily: 25,      // بدل الاغتراب اليومي
                            transportDaily: 15   // بدل الانتقالات اليومي
                        }
                    }
                };

                // إضافة الموظف
                const existingIndex = employees.findIndex(emp => emp.id === 22222);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                employees.push(dailyEmployee);

                addResult('✅ تم إنشاء موظف يومي مع بدلات', 'success');
                addResult(`💰 أجر اليوم: ${dailyEmployee.salary.dailyWage} ج.م`, 'info');
                addResult(`🌍 بدل الاغتراب اليومي: ${dailyEmployee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                addResult(`🚗 بدل الانتقالات اليومي: ${dailyEmployee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف اليومي: ${error.message}`, 'error');
            }
        }

        // اختبار موظف شهري
        function testMonthlyEmployee() {
            addResult('👔 اختبار موظف شهري مع بدلات', 'info');

            try {
                const monthlyEmployee = {
                    id: 33333,
                    name: "موظف شهري مع بدلات",
                    position: "مدير مشاريع",
                    employeeCode: "MONTHLY_MANAGER",
                    employmentType: "monthly",
                    salary: {
                        basic: 12000,
                        total: 12000,
                        dailyAllowances: {
                            expatDaily: 75,      // بدل الاغتراب اليومي
                            transportDaily: 45   // بدل الانتقالات اليومي
                        }
                    }
                };

                // إضافة الموظف
                const existingIndex = employees.findIndex(emp => emp.id === 33333);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                employees.push(monthlyEmployee);

                addResult('✅ تم إنشاء موظف شهري مع بدلات', 'success');
                addResult(`💰 الراتب الشهري: ${monthlyEmployee.salary.basic} ج.م`, 'info');
                addResult(`🌍 بدل الاغتراب اليومي: ${monthlyEmployee.salary.dailyAllowances.expatDaily} ج.م`, 'success');
                addResult(`🚗 بدل الانتقالات اليومي: ${monthlyEmployee.salary.dailyAllowances.transportDaily} ج.م`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف الشهري: ${error.message}`, 'error');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // إجبار ظهور البدلات اليومية
        function forceShowAllowances() {
            addResult('🔧 إجبار ظهور البدلات اليومية...', 'info');

            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 11111);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }

                // إجبار إضافة البدلات اليومية
                if (!employee.salary) {
                    employee.salary = { basic: 8000, total: 8000 };
                }

                employee.salary.dailyAllowances = {
                    expatDaily: 50,
                    transportDaily: 30
                };

                // حفظ البيانات
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }

                addResult('✅ تم إجبار إضافة البدلات اليومية', 'success');

                // التحقق من أيام العمل
                if (!employeeDaysData || employeeDaysData.length === 0) {
                    // إضافة أيام عمل تجريبية
                    if (!window.employeeDaysData) {
                        window.employeeDaysData = [];
                    }

                    const workDaysRecord = {
                        id: Date.now(),
                        employeeId: 11111,
                        projectName: "مشروع اختبار البدلات",
                        startDate: "2024-01-01",
                        endDate: "2024-01-31",
                        calculatedDays: 25,
                        absenceDays: 3,
                        actualDays: 22,
                        overtimeHours: 16,
                        createdAt: new Date().toISOString()
                    };

                    employeeDaysData.push(workDaysRecord);

                    if (typeof saveDaysDataToLocalStorage === 'function') {
                        saveDaysDataToLocalStorage();
                    }

                    addResult('✅ تم إضافة أيام عمل تجريبية', 'success');
                }

                // فتح نافذة حساب الراتب مباشرة
                addResult('🧮 فتح نافذة حساب الراتب...', 'info');

                if (typeof window.openSalaryCalculator === 'function') {
                    window.openSalaryCalculator(11111);

                    setTimeout(() => {
                        addResult('🔍 تحقق من وحدة التحكم (F12) لرؤية رسائل التشخيص', 'info');
                        addResult('📋 يجب أن تظهر البدلات اليومية الآن في الجدول', 'success');
                        addResult('💰 المبلغ المتوقع: 1760 ج.م (50×22 + 30×22)', 'success');
                    }, 1000);

                    updateStatus('✅ تم إجبار ظهور البدلات', 'success');
                } else {
                    addResult('❌ دالة openSalaryCalculator غير متاحة', 'error');
                }

            } catch (error) {
                addResult(`❌ خطأ في إجبار ظهور البدلات: ${error.message}`, 'error');
            }
        }

        // اختبار تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في اختبار البدلات اليومية', 'info');
                addResult('💡 هذا الاختبار يتحقق من حساب بدل الاغتراب والانتقالات', 'info');

                // فحص النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }

                // فحص دالة حساب الراتب
                if (typeof openSalaryCalculator !== 'undefined') {
                    addResult('✅ دالة حساب الراتب متاحة', 'success');
                    updateStatus('✅ النظام جاهز لاختبار البدلات', 'success');
                } else {
                    addResult('❌ دالة حساب الراتب غير متاحة', 'error');
                    updateStatus('❌ الدالة مفقودة', 'error');
                }

            }, 1000);
        });
    </script>
</body>
</html>
