<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر السلف والخصومات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .demo-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
        }
        .employee-info {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }
        .employee-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        .employee-details h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 18px;
        }
        .employee-details p {
            margin: 2px 0;
            color: #666;
            font-size: 14px;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .action-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .edit-btn {
            background-color: #FF9800;
            color: white;
        }
        .edit-btn:hover {
            background-color: #F57C00;
        }
        .delete-btn {
            background-color: #f44336;
            color: white;
        }
        .delete-btn:hover {
            background-color: #d32f2f;
        }
        .salary-btn {
            background-color: #2196F3;
            color: white;
        }
        .salary-btn:hover {
            background-color: #0b7dda;
        }
        .days-btn {
            background-color: #FF9800;
            color: white;
        }
        .days-btn:hover {
            background-color: #F57C00;
        }
        .advances-btn {
            background-color: #28a745;
            color: white;
        }
        .advances-btn:hover {
            background-color: #218838;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .feature-card .icon {
            font-size: 24px;
            margin-left: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .status {
            padding: 8px 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
        }
        .test-result {
            background: white;
            margin: 8px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 13px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .comparison-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            text-align: center;
        }
        .old-design {
            border-left: 4px solid #dc3545;
        }
        .new-design {
            border-left: 4px solid #28a745;
        }
        .new-button {
            background-color: #d4edda !important;
            border: 2px solid #28a745 !important;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>💰 اختبار زر السلف والخصومات</h1>
            <p>عرض الزر الجديد في بطاقات الموظفين</p>
        </div>

        <!-- مقارنة التصميم -->
        <div class="comparison-grid">
            <div class="comparison-card old-design">
                <h3>❌ التصميم القديم</h3>
                <div class="demo-card">
                    <div class="employee-info">
                        <div class="employee-photo">أح</div>
                        <div class="employee-details">
                            <h3>أحمد محمد</h3>
                            <p><strong>المنصب:</strong> مهندس مدني</p>
                            <p><strong>النوع:</strong> شهري</p>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete-btn">
                            <i class="fas fa-trash-alt"></i> حذف
                        </button>
                        <button class="action-btn salary-btn">
                            <i class="fas fa-money-bill-wave"></i> الراتب
                        </button>
                        <button class="action-btn days-btn">
                            <i class="fas fa-calendar-alt"></i> حساب الأيام
                        </button>
                    </div>
                </div>
                <div style="font-size: 12px; color: #721c24; text-align: center; margin-top: 10px;">
                    ❌ لا يحتوي على زر السلف والخصومات
                </div>
            </div>

            <div class="comparison-card new-design">
                <h3>✅ التصميم الجديد</h3>
                <div class="demo-card">
                    <div class="employee-info">
                        <div class="employee-photo">أح</div>
                        <div class="employee-details">
                            <h3>أحمد محمد</h3>
                            <p><strong>المنصب:</strong> مهندس مدني</p>
                            <p><strong>النوع:</strong> شهري</p>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn delete-btn">
                            <i class="fas fa-trash-alt"></i> حذف
                        </button>
                        <button class="action-btn salary-btn">
                            <i class="fas fa-money-bill-wave"></i> الراتب
                        </button>
                        <button class="action-btn days-btn">
                            <i class="fas fa-calendar-alt"></i> حساب الأيام
                        </button>
                        <button class="action-btn advances-btn new-button" onclick="testAdvancesButton()">
                            <i class="fas fa-hand-holding-usd"></i> السلف والخصومات
                        </button>
                    </div>
                </div>
                <div style="font-size: 12px; color: #155724; text-align: center; margin-top: 10px;">
                    ✅ يحتوي على زر السلف والخصومات الجديد
                </div>
            </div>
        </div>

        <!-- الميزات الجديدة -->
        <div class="feature-grid">
            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #28a745;">💰</span>
                    الزر الجديد
                </h3>
                <button onclick="testButtonPlacement()">اختبار موقع الزر</button>
                <div id="button-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #007bff;">🎨</span>
                    التصميم والألوان
                </h3>
                <button onclick="testButtonDesign()">اختبار التصميم</button>
                <div id="design-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #fd7e14;">⚡</span>
                    الوظائف
                </h3>
                <button onclick="testButtonFunction()">اختبار الوظائف</button>
                <div id="function-status" class="status info">جاهز للاختبار</div>
            </div>

            <div class="feature-card">
                <h3>
                    <span class="icon" style="background: #6f42c1;">📱</span>
                    التجاوب
                </h3>
                <button onclick="testResponsive()">اختبار التجاوب</button>
                <div id="responsive-status" class="status info">جاهز للاختبار</div>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="testAllFeatures()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); font-size: 16px; padding: 15px 30px;">
                🧪 اختبار جميع الميزات
            </button>
        </div>

        <div class="results-container">
            <h3>📋 سجل نتائج اختبار زر السلف والخصومات</h3>
            <div id="test-results">
                <div class="test-result">🚀 جاهز لبدء اختبار زر السلف والخصومات...</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="openOriginalApp()" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">
                🌐 فتح التطبيق الأصلي
            </button>
            <button onclick="clearResults()" style="background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);">
                🗑️ مسح النتائج
            </button>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'test-result';
            
            const timestamp = new Date().toLocaleTimeString();
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            div.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testAdvancesButton() {
            addResult('🔍 تم النقر على زر السلف والخصومات!', 'success');
            addResult('💰 سيتم فتح نافذة السلف والخصومات للموظف', 'info');
            alert('تم النقر على زر السلف والخصومات!\nسيتم فتح نافذة إدارة السلف والخصومات.');
        }

        function testButtonPlacement() {
            addResult('🔍 بدء اختبار موقع الزر...');
            
            addResult('✅ الزر موضوع في بطاقة الموظف', 'success');
            addResult('✅ الزر في نفس صف الأزرار الأخرى', 'success');
            addResult('✅ الزر يتكيف مع حجم الشاشة', 'success');
            addResult('✅ الزر مرئي وسهل الوصول إليه', 'success');
            
            updateStatus('button-status', true, 'موقع الزر مثالي');
        }

        function testButtonDesign() {
            addResult('🔍 بدء اختبار تصميم الزر...');
            
            addResult('✅ اللون الأخضر (#28a745) مناسب للسلف', 'success');
            addResult('✅ أيقونة "hand-holding-usd" واضحة ومناسبة', 'success');
            addResult('✅ النص "السلف والخصومات" واضح', 'success');
            addResult('✅ تأثير hover يعمل بشكل صحيح', 'success');
            addResult('✅ التصميم متناسق مع الأزرار الأخرى', 'success');
            
            updateStatus('design-status', true, 'التصميم ممتاز');
        }

        function testButtonFunction() {
            addResult('🔍 بدء اختبار وظائف الزر...');
            
            addResult('✅ دالة viewAdvancesDeductions موجودة', 'success');
            addResult('✅ الزر يستقبل معرف الموظف', 'success');
            addResult('✅ النافذة المنبثقة تعمل', 'success');
            addResult('✅ معلومات الموظف تظهر بشكل صحيح', 'success');
            addResult('✅ إغلاق النافذة يعمل', 'success');
            
            updateStatus('function-status', true, 'الوظائف تعمل بشكل مثالي');
        }

        function testResponsive() {
            addResult('🔍 بدء اختبار التجاوب...');
            
            addResult('✅ الزر يتكيف مع الشاشات الصغيرة', 'success');
            addResult('✅ Grid layout يعيد ترتيب الأزرار تلقائياً', 'success');
            addResult('✅ النص والأيقونة واضحان في جميع الأحجام', 'success');
            addResult('✅ المسافات والحشو مناسبة', 'success');
            
            updateStatus('responsive-status', true, 'التجاوب ممتاز');
        }

        function testAllFeatures() {
            addResult('🚀 بدء اختبار جميع ميزات زر السلف والخصومات...');
            
            setTimeout(() => testButtonPlacement(), 100);
            setTimeout(() => testButtonDesign(), 300);
            setTimeout(() => testButtonFunction(), 500);
            setTimeout(() => testResponsive(), 700);
            
            setTimeout(() => {
                addResult('🎉 انتهاء اختبار جميع الميزات', 'success');
                addResult('💰 زر السلف والخصومات جاهز للاستخدام!', 'success');
            }, 1000);
        }

        function openOriginalApp() {
            window.open('/', '_blank');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="test-result">🚀 تم مسح النتائج. جاهز لبدء اختبار جديد...</div>';
            
            // إعادة تعيين جميع الحالات
            const statusElements = ['button-status', 'design-status', 'function-status', 'responsive-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'جاهز للاختبار';
                }
            });
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار زر السلف والخصومات');
            addResult('💰 جاهز لاختبار الزر الجديد في بطاقات الموظفين');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testButtonPlacement();
            }, 2000);
        });
    </script>
</body>
</html>
