<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التعديل المباشر في الجدول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .results-container {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 14px;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #007bff;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .demo-table th,
        .demo-table td {
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .demo-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .demo-table input {
            width: 100%;
            border: 1px solid transparent;
            padding: 4px 8px;
            border-radius: 4px;
            text-align: center;
            transition: all 0.2s;
        }

        .demo-table input:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .demo-table input:focus {
            outline: none;
            border-color: #007bff;
            background-color: white;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .modified-row {
            background-color: #fff3cd !important;
            border-left: 3px solid #ffc107;
        }

        .modified-row input {
            background-color: rgba(255, 193, 7, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-edit"></i> اختبار التعديل المباشر في الجدول</h1>
            <p>اختبار شامل لوظائف التعديل المباشر وحفظ التغييرات في نافذة السجلات</p>
        </div>

        <!-- معلومات الميزة -->
        <div class="test-section">
            <h2><i class="fas fa-info-circle"></i> نظرة عامة على الميزة</h2>
            <p>نظام التعديل المباشر في الجدول يتضمن:</p>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-edit"></i></div>
                    <h4>تعديل مباشر</h4>
                    <p>تعديل البيانات مباشرة في الجدول</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-save"></i></div>
                    <h4>حفظ تلقائي</h4>
                    <p>تتبع التغييرات وحفظها</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-undo"></i></div>
                    <h4>إلغاء التغييرات</h4>
                    <p>إمكانية إلغاء التعديلات</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-eye"></i></div>
                    <h4>مؤشرات بصرية</h4>
                    <p>تمييز الصفوف المعدلة</p>
                </div>
            </div>
        </div>

        <!-- جدول تجريبي -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> جدول تجريبي للتعديل</h2>
            <p>جرب التعديل المباشر في الجدول أدناه:</p>

            <table class="demo-table" id="demoTable">
                <thead>
                    <tr>
                        <th>المشروع</th>
                        <th>الأيام المحسوبة</th>
                        <th>أيام الغياب</th>
                        <th>الساعات الإضافية</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody id="demoTableBody">
                    <!-- سيتم ملؤها بالبيانات التجريبية -->
                </tbody>
            </table>

            <div style="margin-top: 15px; text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                <button onclick="saveDemoChanges()"
                        id="saveDemoBtn"
                        style="background: #28a745; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: all 0.2s; margin-left: 10px;">
                    💾 حفظ التغييرات
                </button>
                <button onclick="resetDemoChanges()"
                        id="resetDemoBtn"
                        style="background: #6c757d; color: white; border: none; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: all 0.2s; display: none;">
                    ↶ إلغاء التغييرات
                </button>
                <div style="margin-top: 8px;">
                    <span id="demoStatus" style="font-size: 12px; color: #6c757d;"></span>
                </div>
            </div>
        </div>

        <!-- اختبارات الوظائف -->
        <div class="test-section">
            <h2><i class="fas fa-vial"></i> اختبارات الوظائف</h2>

            <div style="margin-bottom: 15px;">
                <button class="test-button" onclick="testInlineEditingFunctions()">
                    <i class="fas fa-search"></i> اختبار وجود الدوال
                </button>
                <button class="test-button secondary" onclick="testRecordsModal()">
                    <i class="fas fa-table"></i> اختبار نافذة السجلات
                </button>
                <button class="test-button warning" onclick="testDataPersistence()">
                    <i class="fas fa-database"></i> اختبار حفظ البيانات
                </button>
                <button class="test-button danger" onclick="testErrorHandling()">
                    <i class="fas fa-exclamation-triangle"></i> اختبار معالجة الأخطاء
                </button>
            </div>

            <div>
                <button class="test-button" onclick="runAllTests()">
                    <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                </button>
                <button class="test-button secondary" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
                <button class="test-button warning" onclick="createTestData()">
                    <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                </button>
                <button class="test-button danger" onclick="openOriginalApp()">
                    <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
                </button>
            </div>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="results-container">
            <h3><i class="fas fa-clipboard-list"></i> سجل نتائج الاختبارات</h3>
            <div id="test-results">
                <div class="test-result info">🚀 جاهز لبدء اختبار التعديل المباشر في الجدول...</div>
            </div>
        </div>

        <!-- حالة الاختبارات -->
        <div style="margin-top: 20px;">
            <div id="functions-status" class="status">⏳ في انتظار اختبار الدوال...</div>
            <div id="modal-status" class="status">⏳ في انتظار اختبار النافذة...</div>
            <div id="data-status" class="status">⏳ في انتظار اختبار البيانات...</div>
            <div id="error-status" class="status">⏳ في انتظار اختبار الأخطاء...</div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // متغيرات للجدول التجريبي
        let demoData = [];
        let demoChanges = {};

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        // إنشاء بيانات تجريبية للجدول
        function createTestData() {
            addResult('🔧 إنشاء بيانات تجريبية...', 'info');

            demoData = [
                { id: 1, projectName: 'مشروع تطوير النظام', calculatedDays: 30, absenceDays: 2, overtimeHours: 10 },
                { id: 2, projectName: 'مشروع الصيانة', calculatedDays: 28, absenceDays: 1, overtimeHours: 8 },
                { id: 3, projectName: 'مشروع التدريب', calculatedDays: 25, absenceDays: 0, overtimeHours: 5 },
                { id: 4, projectName: 'مشروع الدعم الفني', calculatedDays: 31, absenceDays: 3, overtimeHours: 12 }
            ];

            // إنشاء بيانات موظف تجريبي
            if (!employees.find(emp => emp.id === 999)) {
                employees.push({
                    id: 999,
                    name: 'أحمد محمد علي',
                    position: 'مهندس برمجيات',
                    employeeCode: 'EMP001',
                    employmentType: 'monthly',
                    basicSalary: 8000
                });
            }

            // إنشاء سجلات أيام تجريبية
            demoData.forEach((item, index) => {
                const record = {
                    id: Date.now() + index,
                    employeeId: 999,
                    projectName: item.projectName,
                    startDate: '2024-01-01',
                    endDate: '2024-01-31',
                    calculatedDays: item.calculatedDays,
                    absenceDays: item.absenceDays,
                    overtimeHours: item.overtimeHours,
                    createdAt: new Date().toISOString()
                };

                if (!employeeDaysData.find(r => r.id === record.id)) {
                    employeeDaysData.push(record);
                }
            });

            populateDemoTable();
            addResult('✅ تم إنشاء البيانات التجريبية بنجاح', 'success');
        }

        // ملء الجدول التجريبي
        function populateDemoTable() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';

            demoData.forEach(item => {
                const row = document.createElement('tr');
                row.id = `demo-row-${item.id}`;
                row.innerHTML = `
                    <td>
                        <input type="text" value="${item.projectName}"
                               onchange="updateDemoField(${item.id}, 'projectName', this.value)">
                    </td>
                    <td>
                        <input type="number" value="${item.calculatedDays}"
                               onchange="updateDemoField(${item.id}, 'calculatedDays', parseInt(this.value))"
                               min="0">
                    </td>
                    <td>
                        <input type="number" value="${item.absenceDays}"
                               onchange="updateDemoField(${item.id}, 'absenceDays', parseInt(this.value))"
                               min="0">
                    </td>
                    <td>
                        <input type="number" value="${item.overtimeHours}"
                               onchange="updateDemoField(${item.id}, 'overtimeHours', parseInt(this.value))"
                               min="0">
                    </td>
                    <td id="status-${item.id}">عادي</td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث حقل في البيانات التجريبية
        function updateDemoField(id, fieldName, newValue) {
            const item = demoData.find(d => d.id === id);
            if (!item) return;

            const oldValue = item[fieldName];
            item[fieldName] = newValue;

            // تتبع التغيير
            if (!demoChanges[id]) {
                demoChanges[id] = {};
            }
            demoChanges[id][fieldName] = { oldValue, newValue };

            // تحديث مظهر الصف
            const row = document.getElementById(`demo-row-${id}`);
            const statusCell = document.getElementById(`status-${id}`);

            if (row && statusCell) {
                row.classList.add('modified-row');
                statusCell.textContent = 'معدل';
                statusCell.style.color = '#856404';
                statusCell.style.fontWeight = 'bold';
            }

            updateDemoButtons();
        }

        // تحديث أزرار الجدول التجريبي
        function updateDemoButtons() {
            const saveBtn = document.getElementById('saveDemoBtn');
            const resetBtn = document.getElementById('resetDemoBtn');
            const statusSpan = document.getElementById('demoStatus');
            const changesCount = Object.keys(demoChanges).length;

            if (changesCount > 0) {
                saveBtn.style.background = '#ffc107';
                saveBtn.style.color = '#212529';
                saveBtn.innerHTML = `💾 حفظ التغييرات (${changesCount})`;
                resetBtn.style.display = 'inline-block';
                statusSpan.textContent = `${changesCount} تغيير في انتظار الحفظ`;
                statusSpan.style.color = '#856404';
            } else {
                saveBtn.style.background = '#28a745';
                saveBtn.style.color = 'white';
                saveBtn.innerHTML = '💾 حفظ التغييرات';
                resetBtn.style.display = 'none';
                statusSpan.textContent = '';
            }
        }

        // حفظ تغييرات الجدول التجريبي
        function saveDemoChanges() {
            const changesCount = Object.keys(demoChanges).length;

            if (changesCount === 0) {
                addResult('لا توجد تغييرات للحفظ', 'info');
                return;
            }

            // محاكاة الحفظ
            demoChanges = {};

            // إزالة تنسيق التعديل
            document.querySelectorAll('.modified-row').forEach(row => {
                row.classList.remove('modified-row');
                const statusCell = row.querySelector('td:last-child');
                if (statusCell) {
                    statusCell.textContent = 'محفوظ';
                    statusCell.style.color = '#28a745';
                }
            });

            updateDemoButtons();
            addResult(`تم حفظ ${changesCount} تغيير بنجاح`, 'success');
        }

        // إعادة تعيين تغييرات الجدول التجريبي
        function resetDemoChanges() {
            const changesCount = Object.keys(demoChanges).length;

            if (changesCount === 0) {
                addResult('لا توجد تغييرات لإلغائها', 'info');
                return;
            }

            if (!confirm(`هل تريد إلغاء ${changesCount} تغيير؟`)) {
                return;
            }

            // إعادة تعيين البيانات
            Object.keys(demoChanges).forEach(id => {
                const item = demoData.find(d => d.id == id);
                const changes = demoChanges[id];

                Object.keys(changes).forEach(fieldName => {
                    item[fieldName] = changes[fieldName].oldValue;
                });
            });

            demoChanges = {};
            populateDemoTable();
            updateDemoButtons();
            addResult(`تم إلغاء ${changesCount} تغيير`, 'info');
        }

        // اختبار وجود دوال التعديل المباشر
        function testInlineEditingFunctions() {
            addResult('🔍 بدء اختبار وجود دوال التعديل المباشر...', 'info');

            const requiredFunctions = [
                'updateRecordField',
                'saveAllRecordsChanges',
                'resetAllChanges',
                'updateSaveButton',
                'showTemporaryMessage'
            ];

            let passed = 0;

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    passed++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            const success = passed === requiredFunctions.length;
            updateStatus('functions-status', success, success ? 'جميع الدوال موجودة' : `${passed}/${requiredFunctions.length} دالة موجودة`);

            return success;
        }

        // اختبار نافذة السجلات
        function testRecordsModal() {
            addResult('🔍 بدء اختبار نافذة السجلات...', 'info');

            try {
                // إنشاء بيانات تجريبية إذا لم تكن موجودة
                if (!employees.find(emp => emp.id === 999)) {
                    createTestData();
                }

                // اختبار فتح النافذة
                if (typeof viewDaysRecords === 'function') {
                    addResult('✅ دالة viewDaysRecords موجودة', 'success');

                    // فتح النافذة
                    viewDaysRecords(999);

                    setTimeout(() => {
                        const modal = document.getElementById('daysRecordsModal');
                        if (modal) {
                            addResult('✅ تم فتح نافذة السجلات بنجاح', 'success');

                            // اختبار وجود عناصر التعديل المباشر
                            const editableInputs = modal.querySelectorAll('input[onchange*="updateRecordField"]');
                            if (editableInputs.length > 0) {
                                addResult(`✅ تم العثور على ${editableInputs.length} حقل قابل للتعديل`, 'success');
                            } else {
                                addResult('❌ لم يتم العثور على حقول قابلة للتعديل', 'error');
                            }

                            // اختبار وجود أزرار الحفظ والإلغاء
                            const saveBtn = modal.querySelector('#saveChangesBtn');
                            const resetBtn = modal.querySelector('#resetChangesBtn');

                            if (saveBtn) {
                                addResult('✅ زر الحفظ موجود', 'success');
                            } else {
                                addResult('❌ زر الحفظ مفقود', 'error');
                            }

                            if (resetBtn) {
                                addResult('✅ زر الإلغاء موجود', 'success');
                            } else {
                                addResult('❌ زر الإلغاء مفقود', 'error');
                            }

                            // إغلاق النافذة
                            setTimeout(() => {
                                closeDaysRecordsModal();
                                addResult('✅ تم إغلاق النافذة', 'info');
                            }, 2000);

                            updateStatus('modal-status', true, 'نافذة السجلات تعمل بشكل صحيح');
                        } else {
                            addResult('❌ فشل في فتح نافذة السجلات', 'error');
                            updateStatus('modal-status', false, 'فشل في فتح النافذة');
                        }
                    }, 1000);
                } else {
                    addResult('❌ دالة viewDaysRecords غير موجودة', 'error');
                    updateStatus('modal-status', false, 'دالة النافذة مفقودة');
                }

            } catch (error) {
                addResult(`❌ خطأ في اختبار النافذة: ${error.message}`, 'error');
                updateStatus('modal-status', false, 'خطأ في اختبار النافذة');
            }
        }

        // اختبار حفظ البيانات
        function testDataPersistence() {
            addResult('🔍 بدء اختبار حفظ البيانات...', 'info');

            try {
                let passed = 0;
                let total = 0;

                // اختبار 1: وجود دالة الحفظ
                total++;
                if (typeof saveDaysDataToLocalStorage === 'function') {
                    addResult('✅ دالة saveDaysDataToLocalStorage موجودة', 'success');
                    passed++;
                } else {
                    addResult('❌ دالة saveDaysDataToLocalStorage مفقودة', 'error');
                }

                // اختبار 2: وجود دالة التحميل
                total++;
                if (typeof loadDaysDataFromLocalStorage === 'function') {
                    addResult('✅ دالة loadDaysDataFromLocalStorage موجودة', 'success');
                    passed++;
                } else {
                    addResult('❌ دالة loadDaysDataFromLocalStorage مفقودة', 'error');
                }

                // اختبار 3: localStorage
                total++;
                try {
                    const testData = { test: 'data' };
                    localStorage.setItem('testKey', JSON.stringify(testData));
                    const retrieved = JSON.parse(localStorage.getItem('testKey'));

                    if (retrieved && retrieved.test === 'data') {
                        addResult('✅ localStorage يعمل بشكل صحيح', 'success');
                        passed++;
                    } else {
                        addResult('❌ مشكلة في localStorage', 'error');
                    }

                    localStorage.removeItem('testKey');
                } catch (error) {
                    addResult('❌ خطأ في localStorage: ' + error.message, 'error');
                }

                // اختبار 4: بيانات الأيام الموجودة
                total++;
                if (typeof employeeDaysData !== 'undefined' && Array.isArray(employeeDaysData)) {
                    addResult(`✅ بيانات الأيام متاحة: ${employeeDaysData.length} سجل`, 'success');
                    passed++;
                } else {
                    addResult('❌ بيانات الأيام غير متاحة', 'error');
                }

                const success = passed >= 3;
                updateStatus('data-status', success, success ? 'نظام حفظ البيانات يعمل' : 'مشاكل في حفظ البيانات');

                return success;

            } catch (error) {
                addResult(`❌ خطأ في اختبار البيانات: ${error.message}`, 'error');
                updateStatus('data-status', false, 'خطأ في اختبار البيانات');
                return false;
            }
        }

        // اختبار معالجة الأخطاء
        function testErrorHandling() {
            addResult('🔍 بدء اختبار معالجة الأخطاء...', 'info');

            try {
                let passed = 0;
                let total = 0;

                // اختبار 1: تحديث حقل بمعرف غير صحيح
                total++;
                try {
                    if (typeof updateRecordField === 'function') {
                        updateRecordField(99999, 'projectName', 'test');
                        addResult('✅ تم التعامل مع معرف السجل غير الصحيح', 'success');
                        passed++;
                    } else {
                        addResult('⚠️ دالة updateRecordField غير موجودة', 'warning');
                    }
                } catch (error) {
                    addResult('❌ خطأ في التعامل مع معرف السجل غير الصحيح', 'error');
                }

                // اختبار 2: حفظ بدون تغييرات
                total++;
                try {
                    if (typeof saveAllRecordsChanges === 'function') {
                        // مسح التغييرات أولاً
                        if (typeof recordsChanges !== 'undefined') {
                            recordsChanges = {};
                        }
                        saveAllRecordsChanges(999);
                        addResult('✅ تم التعامل مع الحفظ بدون تغييرات', 'success');
                        passed++;
                    } else {
                        addResult('⚠️ دالة saveAllRecordsChanges غير موجودة', 'warning');
                    }
                } catch (error) {
                    addResult('❌ خطأ في التعامل مع الحفظ بدون تغييرات', 'error');
                }

                // اختبار 3: إغلاق النافذة مع تغييرات غير محفوظة
                total++;
                try {
                    if (typeof closeDaysRecordsModal === 'function') {
                        // محاكاة وجود تغييرات
                        if (typeof recordsChanges !== 'undefined') {
                            recordsChanges = { 1: { projectName: { oldValue: 'old', newValue: 'new' } } };
                        }

                        // محاكاة confirm للإلغاء
                        const originalConfirm = window.confirm;
                        window.confirm = () => false;

                        closeDaysRecordsModal();

                        // استعادة confirm الأصلي
                        window.confirm = originalConfirm;

                        addResult('✅ تم التعامل مع إغلاق النافذة مع تغييرات غير محفوظة', 'success');
                        passed++;
                    } else {
                        addResult('⚠️ دالة closeDaysRecordsModal غير موجودة', 'warning');
                    }
                } catch (error) {
                    addResult('❌ خطأ في التعامل مع إغلاق النافذة', 'error');
                }

                const success = passed >= 2;
                updateStatus('error-status', success, success ? 'معالجة الأخطاء تعمل' : 'مشاكل في معالجة الأخطاء');

                return success;

            } catch (error) {
                addResult(`❌ خطأ في اختبار معالجة الأخطاء: ${error.message}`, 'error');
                updateStatus('error-status', false, 'خطأ في اختبار معالجة الأخطاء');
                return false;
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            addResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');

            // إنشاء البيانات التجريبية أولاً
            createTestData();

            setTimeout(() => {
                const test1 = testInlineEditingFunctions();
                setTimeout(() => {
                    const test2 = testDataPersistence();
                    setTimeout(() => {
                        const test3 = testErrorHandling();
                        setTimeout(() => {
                            testRecordsModal();

                            setTimeout(() => {
                                addResult('🎉 انتهاء جميع الاختبارات!', 'success');

                                const allPassed = test1 && test2 && test3;
                                if (allPassed) {
                                    addResult('✅ جميع الاختبارات نجحت! نظام التعديل المباشر جاهز للاستخدام', 'success');
                                } else {
                                    addResult('⚠️ بعض الاختبارات فشلت، يرجى مراجعة النتائج', 'warning');
                                }
                            }, 1000);
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 500);
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('test-results').innerHTML =
                '<div class="test-result info">🚀 جاهز لبدء اختبار التعديل المباشر في الجدول...</div>';

            // إعادة تعيين حالة الاختبارات
            const statusElements = ['functions-status', 'modal-status', 'data-status', 'error-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status';
                    element.textContent = '⏳ في انتظار الاختبار...';
                }
            });
        }

        // فتح التطبيق الأصلي
        function openOriginalApp() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('تم تحميل صفحة اختبار التعديل المباشر', 'info');
            addResult('جاهز لبدء الاختبارات...', 'info');

            // إنشاء الجدول التجريبي
            createTestData();

            // اختبار سريع تلقائي
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع ---', 'info');
                if (typeof updateRecordField === 'function') {
                    addResult('✅ نظام التعديل المباشر متاح', 'success');
                } else {
                    addResult('❌ نظام التعديل المباشر غير متاح', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
