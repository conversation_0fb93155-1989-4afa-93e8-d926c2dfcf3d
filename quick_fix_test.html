<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للإصلاح</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .button.success {
            background: #28a745;
        }
        
        .button.success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار سريع للإصلاح</h1>
            <p>فحص سريع للتأكد من أن البرنامج يعمل بعد الإصلاح</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="button success" onclick="runQuickTest()">
                🚀 اختبار سريع
            </button>
            <button class="button" onclick="testDateApplication()">
                📅 اختبار نافذة التاريخ
            </button>
        </div>

        <div id="status" class="status warning">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div class="console" id="console">
            [QUICK_FIX_TEST] نظام الاختبار السريع جاهز...<br>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function runQuickTest() {
            addLog('🔍 بدء الاختبار السريع...');
            updateStatus('جاري الاختبار...', 'warning');
            
            let testsCount = 0;
            let passedTests = 0;
            
            // اختبار 1: تحميل المتغيرات الأساسية
            testsCount++;
            if (typeof employees !== 'undefined' && typeof activeEmployees !== 'undefined') {
                addLog('✅ اختبار 1: المتغيرات الأساسية محملة');
                passedTests++;
            } else {
                addLog('❌ اختبار 1: المتغيرات الأساسية مفقودة');
            }
            
            // اختبار 2: الدوال الأساسية
            testsCount++;
            if (typeof populateEmployees === 'function' && typeof showTemporaryMessage === 'function') {
                addLog('✅ اختبار 2: الدوال الأساسية موجودة');
                passedTests++;
            } else {
                addLog('❌ اختبار 2: الدوال الأساسية مفقودة');
            }
            
            // اختبار 3: دوال تطبيق التاريخ
            testsCount++;
            if (typeof toggleDateApplication === 'function' && typeof openDateApplicationModal === 'function') {
                addLog('✅ اختبار 3: دوال تطبيق التاريخ موجودة');
                passedTests++;
            } else {
                addLog('❌ اختبار 3: دوال تطبيق التاريخ مفقودة');
            }
            
            // اختبار 4: دوال التعديل والحفظ
            testsCount++;
            if (typeof updateRecordField === 'function' && typeof saveAllRecordsChanges === 'function') {
                addLog('✅ اختبار 4: دوال التعديل والحفظ موجودة');
                passedTests++;
            } else {
                addLog('❌ اختبار 4: دوال التعديل والحفظ مفقودة');
            }
            
            // اختبار 5: دالة getMonthName
            testsCount++;
            if (typeof getMonthName === 'function') {
                try {
                    const monthName = getMonthName(1);
                    if (monthName) {
                        addLog('✅ اختبار 5: دالة getMonthName تعمل بشكل صحيح');
                        passedTests++;
                    } else {
                        addLog('❌ اختبار 5: دالة getMonthName لا تعطي نتيجة');
                    }
                } catch (error) {
                    addLog('❌ اختبار 5: خطأ في دالة getMonthName - ' + error.message);
                }
            } else {
                addLog('❌ اختبار 5: دالة getMonthName مفقودة');
            }
            
            // النتيجة النهائية
            setTimeout(() => {
                const percentage = (passedTests / testsCount) * 100;
                addLog(`📊 النتيجة النهائية: ${passedTests}/${testsCount} اختبار نجح (${percentage.toFixed(1)}%)`);
                
                if (percentage === 100) {
                    updateStatus('🎉 ممتاز! جميع الاختبارات نجحت - البرنامج يعمل بشكل مثالي', 'success');
                    addLog('🎉 البرنامج جاهز للاستخدام!');
                } else if (percentage >= 80) {
                    updateStatus(`⚠️ جيد! ${passedTests}/${testsCount} اختبار نجح - البرنامج يعمل مع بعض المشاكل البسيطة`, 'warning');
                } else {
                    updateStatus(`❌ مشاكل كبيرة! ${passedTests}/${testsCount} اختبار نجح فقط`, 'error');
                }
            }, 1000);
        }

        function testDateApplication() {
            addLog('📅 اختبار نافذة تطبيق التاريخ...');
            
            try {
                // إنشاء موظف تجريبي
                if (!employees.find(emp => emp.id === 9999)) {
                    employees.push({
                        id: 9999,
                        name: 'موظف اختبار سريع',
                        position: 'مختبر',
                        employeeCode: 'TEST9999'
                    });
                    addLog('✅ تم إنشاء موظف تجريبي');
                }
                
                // تنشيط الموظف
                activeEmployees.add(9999);
                addLog('✅ تم تنشيط الموظف التجريبي');
                
                // اختبار فتح النافذة
                if (typeof openDateApplicationModal === 'function') {
                    openDateApplicationModal();
                    addLog('✅ تم استدعاء دالة فتح نافذة التاريخ');
                    
                    // فحص النافذة بعد فترة قصيرة
                    setTimeout(() => {
                        const modal = document.getElementById('dateApplicationModal');
                        if (modal) {
                            addLog('✅ نافذة تطبيق التاريخ تم إنشاؤها بنجاح');
                            
                            // فحص القوائم المنسدلة
                            const monthSelect = document.getElementById('applicationMonth');
                            const yearSelect = document.getElementById('applicationYear');
                            
                            if (monthSelect && yearSelect) {
                                addLog('✅ القوائم المنسدلة موجودة');
                                updateStatus('✅ نافذة تطبيق التاريخ تعمل بشكل مثالي!', 'success');
                            } else {
                                addLog('❌ القوائم المنسدلة مفقودة');
                                updateStatus('❌ مشكلة في القوائم المنسدلة', 'error');
                            }
                        } else {
                            addLog('❌ فشل في إنشاء نافذة تطبيق التاريخ');
                            updateStatus('❌ فشل في إنشاء النافذة', 'error');
                        }
                    }, 1000);
                    
                } else {
                    addLog('❌ دالة فتح نافذة التاريخ غير موجودة');
                    updateStatus('❌ دالة فتح النافذة مفقودة', 'error');
                }
                
            } catch (error) {
                addLog('❌ خطأ في اختبار نافذة التاريخ: ' + error.message);
                updateStatus('❌ خطأ في اختبار النافذة: ' + error.message, 'error');
            }
        }

        // فحص فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة الاختبار السريع');
            
            setTimeout(() => {
                if (typeof employees !== 'undefined') {
                    addLog('✅ البرنامج الأساسي محمل');
                    updateStatus('البرنامج محمل - جاهز للاختبار', 'success');
                } else {
                    addLog('❌ البرنامج الأساسي غير محمل');
                    updateStatus('❌ البرنامج غير محمل', 'error');
                }
            }, 500);
        });
    </script>
</body>
</html>
