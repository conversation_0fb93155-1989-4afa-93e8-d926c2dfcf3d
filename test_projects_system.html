<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المشروعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-top: 0;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }

        .result-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .result-item.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .result-item.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .result-item.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram"></i> اختبار نظام المشروعات</h1>
            <p>اختبار شامل لجميع وظائف إدارة المشروعات</p>
        </div>

        <div class="content">
            <!-- قسم الاختبارات الأساسية -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> الاختبارات الأساسية</h3>
                <div class="button-grid">
                    <button class="test-button" onclick="testProjectsButton()">
                        <i class="fas fa-mouse-pointer"></i> اختبار زر المشروعات
                    </button>
                    <button class="test-button" onclick="testProjectsFunctions()">
                        <i class="fas fa-code"></i> اختبار دوال المشروعات
                    </button>
                    <button class="test-button" onclick="testProjectsData()">
                        <i class="fas fa-database"></i> اختبار بيانات المشروعات
                    </button>
                    <button class="test-button" onclick="testProjectsModal()">
                        <i class="fas fa-window-maximize"></i> اختبار نافذة المشروعات
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3><i class="fas fa-rocket"></i> الاختبارات المتقدمة</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="openProjectsModal()">
                        <i class="fas fa-external-link-alt"></i> فتح نافذة المشروعات
                    </button>
                    <button class="test-button warning" onclick="testProjectCRUD()">
                        <i class="fas fa-edit"></i> اختبار إضافة/تعديل/حذف
                    </button>
                    <button class="test-button" onclick="testProjectsDropdown()">
                        <i class="fas fa-list"></i> اختبار القائمة المنسدلة
                    </button>
                    <button class="test-button" onclick="testProjectsExport()">
                        <i class="fas fa-file-export"></i> اختبار التصدير والطباعة
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات الشاملة -->
            <div class="test-section">
                <h3><i class="fas fa-check-double"></i> الاختبارات الشاملة</h3>
                <div class="button-grid">
                    <button class="test-button success" onclick="runAllTests()">
                        <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                    </button>
                    <button class="test-button danger" onclick="clearResults()">
                        <i class="fas fa-trash"></i> مسح النتائج
                    </button>
                    <button class="test-button" onclick="openOriginalApp()">
                        <i class="fas fa-home"></i> فتح التطبيق الأصلي
                    </button>
                    <button class="test-button warning" onclick="generateReport()">
                        <i class="fas fa-chart-bar"></i> تقرير شامل
                    </button>
                </div>
            </div>

            <!-- إحصائيات الاختبارات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">اختبارات ناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">اختبارات فاشلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div class="stat-label">معدل النجاح</div>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results" id="results">
                <h4><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h4>
                <p>جاهز لبدء الاختبارات...</p>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;

            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const statusIcon = getStatusIcon(type);

            resultItem.innerHTML = `
                <span class="status-indicator status-${type}"></span>
                <strong>[${timestamp}]</strong> ${statusIcon} ${message}
            `;

            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;

            // تحديث الإحصائيات
            if (type === 'success') testStats.passed++;
            if (type === 'error') testStats.failed++;
            testStats.total++;

            updateStats();
        }

        function getStatusIcon(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || 'ℹ️';
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;

            const successRate = testStats.total > 0 ?
                Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function clearResults() {
            document.getElementById('results').innerHTML = `
                <h4><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h4>
                <p>جاهز لبدء الاختبارات...</p>
            `;

            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();

            addResult('تم مسح جميع النتائج', 'info');
        }

        // دوال الاختبار
        function testProjectsButton() {
            addResult('🔍 بدء اختبار زر المشروعات...', 'info');

            try {
                const projectsBtn = document.getElementById('projectsBtn');
                if (projectsBtn) {
                    addResult('✅ زر المشروعات موجود في DOM', 'success');

                    // اختبار وجود دالة فتح المشروعات
                    if (typeof openProjectsModal === 'function') {
                        addResult('✅ دالة openProjectsModal موجودة', 'success');
                        addResult('✅ زر المشروعات يعمل بشكل صحيح', 'success');
                    } else {
                        addResult('❌ دالة openProjectsModal غير موجودة', 'error');
                    }
                } else {
                    addResult('❌ زر المشروعات غير موجود في DOM', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار زر المشروعات: ' + error.message, 'error');
            }
        }

        function testProjectsFunctions() {
            addResult('🔍 بدء اختبار دوال المشروعات...', 'info');

            const projectFunctions = [
                'openProjectsModal',
                'closeProjectsModal',
                'saveProject',
                'editProject',
                'deleteProject',
                'populateProjectsList',
                'createProjectCard',
                'saveProjectsToLocalStorage',
                'loadProjectsFromLocalStorage',
                'exportProjectsToExcel',
                'printProjects',
                'getProjectOptionsForDays',
                'updateProjectsDropdownIfOpen'
            ];

            let functionsFound = 0;

            projectFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            const percentage = Math.round((functionsFound / projectFunctions.length) * 100);
            addResult(`📊 تم العثور على ${functionsFound}/${projectFunctions.length} دالة (${percentage}%)`,
                     percentage >= 80 ? 'success' : 'warning');
        }

        function testProjectsData() {
            addResult('🔍 بدء اختبار بيانات المشروعات...', 'info');

            try {
                // التحقق من وجود متغير المشروعات
                if (typeof projects !== 'undefined') {
                    addResult('✅ متغير projects موجود', 'success');
                    addResult(`📊 عدد المشروعات: ${projects.length}`, 'info');

                    if (projects.length > 0) {
                        addResult('✅ توجد مشروعات في النظام', 'success');
                        projects.forEach((project, index) => {
                            addResult(`📋 مشروع ${index + 1}: ${project.name}`, 'info');
                        });
                    } else {
                        addResult('⚠️ لا توجد مشروعات', 'warning');
                    }
                } else {
                    addResult('❌ متغير projects غير موجود', 'error');
                }

                // التحقق من متغيرات أخرى
                if (typeof projectModalMode !== 'undefined') {
                    addResult('✅ متغير projectModalMode موجود', 'success');
                } else {
                    addResult('❌ متغير projectModalMode غير موجود', 'error');
                }

                if (typeof editingProjectId !== 'undefined') {
                    addResult('✅ متغير editingProjectId موجود', 'success');
                } else {
                    addResult('❌ متغير editingProjectId غير موجود', 'error');
                }

            } catch (error) {
                addResult('❌ خطأ في اختبار البيانات: ' + error.message, 'error');
            }
        }

        function testProjectsModal() {
            addResult('🔍 بدء اختبار نافذة المشروعات...', 'info');

            try {
                const modal = document.getElementById('projectsModal');
                if (modal) {
                    addResult('✅ نافذة المشروعات موجودة في DOM', 'success');

                    // اختبار عناصر النافذة
                    const elements = [
                        { id: 'projectsModalTitle', name: 'عنوان النافذة' },
                        { id: 'projectForm', name: 'نموذج المشروع' },
                        { id: 'projectName', name: 'حقل اسم المشروع' },
                        { id: 'projectDescription', name: 'حقل وصف المشروع' },
                        { id: 'projectPO', name: 'حقل PO' },
                        { id: 'projectsList', name: 'قائمة المشروعات' }
                    ];

                    elements.forEach(element => {
                        const el = document.getElementById(element.id);
                        if (el) {
                            addResult(`✅ ${element.name} موجود`, 'success');
                        } else {
                            addResult(`❌ ${element.name} غير موجود`, 'error');
                        }
                    });
                } else {
                    addResult('❌ نافذة المشروعات غير موجودة في DOM', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار النافذة: ' + error.message, 'error');
            }
        }

        function testProjectCRUD() {
            addResult('🔍 بدء اختبار عمليات CRUD للمشروعات...', 'info');

            try {
                // اختبار إضافة مشروع
                if (typeof saveProject === 'function') {
                    addResult('✅ دالة إضافة المشروع متاحة', 'success');
                } else {
                    addResult('❌ دالة إضافة المشروع غير متاحة', 'error');
                }

                // اختبار تعديل مشروع
                if (typeof editProject === 'function') {
                    addResult('✅ دالة تعديل المشروع متاحة', 'success');
                } else {
                    addResult('❌ دالة تعديل المشروع غير متاحة', 'error');
                }

                // اختبار حذف مشروع
                if (typeof deleteProject === 'function') {
                    addResult('✅ دالة حذف المشروع متاحة', 'success');
                } else {
                    addResult('❌ دالة حذف المشروع غير متاحة', 'error');
                }

                addResult('💡 لاختبار العمليات الفعلية، افتح نافذة المشروعات', 'info');

            } catch (error) {
                addResult('❌ خطأ في اختبار CRUD: ' + error.message, 'error');
            }
        }

        function testProjectsDropdown() {
            addResult('🔍 بدء اختبار القائمة المنسدلة للمشروعات...', 'info');

            try {
                if (typeof getProjectOptionsForDays === 'function') {
                    addResult('✅ دالة إنشاء خيارات المشروعات متاحة', 'success');

                    const options = getProjectOptionsForDays();
                    if (options && options.length > 0) {
                        addResult('✅ تم إنشاء خيارات المشروعات بنجاح', 'success');
                        addResult(`📊 طول محتوى الخيارات: ${options.length} حرف`, 'info');
                    } else {
                        addResult('⚠️ لم يتم إنشاء خيارات أو الخيارات فارغة', 'warning');
                    }
                } else {
                    addResult('❌ دالة إنشاء خيارات المشروعات غير متاحة', 'error');
                }

                if (typeof updateProjectsDropdownIfOpen === 'function') {
                    addResult('✅ دالة تحديث القائمة المنسدلة متاحة', 'success');
                } else {
                    addResult('❌ دالة تحديث القائمة المنسدلة غير متاحة', 'error');
                }

            } catch (error) {
                addResult('❌ خطأ في اختبار القائمة المنسدلة: ' + error.message, 'error');
            }
        }

        function testProjectsExport() {
            addResult('🔍 بدء اختبار التصدير والطباعة...', 'info');

            try {
                if (typeof exportProjectsToExcel === 'function') {
                    addResult('✅ دالة تصدير Excel متاحة', 'success');
                } else {
                    addResult('❌ دالة تصدير Excel غير متاحة', 'error');
                }

                if (typeof printProjects === 'function') {
                    addResult('✅ دالة الطباعة متاحة', 'success');
                } else {
                    addResult('❌ دالة الطباعة غير متاحة', 'error');
                }

                addResult('💡 لاختبار التصدير والطباعة الفعلية، افتح نافذة المشروعات', 'info');

            } catch (error) {
                addResult('❌ خطأ في اختبار التصدير والطباعة: ' + error.message, 'error');
            }
        }

        function runAllTests() {
            addResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');

            // مسح النتائج السابقة
            testStats = { total: 0, passed: 0, failed: 0 };

            // تشغيل جميع الاختبارات
            setTimeout(() => testProjectsButton(), 100);
            setTimeout(() => testProjectsFunctions(), 500);
            setTimeout(() => testProjectsData(), 1000);
            setTimeout(() => testProjectsModal(), 1500);
            setTimeout(() => testProjectCRUD(), 2000);
            setTimeout(() => testProjectsDropdown(), 2500);
            setTimeout(() => testProjectsExport(), 3000);

            setTimeout(() => {
                addResult('🎉 تم الانتهاء من جميع الاختبارات!', 'success');
                generateReport();
            }, 3500);
        }

        function generateReport() {
            addResult('📊 إنشاء تقرير شامل...', 'info');

            const report = `
📋 تقرير اختبار نظام المشروعات

📊 الإحصائيات:
• إجمالي الاختبارات: ${testStats.total}
• اختبارات ناجحة: ${testStats.passed}
• اختبارات فاشلة: ${testStats.failed}
• معدل النجاح: ${testStats.total > 0 ? Math.round((testStats.passed / testStats.total) * 100) : 0}%

🔧 حالة النظام:
• دوال المشروعات: ${typeof openProjectsModal === 'function' ? 'متاحة' : 'غير متاحة'}
• بيانات المشروعات: ${typeof projects !== 'undefined' ? `${projects.length} مشروع` : 'غير متاحة'}
• نافذة المشروعات: ${document.getElementById('projectsModal') ? 'موجودة' : 'غير موجودة'}

💡 التوصيات:
${testStats.failed === 0 ? '✅ النظام يعمل بشكل ممتاز!' : '⚠️ يحتاج إلى إصلاحات'}
            `;

            console.log(report);
            alert(report);

            addResult('✅ تم إنشاء التقرير الشامل', 'success');
        }

        function openOriginalApp() {
            addResult('🔗 فتح التطبيق الأصلي...', 'info');
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎯 تم تحميل صفحة اختبار المشروعات', 'success');
            addResult('🚀 جاهز لبدء الاختبارات...', 'info');

            // اختبار سريع تلقائي
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع ---', 'info');
                if (typeof openProjectsModal === 'function') {
                    addResult('✅ نظام المشروعات متاح', 'success');
                } else {
                    addResult('❌ نظام المشروعات غير متاح', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
