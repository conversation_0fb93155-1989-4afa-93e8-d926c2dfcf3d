<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - فلتر المشروع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .test-steps h3 {
            color: #007bff;
            margin-top: 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .test-button.secondary {
            background: #007bff;
        }
        .test-button.secondary:hover {
            background: #0056b3;
        }
        .result-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> اختبار نهائي - فلتر المشروع</h1>
            <p>اختبار شامل للتأكد من عمل فلتر المشروع في نافذة أيام الحضور</p>
        </div>

        <div class="test-steps">
            <h3><i class="fas fa-list-ol"></i> خطوات الاختبار:</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إنشاء بيانات تجريبية:</strong> اضغط الزر أدناه لإنشاء موظف مع سجلات لمشروعات مختلفة
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>فتح نافذة السجلات:</strong> اضغط لفتح نافذة أيام الحضور
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>البحث عن الفلتر:</strong> ابحث عن قسم "فلتر المشروع" في أعلى النافذة
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>اختبار الفلتر:</strong> جرب تحديد مشروعات مختلفة من القائمة المنسدلة
            </div>
        </div>

        <div style="text-align: center;">
            <button class="test-button" onclick="createTestData()">
                <i class="fas fa-database"></i> إنشاء بيانات تجريبية
            </button>
            
            <button class="test-button secondary" onclick="openRecordsWindow()">
                <i class="fas fa-calendar-alt"></i> فتح نافذة السجلات
            </button>
        </div>

        <div class="info-box">
            <h4><i class="fas fa-info-circle"></i> ما يجب أن تراه:</h4>
            <ul style="text-align: right; margin: 10px 0;">
                <li>قسم فلتر المشروع بخلفية رمادية فاتحة</li>
                <li>قائمة منسدلة تحتوي على "جميع المشروعات" + أسماء المشروعات</li>
                <li>مؤشر يعرض عدد السجلات المعروضة</li>
                <li>زر صغير لمسح الفلتر</li>
            </ul>
        </div>

        <div class="warning-box">
            <h4><i class="fas fa-exclamation-triangle"></i> إذا لم يظهر الفلتر:</h4>
            <p>تأكد من أن النافذة تحتوي على سجلات أيام عمل للموظف. الفلتر يظهر فقط عندما تكون هناك بيانات للعرض.</p>
        </div>

        <div id="result" style="display: none;"></div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            if (type === 'success') {
                resultDiv.className = 'result-box';
                resultDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            } else if (type === 'warning') {
                resultDiv.className = 'warning-box';
                resultDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            } else {
                resultDiv.className = 'info-box';
                resultDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            }
        }

        function createTestData() {
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 88888,
                    name: 'موظف اختبار الفلتر النهائي',
                    position: 'مطور',
                    employeeCode: 'FINAL88888',
                    employmentType: 'monthly',
                    basicSalary: 6000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 88888);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء سجلات متنوعة لمشروعات مختلفة
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 88888,
                        projectName: 'مشروع تطوير الموقع الإلكتروني',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 22,
                        absenceDays: 2,
                        overtimeHours: 15,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 88888,
                        projectName: 'مشروع النظام المحاسبي',
                        startDate: '2024-02-01',
                        endDate: '2024-02-29',
                        calculatedDays: 20,
                        absenceDays: 1,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 88888,
                        projectName: 'مشروع تطوير التطبيق المحمول',
                        startDate: '2024-03-01',
                        endDate: '2024-03-31',
                        calculatedDays: 23,
                        absenceDays: 0,
                        overtimeHours: 12,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 4,
                        employeeId: 88888,
                        projectName: 'مشروع تطوير الموقع الإلكتروني',
                        startDate: '2024-04-01',
                        endDate: '2024-04-30',
                        calculatedDays: 21,
                        absenceDays: 1,
                        overtimeHours: 6,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 5,
                        employeeId: 88888,
                        projectName: 'مشروع التدريب والتطوير',
                        startDate: '2024-05-01',
                        endDate: '2024-05-31',
                        calculatedDays: 22,
                        absenceDays: 3,
                        overtimeHours: 10,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة للموظف
                employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 88888);
                
                // إضافة السجلات الجديدة
                testRecords.forEach(record => {
                    employeeDaysData.push(record);
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();
                
                showResult(`تم إنشاء الموظف "${testEmployee.name}" مع ${testRecords.length} سجل أيام عمل لـ 4 مشروعات مختلفة`, 'success');
                
            } catch (error) {
                showResult('خطأ في إنشاء البيانات: ' + error.message, 'warning');
            }
        }

        function openRecordsWindow() {
            try {
                if (!employees.find(emp => emp.id === 88888)) {
                    showResult('لا يوجد موظف تجريبي. اضغط "إنشاء بيانات تجريبية" أولاً', 'warning');
                    return;
                }
                
                if (typeof viewDaysRecords === 'function') {
                    viewDaysRecords(88888);
                    
                    // فحص الفلتر بعد فترة قصيرة
                    setTimeout(() => {
                        const modal = document.getElementById('daysRecordsModal');
                        if (modal) {
                            const projectFilter = modal.querySelector('#projectFilter');
                            const filterContainer = modal.querySelector('#projectFilterContainer');
                            
                            if (projectFilter && filterContainer) {
                                const options = projectFilter.querySelectorAll('option');
                                showResult(`✅ فلتر المشروع يعمل بنجاح! يحتوي على ${options.length} خيار (${options.length - 1} مشروع + خيار "جميع المشروعات")`, 'success');
                            } else {
                                showResult('❌ فلتر المشروع غير موجود في النافذة', 'warning');
                            }
                        } else {
                            showResult('❌ لم يتم فتح النافذة', 'warning');
                        }
                    }, 1000);
                    
                } else {
                    showResult('دالة viewDaysRecords غير موجودة', 'warning');
                }
                
            } catch (error) {
                showResult('خطأ في فتح النافذة: ' + error.message, 'warning');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewDaysRecords === 'function') {
                    showResult('النظام جاهز للاختبار! اضغط "إنشاء بيانات تجريبية" للبدء', 'info');
                } else {
                    showResult('خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'warning');
                }
            }, 500);
        });
    </script>
</body>
</html>
