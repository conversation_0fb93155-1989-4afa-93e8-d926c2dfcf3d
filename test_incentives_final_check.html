<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - نظام الحوافز والمكافآت</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }

        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .button.secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .button.warning {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-item.success {
            color: #28a745;
        }

        .result-item.error {
            color: #dc3545;
        }

        .result-item.warning {
            color: #ffc107;
        }

        .result-item.info {
            color: #17a2b8;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .card h4 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .icon {
            font-size: 1.2em;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 اختبار نهائي - نظام الحوافز والمكافآت</h1>
            <p>التحقق من جميع الدوال والمتغيرات المطلوبة</p>
        </div>

        <div class="content">
            <div class="status info" id="mainStatus">
                <strong>الحالة:</strong> جاري التحضير للاختبار...
            </div>

            <div class="test-section">
                <h3>🔧 إعدادات الاختبار</h3>
                <button class="button" onclick="runFullTest()">
                    <span class="icon">🚀</span>
                    تشغيل الاختبار الشامل
                </button>
                <button class="button secondary" onclick="testIncentivesSystem()">
                    <span class="icon">🏆</span>
                    اختبار نظام الحوافز
                </button>
                <button class="button warning" onclick="testSalaryCalculation()">
                    <span class="icon">💰</span>
                    اختبار حساب الراتب
                </button>
            </div>

            <div class="grid">
                <div class="card">
                    <h4>📊 نتائج الاختبار</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <div id="progressText">0% مكتمل</div>
                    <div class="results" id="testResults">
                        <div class="result-item info">انتظار بدء الاختبار...</div>
                    </div>
                </div>

                <div class="card">
                    <h4>🔍 تفاصيل النظام</h4>
                    <div id="systemDetails">
                        <div class="result-item info">جاري فحص النظام...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('mainStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const item = document.createElement('div');
            item.className = `result-item ${type}`;
            item.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(item);
            results.scrollTop = results.scrollHeight;
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = percentage + '%';
            progressText.textContent = Math.round(percentage) + '% مكتمل';
        }

        function addSystemDetail(message, type = 'info') {
            const details = document.getElementById('systemDetails');
            const item = document.createElement('div');
            item.className = `result-item ${type}`;
            item.textContent = message;
            details.appendChild(item);
        }

        function runFullTest() {
            updateStatus('جاري تشغيل الاختبار الشامل...', 'warning');
            addResult('🚀 بدء الاختبار الشامل للنظام', 'info');
            
            // مسح النتائج السابقة
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('systemDetails').innerHTML = '';
            
            let progress = 0;
            let totalTests = 0;
            let passedTests = 0;

            // اختبار 1: المتغيرات الأساسية
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            if (typeof employees !== 'undefined' && Array.isArray(employees)) {
                addResult('✅ متغير employees موجود ومعرف بشكل صحيح', 'success');
                passedTests++;
            } else {
                addResult('❌ متغير employees غير موجود أو غير صحيح', 'error');
            }

            // اختبار 2: متغيرات الحوافز
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            if (typeof incentivesRewardsData !== 'undefined' && Array.isArray(incentivesRewardsData)) {
                addResult('✅ متغير incentivesRewardsData موجود ومعرف بشكل صحيح', 'success');
                passedTests++;
            } else {
                addResult('❌ متغير incentivesRewardsData غير موجود أو غير صحيح', 'error');
            }

            // اختبار 3: متغير أيام العمل
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            if (typeof employeeDaysData !== 'undefined' && Array.isArray(employeeDaysData)) {
                addResult('✅ متغير employeeDaysData موجود ومعرف بشكل صحيح', 'success');
                passedTests++;
            } else {
                addResult('❌ متغير employeeDaysData غير موجود أو غير صحيح', 'error');
            }

            // اختبار 4: دوال الحوافز
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            const incentiveFunctions = [
                'loadIncentivesRewardsFromLocalStorage',
                'viewIncentivesRewards',
                'updateIncentivesRewardsTable',
                'closeIncentivesRewardsModal'
            ];
            
            let incentiveFunctionsFound = 0;
            incentiveFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    incentiveFunctionsFound++;
                }
            });
            
            if (incentiveFunctionsFound === incentiveFunctions.length) {
                addResult(`✅ جميع دوال الحوافز موجودة (${incentiveFunctionsFound}/${incentiveFunctions.length})`, 'success');
                passedTests++;
            } else {
                addResult(`❌ بعض دوال الحوافز مفقودة (${incentiveFunctionsFound}/${incentiveFunctions.length})`, 'error');
            }

            // اختبار 5: دوال حساب الراتب
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            const salaryFunctions = [
                'calculateEmployeeSalary',
                'openSalaryCalculator',
                'closeSalaryCalculatorModal',
                'saveSalaryCalculation'
            ];
            
            let salaryFunctionsFound = 0;
            salaryFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    salaryFunctionsFound++;
                }
            });
            
            if (salaryFunctionsFound === salaryFunctions.length) {
                addResult(`✅ جميع دوال حساب الراتب موجودة (${salaryFunctionsFound}/${salaryFunctions.length})`, 'success');
                passedTests++;
            } else {
                addResult(`❌ بعض دوال حساب الراتب مفقودة (${salaryFunctionsFound}/${salaryFunctions.length})`, 'error');
            }

            // اختبار 6: دوال أيام العمل
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            const daysFunctions = [
                'loadDaysDataFromLocalStorage',
                'saveDaysDataToLocalStorage'
            ];
            
            let daysFunctionsFound = 0;
            daysFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    daysFunctionsFound++;
                }
            });
            
            if (daysFunctionsFound === daysFunctions.length) {
                addResult(`✅ جميع دوال أيام العمل موجودة (${daysFunctionsFound}/${daysFunctions.length})`, 'success');
                passedTests++;
            } else {
                addResult(`❌ بعض دوال أيام العمل مفقودة (${daysFunctionsFound}/${daysFunctions.length})`, 'error');
            }

            // اختبار 7: دالة getMonthName
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            if (typeof getMonthName === 'function') {
                try {
                    const monthName = getMonthName(1);
                    if (monthName === 'يناير') {
                        addResult('✅ دالة getMonthName تعمل بشكل صحيح', 'success');
                        passedTests++;
                    } else {
                        addResult(`❌ دالة getMonthName ترجع قيمة خاطئة: ${monthName}`, 'error');
                    }
                } catch (error) {
                    addResult('❌ خطأ في دالة getMonthName: ' + error.message, 'error');
                }
            } else {
                addResult('❌ دالة getMonthName غير موجودة', 'error');
            }

            // اختبار 8: localStorage
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            try {
                const testData = { test: 'data' };
                localStorage.setItem('testKey', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('testKey'));
                
                if (retrieved && retrieved.test === 'data') {
                    addResult('✅ localStorage يعمل بشكل صحيح', 'success');
                    passedTests++;
                } else {
                    addResult('❌ مشكلة في localStorage', 'error');
                }
                
                localStorage.removeItem('testKey');
            } catch (error) {
                addResult('❌ خطأ في localStorage: ' + error.message, 'error');
            }

            // اختبار 9: دالة حساب الراتب
            totalTests++;
            updateProgress((totalTests / 10) * 100);
            
            if (typeof calculateEmployeeSalary === 'function') {
                try {
                    // إنشاء موظف تجريبي
                    const testEmployee = {
                        id: 999,
                        name: 'موظف تجريبي',
                        employmentType: 'monthly',
                        basicSalary: 5000
                    };
                    
                    const salaryData = calculateEmployeeSalary(testEmployee);
                    
                    if (salaryData && typeof salaryData.netSalary === 'number') {
                        addResult('✅ دالة حساب الراتب تعمل بشكل صحيح', 'success');
                        passedTests++;
                    } else {
                        addResult('❌ دالة حساب الراتب لا تعطي نتيجة صحيحة', 'error');
                    }
                } catch (error) {
                    addResult('❌ خطأ في دالة حساب الراتب: ' + error.message, 'error');
                }
            } else {
                addResult('❌ دالة calculateEmployeeSalary غير موجودة', 'error');
            }

            // اختبار 10: النتيجة النهائية
            totalTests++;
            updateProgress(100);
            
            const successRate = (passedTests / totalTests) * 100;
            
            if (successRate >= 90) {
                addResult(`🎉 اختبار ممتاز! نجح ${passedTests} من ${totalTests} اختبار (${successRate.toFixed(1)}%)`, 'success');
                updateStatus(`✅ النظام يعمل بشكل ممتاز! معدل النجاح: ${successRate.toFixed(1)}%`, 'success');
            } else if (successRate >= 70) {
                addResult(`✅ اختبار جيد! نجح ${passedTests} من ${totalTests} اختبار (${successRate.toFixed(1)}%)`, 'warning');
                updateStatus(`⚠️ النظام يعمل بشكل جيد مع بعض المشاكل. معدل النجاح: ${successRate.toFixed(1)}%`, 'warning');
            } else {
                addResult(`❌ اختبار ضعيف! نجح ${passedTests} من ${totalTests} اختبار (${successRate.toFixed(1)}%)`, 'error');
                updateStatus(`❌ النظام يحتاج إلى إصلاحات. معدل النجاح: ${successRate.toFixed(1)}%`, 'error');
            }

            // إضافة تفاصيل النظام
            addSystemDetail(`إجمالي الموظفين: ${employees ? employees.length : 0}`, 'info');
            addSystemDetail(`سجلات الحوافز: ${incentivesRewardsData ? incentivesRewardsData.length : 0}`, 'info');
            addSystemDetail(`سجلات أيام العمل: ${employeeDaysData ? employeeDaysData.length : 0}`, 'info');
            addSystemDetail(`معدل نجاح الاختبار: ${successRate.toFixed(1)}%`, successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error');
        }

        function testIncentivesSystem() {
            updateStatus('جاري اختبار نظام الحوافز...', 'warning');
            addResult('🏆 بدء اختبار نظام الحوافز والمكافآت', 'info');
            
            // اختبار دوال الحوافز
            const incentiveFunctions = [
                'loadIncentivesRewardsFromLocalStorage',
                'viewIncentivesRewards',
                'updateIncentivesRewardsTable',
                'closeIncentivesRewardsModal',
                'updateIncentivesSummary'
            ];
            
            let functionsFound = 0;
            incentiveFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            if (functionsFound === incentiveFunctions.length) {
                updateStatus('✅ نظام الحوافز يعمل بشكل كامل!', 'success');
            } else {
                updateStatus(`⚠️ نظام الحوافز يحتاج إصلاحات (${functionsFound}/${incentiveFunctions.length})`, 'warning');
            }
        }

        function testSalaryCalculation() {
            updateStatus('جاري اختبار حساب الراتب...', 'warning');
            addResult('💰 بدء اختبار نظام حساب الراتب', 'info');
            
            // اختبار دوال حساب الراتب
            const salaryFunctions = [
                'calculateEmployeeSalary',
                'openSalaryCalculator',
                'closeSalaryCalculatorModal',
                'saveSalaryCalculation',
                'getDailyWage',
                'getMonthName'
            ];
            
            let functionsFound = 0;
            salaryFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            if (functionsFound === salaryFunctions.length) {
                updateStatus('✅ نظام حساب الراتب يعمل بشكل كامل!', 'success');
            } else {
                updateStatus(`⚠️ نظام حساب الراتب يحتاج إصلاحات (${functionsFound}/${salaryFunctions.length})`, 'warning');
            }
        }

        // تشغيل فحص سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined') {
                    updateStatus('✅ النظام الأساسي محمل بنجاح', 'success');
                } else {
                    updateStatus('❌ فشل في تحميل النظام الأساسي', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
