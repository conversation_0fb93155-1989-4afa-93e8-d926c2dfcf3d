<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 الاختبار النهائي - فلتر المشروع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .step-card {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        .step-number {
            position: absolute;
            top: -15px;
            right: 20px;
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        .big-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        .big-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .result {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
            text-align: center;
            display: none;
        }
        .result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-fill {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .checklist {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .checklist-item.completed {
            background: #d4edda;
            border-color: #28a745;
        }
        .checklist-item.failed {
            background: #f8d7da;
            border-color: #dc3545;
        }
        .check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        .check-icon.pending {
            background: #6c757d;
            color: white;
        }
        .check-icon.completed {
            background: #28a745;
            color: white;
        }
        .check-icon.failed {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bullseye"></i> الاختبار النهائي</h1>
            <p style="font-size: 1.2em; color: #666;">اختبار شامل لفلتر المشروع في نافذة أيام الحضور</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill">0%</div>
        </div>

        <div class="step-card">
            <div class="step-number">1</div>
            <h3>إنشاء البيانات التجريبية</h3>
            <p>إنشاء موظف مع سجلات أيام عمل لمشروعات مختلفة</p>
            <button class="big-button" onclick="createTestData()">
                <i class="fas fa-database"></i> إنشاء البيانات
            </button>
        </div>

        <div class="step-card">
            <div class="step-number">2</div>
            <h3>فتح نافذة السجلات</h3>
            <p>فتح نافذة أيام الحضور والتحقق من وجود فلتر المشروع</p>
            <button class="big-button" onclick="openAndTest()">
                <i class="fas fa-window-restore"></i> فتح واختبار
            </button>
        </div>

        <div id="result" class="result"></div>

        <div class="checklist">
            <h3><i class="fas fa-tasks"></i> قائمة التحقق:</h3>
            
            <div class="checklist-item" id="check1">
                <div class="check-icon pending" id="icon1">?</div>
                <span>إنشاء موظف تجريبي مع سجلات متعددة</span>
            </div>
            
            <div class="checklist-item" id="check2">
                <div class="check-icon pending" id="icon2">?</div>
                <span>فتح نافذة أيام الحضور بنجاح</span>
            </div>
            
            <div class="checklist-item" id="check3">
                <div class="check-icon pending" id="icon3">?</div>
                <span>وجود عنصر فلتر المشروع (#projectFilterContainer)</span>
            </div>
            
            <div class="checklist-item" id="check4">
                <div class="check-icon pending" id="icon4">?</div>
                <span>وجود قائمة منسدلة للمشروعات (#projectFilter)</span>
            </div>
            
            <div class="checklist-item" id="check5">
                <div class="check-icon pending" id="icon5">?</div>
                <span>وجود خيارات المشروعات في القائمة</span>
            </div>
            
            <div class="checklist-item" id="check6">
                <div class="check-icon pending" id="icon6">?</div>
                <span>وجود مؤشر حالة الفلتر (#filterStatus)</span>
            </div>
            
            <div class="checklist-item" id="check7">
                <div class="check-icon pending" id="icon7">?</div>
                <span>وجود زر مسح الفلتر</span>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        let currentProgress = 0;
        
        function updateProgress(percentage) {
            currentProgress = percentage;
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
            progressFill.textContent = Math.round(percentage) + '%';
        }
        
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'exclamation-triangle'}"></i> ${message}`;
        }
        
        function updateChecklistItem(itemNumber, status, message = '') {
            const item = document.getElementById(`check${itemNumber}`);
            const icon = document.getElementById(`icon${itemNumber}`);
            
            item.className = `checklist-item ${status}`;
            icon.className = `check-icon ${status}`;
            
            if (status === 'completed') {
                icon.textContent = '✓';
            } else if (status === 'failed') {
                icon.textContent = '✗';
            } else {
                icon.textContent = '?';
            }
            
            if (message) {
                const span = item.querySelector('span');
                span.textContent = message;
            }
        }

        function createTestData() {
            try {
                updateChecklistItem(1, 'pending');
                updateProgress(10);
                
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 66666,
                    name: 'موظف الاختبار النهائي',
                    position: 'مطور',
                    employeeCode: 'TEST66666',
                    employmentType: 'monthly',
                    basicSalary: 6000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 66666);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء سجلات متنوعة لمشروعات مختلفة
                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 66666,
                        projectName: 'مشروع تطوير النظام',
                        startDate: '2024-01-01',
                        endDate: '2024-01-31',
                        calculatedDays: 25,
                        absenceDays: 2,
                        overtimeHours: 20,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 66666,
                        projectName: 'مشروع الصيانة',
                        startDate: '2024-02-01',
                        endDate: '2024-02-29',
                        calculatedDays: 22,
                        absenceDays: 1,
                        overtimeHours: 15,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 66666,
                        projectName: 'مشروع التحديث',
                        startDate: '2024-03-01',
                        endDate: '2024-03-31',
                        calculatedDays: 24,
                        absenceDays: 0,
                        overtimeHours: 18,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 4,
                        employeeId: 66666,
                        projectName: 'مشروع تطوير النظام',
                        startDate: '2024-04-01',
                        endDate: '2024-04-30',
                        calculatedDays: 23,
                        absenceDays: 1,
                        overtimeHours: 12,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 66666);
                
                // إضافة السجلات الجديدة
                testRecords.forEach(record => {
                    employeeDaysData.push(record);
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();
                
                updateChecklistItem(1, 'completed');
                updateProgress(30);
                showResult(`✅ تم إنشاء الموظف "${testEmployee.name}" مع ${testRecords.length} سجلات لـ 3 مشروعات مختلفة`, 'success');
                
            } catch (error) {
                updateChecklistItem(1, 'failed');
                showResult('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function openAndTest() {
            try {
                if (!employees.find(emp => emp.id === 66666)) {
                    showResult('❌ يرجى إنشاء البيانات التجريبية أولاً', 'error');
                    return;
                }
                
                updateChecklistItem(2, 'pending');
                updateProgress(40);
                
                // فتح النافذة
                viewDaysRecords(66666);
                
                // اختبار النافذة بعد فترة قصيرة
                setTimeout(() => {
                    testFilterElements();
                }, 1000);
                
                updateChecklistItem(2, 'completed');
                updateProgress(50);
                
            } catch (error) {
                updateChecklistItem(2, 'failed');
                showResult('❌ خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function testFilterElements() {
            try {
                const modal = document.getElementById('daysRecordsModal');
                
                if (!modal) {
                    updateChecklistItem(2, 'failed');
                    showResult('❌ النافذة غير مفتوحة', 'error');
                    return;
                }
                
                updateProgress(60);
                
                // فحص عنصر فلتر المشروع
                const filterContainer = modal.querySelector('#projectFilterContainer');
                if (filterContainer) {
                    updateChecklistItem(3, 'completed');
                    updateProgress(70);
                } else {
                    updateChecklistItem(3, 'failed');
                    showResult('❌ عنصر فلتر المشروع غير موجود', 'error');
                    return;
                }
                
                // فحص قائمة المشروعات
                const projectFilter = modal.querySelector('#projectFilter');
                if (projectFilter) {
                    updateChecklistItem(4, 'completed');
                    updateProgress(80);
                    
                    // فحص خيارات المشروعات
                    const options = projectFilter.querySelectorAll('option');
                    if (options.length > 1) { // أكثر من خيار "جميع المشروعات"
                        updateChecklistItem(5, 'completed', `وجود ${options.length} خيار (${options.length - 1} مشروع + "جميع المشروعات")`);
                        updateProgress(85);
                    } else {
                        updateChecklistItem(5, 'failed');
                        showResult('❌ لا توجد خيارات مشروعات في القائمة', 'error');
                        return;
                    }
                } else {
                    updateChecklistItem(4, 'failed');
                    showResult('❌ قائمة المشروعات غير موجودة', 'error');
                    return;
                }
                
                // فحص مؤشر الحالة
                const filterStatus = modal.querySelector('#filterStatus');
                if (filterStatus) {
                    updateChecklistItem(6, 'completed');
                    updateProgress(90);
                } else {
                    updateChecklistItem(6, 'failed');
                    showResult('❌ مؤشر حالة الفلتر غير موجود', 'error');
                    return;
                }
                
                // فحص زر المسح
                const clearButton = modal.querySelector('button[onclick*="clearProjectFilter"]');
                if (clearButton) {
                    updateChecklistItem(7, 'completed');
                    updateProgress(100);
                    showResult('🎉 نجح الاختبار! فلتر المشروع يعمل بشكل مثالي', 'success');
                } else {
                    updateChecklistItem(7, 'failed');
                    showResult('❌ زر مسح الفلتر غير موجود', 'error');
                    return;
                }
                
            } catch (error) {
                showResult('❌ خطأ في اختبار العناصر: ' + error.message, 'error');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewDaysRecords === 'function') {
                    showResult('✅ النظام جاهز للاختبار!', 'success');
                } else {
                    showResult('❌ خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
