<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الأنواع المخصصة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .test-button {
            background: #6f42c1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a2d91;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .current-types {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .type-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار إدارة الأنواع المخصصة للحوافز والمكافآت</h1>
        
        <div class="test-section">
            <h3>📋 الحالة الحالية</h3>
            <div id="currentStatus" class="status info">
                جاري تحميل البيانات...
            </div>
            
            <div>
                <h4>الأنواع المخصصة الحالية:</h4>
                <div id="currentTypes" class="current-types">
                    <!-- سيتم تحميل الأنواع هنا -->
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الوظائف</h3>
            
            <button class="test-button" onclick="testOpenManageModal()">
                ⚙️ فتح نافذة إدارة الأنواع
            </button>
            
            <button class="test-button" onclick="testAddSampleTypes()">
                ➕ إضافة أنواع تجريبية
            </button>
            
            <button class="test-button" onclick="testLoadTypes()">
                🔄 إعادة تحميل الأنواع
            </button>
            
            <button class="test-button" onclick="testClearAllTypes()">
                🗑️ مسح جميع الأنواع
            </button>
        </div>

        <div class="test-section">
            <h3>📝 سجل الاختبارات</h3>
            <div id="testLog" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                <!-- سيتم إضافة سجل الاختبارات هنا -->
            </div>
        </div>

        <div class="test-section">
            <h3>ℹ️ تعليمات الاختبار</h3>
            <ol>
                <li>انقر على "فتح نافذة إدارة الأنواع" لاختبار النافذة الرئيسية</li>
                <li>استخدم "إضافة أنواع تجريبية" لإضافة بيانات اختبار</li>
                <li>جرب إضافة وتعديل وحذف الأنواع من النافذة</li>
                <li>تحقق من أن التغييرات تُحفظ بشكل صحيح</li>
                <li>اختبر التعامل مع الأنواع المحذوفة المستخدمة في سجلات</li>
            </ol>
        </div>
    </div>

    <script>
        // بيانات تجريبية
        var employees = [
            { id: 1, name: "أحمد محمد", employeeCode: "EMP001" },
            { id: 2, name: "فاطمة علي", employeeCode: "EMP002" }
        ];
        
        var incentivesData = [];
        var customIncentiveTypes = [];

        // دالة لتسجيل الأحداث
        function logTest(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#495057';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // دالة لتحديث الحالة الحالية
        function updateCurrentStatus() {
            const statusDiv = document.getElementById('currentStatus');
            const typesDiv = document.getElementById('currentTypes');
            
            statusDiv.className = 'status success';
            statusDiv.innerHTML = `✅ تم تحميل ${customIncentiveTypes.length} نوع مخصص`;
            
            if (customIncentiveTypes.length === 0) {
                typesDiv.innerHTML = '<p style="text-align: center; color: #6c757d;">لا توجد أنواع مخصصة</p>';
            } else {
                let typesHTML = '';
                customIncentiveTypes.forEach((type, index) => {
                    typesHTML += `
                        <div class="type-item">
                            <div>
                                <span style="font-size: 16px;">${type.icon}</span>
                                <strong>${type.name}</strong>
                                <small style="color: #6c757d;"> (${type.id})</small>
                            </div>
                            <small style="color: #6c757d;">
                                ${new Date(type.createdAt).toLocaleDateString('ar-EG')}
                            </small>
                        </div>
                    `;
                });
                typesDiv.innerHTML = typesHTML;
            }
            
            logTest(`تم تحديث العرض - ${customIncentiveTypes.length} نوع مخصص`, 'success');
        }

        // اختبار فتح نافذة إدارة الأنواع
        function testOpenManageModal() {
            try {
                if (typeof manageCustomIncentiveTypes === 'function') {
                    manageCustomIncentiveTypes();
                    logTest('✅ تم فتح نافذة إدارة الأنواع بنجاح', 'success');
                } else {
                    logTest('❌ دالة manageCustomIncentiveTypes غير موجودة', 'error');
                }
            } catch (error) {
                logTest(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        // اختبار إضافة أنواع تجريبية
        function testAddSampleTypes() {
            try {
                const sampleTypes = [
                    { name: 'حافز مبيعات', icon: '💰' },
                    { name: 'مكافأة إبداع', icon: '💡' },
                    { name: 'حافز حضور', icon: '⏰' },
                    { name: 'مكافأة فريق', icon: '👥' }
                ];

                sampleTypes.forEach(sample => {
                    const newType = {
                        id: 'custom_incentive_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                        name: sample.name,
                        icon: sample.icon,
                        createdAt: new Date().toISOString()
                    };
                    
                    customIncentiveTypes.push(newType);
                    logTest(`➕ تم إضافة النوع: ${sample.name}`, 'success');
                });

                // حفظ الأنواع
                if (typeof saveCustomIncentiveTypes === 'function') {
                    saveCustomIncentiveTypes();
                }

                updateCurrentStatus();
                logTest(`✅ تم إضافة ${sampleTypes.length} نوع تجريبي بنجاح`, 'success');
            } catch (error) {
                logTest(`❌ خطأ في إضافة الأنواع التجريبية: ${error.message}`, 'error');
            }
        }

        // اختبار إعادة تحميل الأنواع
        function testLoadTypes() {
            try {
                if (typeof loadCustomIncentiveTypes === 'function') {
                    loadCustomIncentiveTypes();
                    updateCurrentStatus();
                    logTest('🔄 تم إعادة تحميل الأنواع من localStorage', 'success');
                } else {
                    logTest('❌ دالة loadCustomIncentiveTypes غير موجودة', 'error');
                }
            } catch (error) {
                logTest(`❌ خطأ في إعادة تحميل الأنواع: ${error.message}`, 'error');
            }
        }

        // اختبار مسح جميع الأنواع
        function testClearAllTypes() {
            if (confirm('هل أنت متأكد من مسح جميع الأنواع المخصصة؟')) {
                try {
                    customIncentiveTypes = [];
                    
                    if (typeof saveCustomIncentiveTypes === 'function') {
                        saveCustomIncentiveTypes();
                    }
                    
                    updateCurrentStatus();
                    logTest('🗑️ تم مسح جميع الأنواع المخصصة', 'success');
                } catch (error) {
                    logTest(`❌ خطأ في مسح الأنواع: ${error.message}`, 'error');
                }
            }
        }

        // تحميل ملف app.js
        const script = document.createElement('script');
        script.src = 'app.js';
        script.onload = function() {
            logTest('✅ تم تحميل app.js بنجاح', 'success');
            
            // تحميل الأنواع المخصصة
            if (typeof loadCustomIncentiveTypes === 'function') {
                loadCustomIncentiveTypes();
            }
            
            updateCurrentStatus();
        };
        script.onerror = function() {
            logTest('❌ فشل في تحميل app.js', 'error');
            document.getElementById('currentStatus').className = 'status error';
            document.getElementById('currentStatus').innerHTML = '❌ فشل في تحميل ملف app.js';
        };
        document.head.appendChild(script);

        // تحديث العرض كل 5 ثوان
        setInterval(updateCurrentStatus, 5000);
    </script>
</body>
</html>
