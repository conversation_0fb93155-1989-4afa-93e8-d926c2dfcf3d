<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إخفاء حقل إدخال العطلة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #6c757d;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn.primary {
            background: #495057;
        }

        .btn.primary:hover {
            background: #343a40;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 3px solid #6c757d;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .instructions ol {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
            font-weight: 500;
        }

        .feature-highlight {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 3px solid #28a745;
        }

        .feature-highlight h4 {
            color: #28a745;
            margin-bottom: 10px;
        }

        .feature-highlight p {
            color: #495057;
            margin-bottom: 5px;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار إخفاء حقل إدخال العطلة</h1>
            <p>التحقق من إخفاء حقل الإدخال تلقائياً بعد إضافة عطلة رسمية جديدة</p>
        </div>

        <div class="content">
            <!-- الميزة المحدثة -->
            <div class="feature-highlight">
                <h4>✅ الميزة المحدثة:</h4>
                <p><strong>إخفاء تلقائي:</strong> بعد إضافة عطلة رسمية جديدة، يتم إخفاء حقل الإدخال تلقائياً</p>
                <p><strong>تنظيف الواجهة:</strong> مسح النص من حقل الإدخال وإعادة تعيين القائمة المنسدلة</p>
                <p><strong>تجربة أفضل:</strong> واجهة أكثر نظافة وسهولة في الاستخدام</p>
            </div>

            <!-- تعليمات الاختبار -->
            <div class="instructions">
                <h4>📋 خطوات اختبار الميزة:</h4>
                <ol>
                    <li>أنشئ موظف تجريبي</li>
                    <li>افتح نافذة الإجازات</li>
                    <li>في قسم "استحقاق الإجازات" اختر "عطلات رسمية"</li>
                    <li>من القائمة المنسدلة اختر "إضافة عطلة جديدة"</li>
                    <li>ستظهر حقل إدخال مع زر "إضافة"</li>
                    <li>أدخل اسم عطلة جديدة (مثل: "عيد الاستقلال")</li>
                    <li>اضغط زر "إضافة"</li>
                    <li><strong>لاحظ:</strong> حقل الإدخال سيختفي تلقائياً</li>
                    <li>العطلة الجديدة ستظهر في القائمة وتكون محددة</li>
                </ol>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn primary" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="checkHideFunction()">
                        فحص دالة الإخفاء
                    </button>
                    <button class="btn" onclick="loadExistingHolidays()">
                        تحميل العطلات الموجودة
                    </button>
                </div>
            </div>

            <!-- قسم اختبار الميزة -->
            <div class="test-section">
                <h3>🧪 اختبار إخفاء حقل الإدخال</h3>
                <div class="grid">
                    <button class="btn primary" onclick="openVacationsWindow()">
                        فتح نافذة الإجازات
                    </button>
                    <button class="btn" onclick="testHideFeature()">
                        اختبار الإخفاء التلقائي
                    </button>
                    <button class="btn" onclick="addTestHoliday()">
                        إضافة عطلة تجريبية
                    </button>
                    <button class="btn" onclick="verifyHideFunction()">
                        التحقق من الإخفاء
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>🚀 اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn primary" onclick="runFullHideTest()">
                        تشغيل اختبار شامل
                    </button>
                    <button class="btn" onclick="testMultipleHolidays()">
                        اختبار عطلات متعددة
                    </button>
                    <button class="btn" onclick="testUICleanup()">
                        اختبار تنظيف الواجهة
                    </button>
                    <button class="btn" onclick="clearTestData()">
                        مسح البيانات التجريبية
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status">
                <strong>الحالة:</strong> جاهز لاختبار إخفاء حقل إدخال العطلة...
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry">تم تحميل صفحة اختبار إخفاء حقل إدخال العطلة</div>
                <div class="log-entry">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 777,
                name: 'محمد علي (اختبار إخفاء الحقل)',
                position: 'مطور واجهات',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 10000,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 777)) {
                employees.push(testEmployee);
                addLog('تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                addLog('الموظف التجريبي موجود بالفعل', 'info');
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function checkHideFunction() {
            const checks = [
                { name: 'دالة addNewOfficialHoliday', test: () => typeof addNewOfficialHoliday === 'function' },
                { name: 'دالة handleEntitlementOfficialHolidayChange', test: () => typeof handleEntitlementOfficialHolidayChange === 'function' },
                { name: 'دالة saveCustomHolidaysToLocalStorage', test: () => typeof saveCustomHolidaysToLocalStorage === 'function' },
                { name: 'متغير customOfficialHolidays', test: () => typeof customOfficialHolidays !== 'undefined' }
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                    addLog(`✓ ${check.name}`, 'success');
                } else {
                    results.push(`✗ ${check.name}`);
                    addLog(`✗ ${check.name}`, 'error');
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص دالة الإخفاء: ${passed}/${checks.length} دالة متاحة`, status);
        }

        function loadExistingHolidays() {
            if (typeof loadCustomHolidaysFromLocalStorage === 'function') {
                loadCustomHolidaysFromLocalStorage();
                const count = typeof customOfficialHolidays !== 'undefined' ? customOfficialHolidays.length : 0;
                addLog(`تم تحميل ${count} عطلة مخصصة من localStorage`, 'success');
                updateStatus(`تم تحميل ${count} عطلة مخصصة`, 'success');
            } else {
                addLog('دالة تحميل العطلات غير متاحة', 'error');
                updateStatus('خطأ: دالة تحميل العطلات غير متاحة', 'error');
            }
        }

        function openVacationsWindow() {
            if (!employees.find(emp => emp.id === 777)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            if (typeof openVacationsModal === 'function') {
                openVacationsModal(777);
                addLog('تم فتح نافذة الإجازات للموظف التجريبي', 'success');
                updateStatus('تم فتح نافذة الإجازات - جرب إضافة عطلة جديدة!', 'success');
            } else {
                addLog('دالة openVacationsModal غير متاحة', 'error');
                updateStatus('خطأ: دالة فتح نافذة الإجازات غير متاحة', 'error');
            }
        }

        function testHideFeature() {
            addLog('✓ اختر "عطلات رسمية" من قائمة نوع الإجازة', 'success');
            addLog('✓ اختر "إضافة عطلة جديدة" من القائمة المنسدلة', 'success');
            addLog('✓ أدخل اسم عطلة جديدة واضغط "إضافة"', 'success');
            addLog('✓ لاحظ اختفاء حقل الإدخال تلقائياً', 'success');
            updateStatus('✓ اتبع الخطوات لاختبار الإخفاء التلقائي', 'success');
        }

        function addTestHoliday() {
            const modal = document.getElementById('vacationsModal');
            if (!modal || modal.style.display !== 'block') {
                addLog('يرجى فتح نافذة الإجازات أولاً', 'warning');
                updateStatus('يرجى فتح نافذة الإجازات أولاً', 'warning');
                return;
            }

            // محاولة إضافة عطلة تجريبية برمجياً
            const holidayName = 'عيد الاستقلال التجريبي';
            const inputField = document.getElementById('entitlementNewHolidayName');
            
            if (inputField) {
                inputField.value = holidayName;
                addLog('تم ملء حقل اسم العطلة بـ: ' + holidayName, 'success');
                addLog('اضغط زر "إضافة" لاختبار الإخفاء التلقائي', 'info');
                updateStatus('جاهز لاختبار الإخفاء - اضغط زر "إضافة"', 'success');
            } else {
                addLog('حقل إدخال العطلة غير مرئي - اختر "إضافة عطلة جديدة" أولاً', 'warning');
                updateStatus('اختر "إضافة عطلة جديدة" من القائمة أولاً', 'warning');
            }
        }

        function verifyHideFunction() {
            const modal = document.getElementById('vacationsModal');
            if (!modal || modal.style.display !== 'block') {
                addLog('نافذة الإجازات غير مفتوحة', 'warning');
                updateStatus('افتح نافذة الإجازات أولاً', 'warning');
                return;
            }

            const inputContainer = document.getElementById('entitlementNewHolidayInput');
            if (inputContainer) {
                const isHidden = inputContainer.style.display === 'none';
                if (isHidden) {
                    addLog('✅ حقل الإدخال مخفي بنجاح', 'success');
                    updateStatus('✅ حقل الإدخال مخفي كما هو متوقع', 'success');
                } else {
                    addLog('⚠️ حقل الإدخال لا يزال مرئياً', 'warning');
                    updateStatus('⚠️ حقل الإدخال لا يزال مرئياً', 'warning');
                }
            } else {
                addLog('لم يتم العثور على حقل الإدخال', 'error');
                updateStatus('خطأ: لم يتم العثور على حقل الإدخال', 'error');
            }
        }

        function testMultipleHolidays() {
            addLog('✓ جرب إضافة عدة عطلات متتالية', 'success');
            addLog('✓ تأكد من إخفاء الحقل بعد كل إضافة', 'success');
            addLog('✓ تحقق من ظهور العطلات في القائمة', 'success');
            updateStatus('✓ اختبر إضافة عطلات متعددة', 'success');
        }

        function testUICleanup() {
            addLog('✓ تحقق من مسح النص من حقل الإدخال', 'success');
            addLog('✓ تحقق من إعادة تعيين القائمة المنسدلة', 'success');
            addLog('✓ تحقق من تحديد العطلة الجديدة تلقائياً', 'success');
            updateStatus('✓ اختبر تنظيف واجهة المستخدم', 'success');
        }

        function runFullHideTest() {
            addLog('بدء تشغيل الاختبار الشامل لإخفاء حقل الإدخال...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => checkHideFunction(), 1000);
            setTimeout(() => loadExistingHolidays(), 1500);
            setTimeout(() => openVacationsWindow(), 2000);
            setTimeout(() => testHideFeature(), 3000);
            setTimeout(() => addTestHoliday(), 4000);
            setTimeout(() => testMultipleHolidays(), 5000);
            setTimeout(() => testUICleanup(), 6000);

            setTimeout(() => {
                addLog('انتهى الاختبار الشامل لإخفاء حقل الإدخال بنجاح! 🎉', 'success');
                updateStatus('انتهى الاختبار الشامل بنجاح! جرب الميزة الآن 🎉', 'success');
            }, 7000);
        }

        function clearTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار إخفاء الحقل؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 777);
                
                // مسح العطلات التجريبية
                if (typeof customOfficialHolidays !== 'undefined') {
                    customOfficialHolidays = customOfficialHolidays.filter(holiday => 
                        !holiday.name.includes('تجريبي')
                    );
                    if (typeof saveCustomHolidaysToLocalStorage === 'function') {
                        saveCustomHolidaysToLocalStorage();
                    }
                }

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('تم مسح جميع بيانات اختبار إخفاء الحقل', 'warning');
                updateStatus('تم مسح جميع بيانات اختبار إخفاء الحقل', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار إخفاء حقل إدخال العطلة - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
