<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - نظام المشروعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .status {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        
        .status h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .log {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram"></i> اختبار سريع - نظام المشروعات</h1>
            <p>اختبار سريع وشامل لجميع وظائف نظام إدارة المشروعات</p>
        </div>

        <!-- أزرار الاختبار -->
        <div class="test-buttons">
            <button class="btn btn-primary" onclick="quickTest()">
                <i class="fas fa-play"></i> اختبار سريع
            </button>
            
            <button class="btn btn-success" onclick="openProjectsModal()">
                <i class="fas fa-external-link-alt"></i> فتح نافذة المشروعات
            </button>
            
            <button class="btn btn-warning" onclick="testAllFunctions()">
                <i class="fas fa-cogs"></i> اختبار جميع الدوال
            </button>
            
            <button class="btn btn-danger" onclick="clearLog()">
                <i class="fas fa-trash"></i> مسح السجل
            </button>
            
            <button class="btn btn-primary" onclick="window.open('/', '_blank')">
                <i class="fas fa-home"></i> التطبيق الأصلي
            </button>
            
            <button class="btn btn-success" onclick="window.open('test_projects_system.html', '_blank')">
                <i class="fas fa-flask"></i> اختبار متقدم
            </button>
        </div>

        <!-- حالة النظام -->
        <div class="status">
            <h3><i class="fas fa-info-circle"></i> حالة النظام</h3>
            <div class="status-item">
                <span>زر المشروعات:</span>
                <span class="status-indicator" id="buttonStatus">غير محدد</span>
            </div>
            <div class="status-item">
                <span>دوال المشروعات:</span>
                <span class="status-indicator" id="functionsStatus">غير محدد</span>
            </div>
            <div class="status-item">
                <span>بيانات المشروعات:</span>
                <span class="status-indicator" id="dataStatus">غير محدد</span>
            </div>
            <div class="status-item">
                <span>نافذة المشروعات:</span>
                <span class="status-indicator" id="modalStatus">غير محدد</span>
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="log" id="log">
            <div class="log-entry log-info">[تحميل] جاهز لبدء الاختبارات...</div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-indicator status-${status}`;
        }

        function quickTest() {
            log('🚀 بدء الاختبار السريع...', 'info');
            
            // اختبار زر المشروعات
            const projectsBtn = document.getElementById('projectsBtn');
            if (projectsBtn) {
                log('✅ زر المشروعات موجود', 'success');
                updateStatus('buttonStatus', 'success', 'موجود');
            } else {
                log('❌ زر المشروعات غير موجود', 'error');
                updateStatus('buttonStatus', 'error', 'غير موجود');
            }
            
            // اختبار الدوال
            const functions = ['openProjectsModal', 'saveProject', 'editProject', 'deleteProject'];
            let functionsFound = 0;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ دالة ${funcName} موجودة`, 'success');
                    functionsFound++;
                } else {
                    log(`❌ دالة ${funcName} غير موجودة`, 'error');
                }
            });
            
            if (functionsFound === functions.length) {
                updateStatus('functionsStatus', 'success', 'جميع الدوال موجودة');
            } else {
                updateStatus('functionsStatus', 'warning', `${functionsFound}/${functions.length} موجودة`);
            }
            
            // اختبار البيانات
            if (typeof projects !== 'undefined') {
                log(`✅ بيانات المشروعات موجودة (${projects.length} مشروع)`, 'success');
                updateStatus('dataStatus', 'success', `${projects.length} مشروع`);
            } else {
                log('❌ بيانات المشروعات غير موجودة', 'error');
                updateStatus('dataStatus', 'error', 'غير موجودة');
            }
            
            // اختبار النافذة
            const modal = document.getElementById('projectsModal');
            if (modal) {
                log('✅ نافذة المشروعات موجودة', 'success');
                updateStatus('modalStatus', 'success', 'موجودة');
            } else {
                log('❌ نافذة المشروعات غير موجودة', 'error');
                updateStatus('modalStatus', 'error', 'غير موجودة');
            }
            
            log('🎉 انتهى الاختبار السريع!', 'success');
        }

        function testAllFunctions() {
            log('🔧 اختبار جميع دوال المشروعات...', 'info');
            
            const allFunctions = [
                'openProjectsModal',
                'closeProjectsModal',
                'saveProject',
                'editProject',
                'deleteProject',
                'populateProjectsList',
                'createProjectCard',
                'saveProjectsToLocalStorage',
                'loadProjectsFromLocalStorage',
                'exportProjectsToExcel',
                'printProjects',
                'getProjectOptionsForDays',
                'updateProjectsDropdownIfOpen'
            ];
            
            let found = 0;
            allFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ ${funcName}`, 'success');
                    found++;
                } else {
                    log(`❌ ${funcName}`, 'error');
                }
            });
            
            const percentage = Math.round((found / allFunctions.length) * 100);
            log(`📊 النتيجة: ${found}/${allFunctions.length} دالة (${percentage}%)`, 
                percentage >= 80 ? 'success' : 'warning');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 
                '<div class="log-entry log-info">[مسح] تم مسح السجل...</div>';
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة الاختبار السريع', 'success');
            
            setTimeout(() => {
                log('🔍 تشغيل اختبار تلقائي...', 'info');
                quickTest();
            }, 1000);
        });
    </script>
</body>
</html>
