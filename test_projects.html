<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر المشروعات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .projects-btn {
            background-color: #FF9800;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 20px;
            margin: 10px;
            cursor: pointer;
            font-size: 16px;
        }
        .projects-btn:hover {
            background-color: #F57C00;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>اختبار زر المشروعات</h1>
    
    <div class="test-container">
        <h2>اختبار وظائف المشروعات</h2>
        
        <div>
            <button class="projects-btn" onclick="testProjectsButton()">
                <i class="fas fa-project-diagram"></i> المشروعات
            </button>
            <div id="button-status" class="status info">انقر على الزر لاختباره</div>
        </div>
        
        <div>
            <h3>اختبارات إضافية:</h3>
            <button onclick="testProjectsFunctions()" style="background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">
                اختبار دوال المشروعات
            </button>
            <button onclick="testProjectsData()" style="background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">
                اختبار بيانات المشروعات
            </button>
            <button onclick="openOriginalPage()" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px;">
                فتح الصفحة الأصلية
            </button>
        </div>
    </div>
    
    <div id="results">
        <h3>نتائج الاختبارات:</h3>
        <p>جاهز لبدء الاختبارات...</p>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            p.style.color = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff';
            p.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function updateButtonStatus(success, message) {
            const status = document.getElementById('button-status');
            if (success) {
                status.className = 'status success';
                status.textContent = '✓ ' + message;
            } else {
                status.className = 'status error';
                status.textContent = '✗ ' + message;
            }
        }

        function testProjectsButton() {
            addResult('بدء اختبار زر المشروعات...');
            
            try {
                // التحقق من وجود دالة فتح المشروعات
                if (typeof openProjectsModal === 'function') {
                    addResult('✓ دالة openProjectsModal موجودة', 'success');
                    updateButtonStatus(true, 'زر المشروعات يعمل بشكل صحيح');
                    
                    // محاولة استدعاء الدالة (اختياري)
                    // openProjectsModal();
                    addResult('يمكنك الآن النقر على زر المشروعات في الصفحة الأصلية', 'success');
                } else {
                    addResult('✗ دالة openProjectsModal غير موجودة', 'error');
                    updateButtonStatus(false, 'دالة openProjectsModal غير موجودة');
                }
            } catch (error) {
                addResult('✗ خطأ في اختبار زر المشروعات: ' + error.message, 'error');
                updateButtonStatus(false, 'خطأ في الاختبار');
            }
        }

        function testProjectsFunctions() {
            addResult('=== بدء اختبار دوال المشروعات ===');
            
            const projectFunctions = [
                'openProjectsModal',
                'closeProjectsModal',
                'saveProject',
                'editProject',
                'deleteProject',
                'populateProjectsList',
                'createProjectCard',
                'getProjectStatusText',
                'saveProjectsToLocalStorage',
                'loadProjectsFromLocalStorage',
                'exportProjectsToExcel',
                'printProjects'
            ];
            
            let functionsFound = 0;
            
            projectFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✓ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`✗ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            addResult(`=== النتيجة: ${functionsFound}/${projectFunctions.length} دالة موجودة ===`);
            
            if (functionsFound === projectFunctions.length) {
                addResult('✓ جميع دوال المشروعات موجودة!', 'success');
            } else {
                addResult('⚠ بعض دوال المشروعات مفقودة', 'error');
            }
        }

        function testProjectsData() {
            addResult('=== بدء اختبار بيانات المشروعات ===');
            
            try {
                // التحقق من وجود متغير المشروعات
                if (typeof projects !== 'undefined') {
                    addResult('✓ متغير projects موجود', 'success');
                    addResult(`عدد المشروعات: ${projects.length}`, 'info');
                    
                    if (projects.length > 0) {
                        addResult('✓ توجد مشروعات تجريبية', 'success');
                        projects.forEach((project, index) => {
                            addResult(`مشروع ${index + 1}: ${project.name}`, 'info');
                        });
                    } else {
                        addResult('⚠ لا توجد مشروعات', 'error');
                    }
                } else {
                    addResult('✗ متغير projects غير موجود', 'error');
                }
                
                // التحقق من متغيرات أخرى
                if (typeof projectModalMode !== 'undefined') {
                    addResult('✓ متغير projectModalMode موجود', 'success');
                } else {
                    addResult('✗ متغير projectModalMode غير موجود', 'error');
                }
                
                if (typeof editingProjectId !== 'undefined') {
                    addResult('✓ متغير editingProjectId موجود', 'success');
                } else {
                    addResult('✗ متغير editingProjectId غير موجود', 'error');
                }
                
            } catch (error) {
                addResult('✗ خطأ في اختبار البيانات: ' + error.message, 'error');
            }
            
            addResult('=== انتهاء اختبار البيانات ===');
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('تم تحميل صفحة اختبار المشروعات');
            addResult('جاهز لبدء الاختبارات...');
            
            // اختبار تلقائي سريع
            setTimeout(() => {
                addResult('--- اختبار تلقائي سريع ---');
                if (typeof openProjectsModal === 'function') {
                    addResult('✓ دالة المشروعات متاحة', 'success');
                } else {
                    addResult('✗ دالة المشروعات غير متاحة', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
