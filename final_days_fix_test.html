<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح مشكلة الأيام المحسوبة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .button.primary { background: #007bff; }
        .button.warning { background: #ffc107; color: #212529; }
        .button.danger { background: #dc3545; }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .test-step {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #6c757d;
        }
        
        .test-step.success { border-left-color: #28a745; }
        .test-step.error { border-left-color: #dc3545; }
        .test-step.warning { border-left-color: #ffc107; }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .fix-summary {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 20px 0;
        }
        
        .fix-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        
        .fix-item:last-child { border-bottom: none; }
        
        .fix-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار إصلاح مشكلة الأيام المحسوبة</h1>
            <p>اختبار نهائي للتأكد من حل مشكلة "يرجى إدخال الأيام المحسوبة"</p>
        </div>

        <div class="fix-summary">
            <h4>🛠️ الإصلاحات المطبقة:</h4>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إعادة بناء دالة saveDaysCalculationFast</strong>
                    <br><small>بحث مباشر في النافذة المفتوحة بدلاً من البحث العام</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>تحسين التحقق من حقل calculatedDays</strong>
                    <br><small>فحص القيمة الخام قبل التحويل لرقم</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>إضافة تسجيل مفصل</strong>
                    <br><small>تتبع دقيق لجميع الحقول والقيم</small>
                </div>
            </div>
            
            <div class="fix-item">
                <div class="fix-icon">✓</div>
                <div>
                    <strong>معالجة أفضل للأخطاء</strong>
                    <br><small>رسائل خطأ واضحة ومفصلة</small>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="button danger" onclick="runCompleteTest()">
                🚀 اختبار شامل
            </button>
            
            <button class="button" onclick="createEmployee()">
                👤 إنشاء موظف
            </button>
            
            <button class="button warning" onclick="openWindow()">
                📝 فتح النافذة
            </button>
            
            <button class="button primary" onclick="testSave()">
                💾 اختبار الحفظ
            </button>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار النهائي...
        </div>

        <div id="steps"></div>

        <div class="console" id="console">
            [FINAL_DAYS_FIX_TEST] اختبار إصلاح مشكلة الأيام المحسوبة جاهز...<br>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function addStep(message, type = 'info') {
            const stepsDiv = document.getElementById('steps');
            const stepDiv = document.createElement('div');
            stepDiv.className = `test-step ${type}`;
            stepDiv.innerHTML = message;
            stepsDiv.appendChild(stepDiv);
        }

        function createEmployee() {
            addLog('إنشاء موظف تجريبي...');
            
            try {
                if (!employees.find(emp => emp.id === 999)) {
                    employees.push({
                        id: 999,
                        name: 'سارة أحمد - اختبار الأيام المحسوبة',
                        position: 'محاسبة',
                        employeeCode: 'DAYS999',
                        employmentType: 'monthly',
                        basicSalary: 6000
                    });
                    addStep('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                    addLog('✓ تم إنشاء موظف تجريبي');
                    updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
                } else {
                    addStep('⚠️ الموظف التجريبي موجود مسبقاً', 'warning');
                    updateStatus('الموظف التجريبي موجود مسبقاً', 'info');
                }
            } catch (error) {
                addStep('❌ فشل في إنشاء الموظف: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في إنشاء الموظف: ' + error.message, 'error');
            }
        }

        function openWindow() {
            addLog('فتح نافذة حساب الأيام...');
            
            try {
                if (!employees.find(emp => emp.id === 999)) {
                    addStep('❌ الموظف التجريبي غير موجود - يرجى إنشاؤه أولاً', 'error');
                    updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                createEnhancedDaysCalculator(999);
                
                setTimeout(() => {
                    const modal = document.getElementById('enhancedDaysModal');
                    if (modal) {
                        addStep('✅ تم فتح نافذة حساب الأيام بنجاح', 'success');
                        addLog('✓ تم فتح النافذة بنجاح');
                        updateStatus('تم فتح النافذة - الآن املأ البيانات', 'success');
                        
                        // ملء البيانات تلقائياً
                        fillTestData();
                    } else {
                        addStep('❌ فشل في فتح النافذة', 'error');
                        addLog('✗ فشل في فتح النافذة');
                        updateStatus('فشل في فتح النافذة', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addStep('❌ خطأ في فتح النافذة: ' + error.message, 'error');
                addLog('✗ خطأ: ' + error.message);
                updateStatus('خطأ في فتح النافذة: ' + error.message, 'error');
            }
        }

        function fillTestData() {
            addLog('ملء البيانات التجريبية...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) return;
            
            setTimeout(() => {
                const projectSelect = modal.querySelector('#projectSelect');
                const monthSelect = modal.querySelector('#monthSelect');
                const yearSelect = modal.querySelector('#yearSelect');
                const calculatedDays = modal.querySelector('#calculatedDays');
                const absenceDays = modal.querySelector('#absenceDays');
                const overtimeHours = modal.querySelector('#overtimeHours');
                
                let filledCount = 0;
                
                if (projectSelect) {
                    projectSelect.value = 'مشروع اختبار الأيام المحسوبة';
                    filledCount++;
                    addLog('✓ تم ملء المشروع');
                }
                
                if (monthSelect) {
                    monthSelect.value = '2';
                    filledCount++;
                    addLog('✓ تم ملء الشهر (فبراير)');
                }
                
                if (yearSelect) {
                    yearSelect.value = '2024';
                    filledCount++;
                    addLog('✓ تم ملء السنة');
                }
                
                if (calculatedDays) {
                    calculatedDays.value = '28';
                    filledCount++;
                    addLog('✓ تم ملء الأيام المحسوبة: 28 يوم');
                }
                
                if (absenceDays) {
                    absenceDays.value = '1';
                    filledCount++;
                    addLog('✓ تم ملء أيام الغياب');
                }
                
                if (overtimeHours) {
                    overtimeHours.value = '4';
                    filledCount++;
                    addLog('✓ تم ملء الساعات الإضافية');
                }
                
                addStep(`✅ تم ملء ${filledCount} حقل بنجاح`, 'success');
                updateStatus('تم ملء جميع البيانات - الآن اختبر الحفظ', 'success');
            }, 300);
        }

        function testSave() {
            addLog('🔄 اختبار دالة الحفظ المحسنة...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addStep('❌ النافذة غير مفتوحة - يرجى فتحها أولاً', 'error');
                updateStatus('يرجى فتح النافذة أولاً', 'error');
                return;
            }
            
            try {
                addLog('📞 استدعاء saveDaysCalculationFast...');
                const result = saveDaysCalculationFast();
                
                if (result === true) {
                    addStep('🎉 نجح الحفظ! تم حل مشكلة الأيام المحسوبة', 'success');
                    addLog('✅ نجح الحفظ!');
                    updateStatus('🎉 ممتاز! تم حل المشكلة نهائياً - الحفظ يعمل بشكل مثالي', 'success');
                } else if (result === false) {
                    addStep('❌ فشل الحفظ - راجع console للتفاصيل', 'error');
                    addLog('❌ فشل الحفظ');
                    updateStatus('❌ فشل الحفظ - راجع console للتفاصيل', 'error');
                } else {
                    addStep('⚠️ نتيجة غير متوقعة: ' + result, 'warning');
                    addLog('⚠️ نتيجة غير متوقعة: ' + result);
                    updateStatus('نتيجة غير متوقعة من دالة الحفظ', 'error');
                }
            } catch (error) {
                addStep('❌ خطأ في دالة الحفظ: ' + error.message, 'error');
                addLog('❌ خطأ: ' + error.message);
                updateStatus('خطأ في دالة الحفظ: ' + error.message, 'error');
            }
        }

        function runCompleteTest() {
            updateStatus('🚀 بدء الاختبار الشامل...', 'info');
            addLog('=== بدء الاختبار الشامل لإصلاح مشكلة الأيام المحسوبة ===');
            
            // مسح الخطوات السابقة
            document.getElementById('steps').innerHTML = '';
            
            addStep('🔧 بدء الاختبار الشامل لإصلاح مشكلة الأيام المحسوبة', 'info');
            
            // خطوة 1: إنشاء الموظف
            setTimeout(() => {
                createEmployee();
            }, 500);
            
            // خطوة 2: فتح النافذة
            setTimeout(() => {
                openWindow();
            }, 2000);
            
            // خطوة 3: اختبار الحفظ
            setTimeout(() => {
                testSave();
            }, 4500);
            
            // النتيجة النهائية
            setTimeout(() => {
                addLog('=== انتهاء الاختبار الشامل ===');
                addStep('📋 انتهاء الاختبار - راجع النتائج أعلاه', 'info');
                addLog('🎯 إذا ظهرت رسالة "نجح الحفظ" فقد تم حل المشكلة نهائياً');
            }, 6000);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل اختبار إصلاح مشكلة الأيام المحسوبة');
            updateStatus('جاهز لبدء الاختبار النهائي', 'info');
        });
    </script>
</body>
</html>
