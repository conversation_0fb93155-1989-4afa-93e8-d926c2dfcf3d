<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الخطأ النحوي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        
        .header h1 {
            color: #28a745;
            margin: 0;
        }
        
        .status {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
        
        .btn-primary:hover {
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .log {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار إصلاح الخطأ النحوي</h1>
            <p>التحقق من إصلاح خطأ "Identifier 'projects' has already been declared"</p>
        </div>

        <div class="status">
            <h3>حالة النظام</h3>
            <div class="status-item">
                <span>تحميل ملف JavaScript:</span>
                <span class="status-indicator" id="jsStatus">جاري التحقق...</span>
            </div>
            <div class="status-item">
                <span>متغير projects:</span>
                <span class="status-indicator" id="projectsStatus">جاري التحقق...</span>
            </div>
            <div class="status-item">
                <span>دوال المشروعات:</span>
                <span class="status-indicator" id="functionsStatus">جاري التحقق...</span>
            </div>
            <div class="status-item">
                <span>زر المشروعات:</span>
                <span class="status-indicator" id="buttonStatus">جاري التحقق...</span>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="btn" onclick="runTest()">🔍 تشغيل الاختبار</button>
            <button class="btn btn-primary" onclick="openProjectsModal()">📋 فتح نافذة المشروعات</button>
            <button class="btn btn-primary" onclick="window.open('/', '_blank')">🏠 التطبيق الأصلي</button>
        </div>

        <div class="log" id="log">
            <div class="log-entry log-info">[تحميل] جاهز لبدء الاختبار...</div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status-indicator status-${status}`;
        }

        function runTest() {
            log('🚀 بدء اختبار إصلاح الخطأ النحوي...', 'info');
            
            // اختبار تحميل ملف JavaScript
            try {
                if (typeof console !== 'undefined') {
                    log('✅ ملف JavaScript تم تحميله بنجاح', 'success');
                    updateStatus('jsStatus', 'success', 'تم التحميل');
                } else {
                    log('❌ مشكلة في تحميل ملف JavaScript', 'error');
                    updateStatus('jsStatus', 'error', 'فشل التحميل');
                }
            } catch (error) {
                log('❌ خطأ في تحميل JavaScript: ' + error.message, 'error');
                updateStatus('jsStatus', 'error', 'خطأ');
            }
            
            // اختبار متغير projects
            try {
                if (typeof projects !== 'undefined') {
                    log(`✅ متغير projects موجود ويحتوي على ${projects.length} مشروع`, 'success');
                    updateStatus('projectsStatus', 'success', `${projects.length} مشروع`);
                } else {
                    log('❌ متغير projects غير موجود', 'error');
                    updateStatus('projectsStatus', 'error', 'غير موجود');
                }
            } catch (error) {
                log('❌ خطأ في متغير projects: ' + error.message, 'error');
                updateStatus('projectsStatus', 'error', 'خطأ');
            }
            
            // اختبار دوال المشروعات
            try {
                const functions = ['openProjectsModal', 'saveProject', 'editProject', 'deleteProject'];
                let functionsFound = 0;
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        log(`✅ دالة ${funcName} موجودة`, 'success');
                        functionsFound++;
                    } else {
                        log(`❌ دالة ${funcName} غير موجودة`, 'error');
                    }
                });
                
                if (functionsFound === functions.length) {
                    updateStatus('functionsStatus', 'success', 'جميع الدوال موجودة');
                } else {
                    updateStatus('functionsStatus', 'error', `${functionsFound}/${functions.length} موجودة`);
                }
            } catch (error) {
                log('❌ خطأ في اختبار الدوال: ' + error.message, 'error');
                updateStatus('functionsStatus', 'error', 'خطأ');
            }
            
            // اختبار زر المشروعات
            try {
                const projectsBtn = document.getElementById('projectsBtn');
                if (projectsBtn) {
                    log('✅ زر المشروعات موجود في DOM', 'success');
                    updateStatus('buttonStatus', 'success', 'موجود');
                } else {
                    log('❌ زر المشروعات غير موجود في DOM', 'error');
                    updateStatus('buttonStatus', 'error', 'غير موجود');
                }
            } catch (error) {
                log('❌ خطأ في اختبار الزر: ' + error.message, 'error');
                updateStatus('buttonStatus', 'error', 'خطأ');
            }
            
            log('🎉 انتهى الاختبار!', 'success');
        }

        // تشغيل اختبار تلقائي عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 تم تحميل صفحة الاختبار', 'success');
            
            setTimeout(() => {
                log('🔍 تشغيل اختبار تلقائي...', 'info');
                runTest();
            }, 1000);
        });

        // التحقق من عدم وجود أخطاء JavaScript
        window.addEventListener('error', function(event) {
            log('❌ خطأ JavaScript: ' + event.message, 'error');
            log('📍 في الملف: ' + event.filename + ' السطر: ' + event.lineno, 'error');
        });
    </script>
</body>
</html>
