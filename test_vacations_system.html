<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الإجازات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #6c757d;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #5a6268;
        }

        .btn.primary {
            background: #495057;
        }

        .btn.primary:hover {
            background: #343a40;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .features-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 3px solid #6c757d;
        }

        .features-list h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .features-list ul {
            margin-right: 20px;
        }

        .features-list li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .vacation-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .vacation-type {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }

        .vacation-type h5 {
            color: #495057;
            margin-bottom: 10px;
        }

        .vacation-type .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .vacation-type .description {
            font-size: 12px;
            color: #6c757d;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏖️ اختبار نظام الإجازات</h1>
            <p>نظام إدارة الإجازات الشامل مع الأقسام الثلاثة والخيارات المتقدمة</p>
        </div>

        <div class="content">
            <!-- ميزات النظام -->
            <div class="features-list">
                <h4>🌟 ميزات نظام الإجازات:</h4>
                <ul>
                    <li><strong>استحقاق الإجازات:</strong> تحديد أيام الإجازة المستحقة حسب النوع والسنة</li>
                    <li><strong>المستهلك من الإجازات:</strong> تتبع الإجازات المأخوذة والمستهلكة</li>
                    <li><strong>القيام بالإجازة:</strong> تسجيل طلبات الإجازة الجديدة مع حساب الأيام تلقائياً</li>
                    <li><strong>أنواع الإجازات:</strong> سنوي، عطلات رسمية</li>
                    <li><strong>خيارات فرعية:</strong> للإجازة السنوية (اعتيادي/عارضة)</li>
                    <li><strong>عطلات مخصصة:</strong> إمكانية إضافة عطلات رسمية جديدة</li>
                    <li><strong>سجلات شاملة:</strong> عرض جميع سجلات الإجازات في جدول منظم</li>
                </ul>
            </div>

            <!-- أنواع الإجازات -->
            <div class="test-section">
                <h3>📋 أنواع الإجازات المدعومة</h3>
                <div class="vacation-types">
                    <div class="vacation-type">
                        <div class="icon">📅</div>
                        <h5>إجازة سنوية</h5>
                        <div class="description">
                            مع خيارات فرعية:<br>
                            • اعتيادي<br>
                            • عارضة
                        </div>
                    </div>
                    <div class="vacation-type">
                        <div class="icon">🎉</div>
                        <h5>عطلات رسمية</h5>
                        <div class="description">
                            عطلات محددة مسبقاً<br>
                            + إمكانية إضافة عطلات جديدة
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn primary" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="checkVacationSystem()">
                        فحص نظام الإجازات
                    </button>
                    <button class="btn" onclick="loadExistingData()">
                        تحميل البيانات الموجودة
                    </button>
                </div>
            </div>

            <!-- قسم اختبار النظام -->
            <div class="test-section">
                <h3>🧪 اختبار نظام الإجازات</h3>
                <div class="grid">
                    <button class="btn primary" onclick="openVacationsWindow()">
                        فتح نافذة الإجازات
                    </button>
                    <button class="btn" onclick="testAnnualVacation()">
                        اختبار الإجازة السنوية
                    </button>
                    <button class="btn" onclick="testOfficialHolidays()">
                        اختبار العطلات الرسمية
                    </button>
                    <button class="btn" onclick="testCustomHoliday()">
                        اختبار إضافة عطلة مخصصة
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>🚀 اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn primary" onclick="runFullVacationTest()">
                        تشغيل اختبار شامل
                    </button>
                    <button class="btn" onclick="testDataPersistence()">
                        اختبار حفظ البيانات
                    </button>
                    <button class="btn" onclick="testVacationCalculations()">
                        اختبار حساب الأيام
                    </button>
                    <button class="btn" onclick="clearVacationData()">
                        مسح بيانات الإجازات
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status">
                <strong>الحالة:</strong> جاهز لاختبار نظام الإجازات...
            </div>

            <!-- سجل الأحداث -->
            <div class="log-container" id="logContainer">
                <div class="log-entry">تم تحميل صفحة اختبار نظام الإجازات</div>
                <div class="log-entry">جاهز لبدء الاختبارات...</div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 555,
                name: 'سارة أحمد (اختبار الإجازات)',
                position: 'مديرة الموارد البشرية',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 12000,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 555)) {
                employees.push(testEmployee);
                addLog('تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                addLog('الموظف التجريبي موجود بالفعل', 'info');
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function checkVacationSystem() {
            const checks = [
                { name: 'دالة openVacationsModal', test: () => typeof openVacationsModal === 'function' },
                { name: 'دالة saveVacationEntitlement', test: () => typeof saveVacationEntitlement === 'function' },
                { name: 'دالة handleEntitlementVacationTypeChange', test: () => typeof handleEntitlementVacationTypeChange === 'function' },
                { name: 'دالة addNewOfficialHoliday', test: () => typeof addNewOfficialHoliday === 'function' },
                { name: 'دالة loadVacationRecords', test: () => typeof loadVacationRecords === 'function' },
                { name: 'متغير vacationsData', test: () => typeof vacationsData !== 'undefined' },
                { name: 'متغير customOfficialHolidays', test: () => typeof customOfficialHolidays !== 'undefined' }
            ];

            let passed = 0;
            let results = [];

            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                    addLog(`✓ ${check.name}`, 'success');
                } else {
                    results.push(`✗ ${check.name}`);
                    addLog(`✗ ${check.name}`, 'error');
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص نظام الإجازات: ${passed}/${checks.length} مكون متاح`, status);
        }

        function loadExistingData() {
            if (typeof loadVacationsDataFromLocalStorage === 'function') {
                loadVacationsDataFromLocalStorage();
                addLog('تم تحميل بيانات الإجازات من localStorage', 'success');
            }

            if (typeof loadCustomHolidaysFromLocalStorage === 'function') {
                loadCustomHolidaysFromLocalStorage();
                addLog('تم تحميل العطلات المخصصة من localStorage', 'success');
            }

            updateStatus('تم تحميل البيانات الموجودة بنجاح', 'success');
        }

        function openVacationsWindow() {
            if (!employees.find(emp => emp.id === 555)) {
                addLog('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            if (typeof openVacationsModal === 'function') {
                openVacationsModal(555);
                addLog('تم فتح نافذة الإجازات للموظف التجريبي', 'success');
                updateStatus('تم فتح نافذة الإجازات - جرب الأقسام الثلاثة!', 'success');
            } else {
                addLog('دالة openVacationsModal غير متاحة', 'error');
                updateStatus('خطأ: دالة فتح نافذة الإجازات غير متاحة', 'error');
            }
        }

        function testAnnualVacation() {
            addLog('✓ جرب اختيار "سنوي" من قائمة نوع الإجازة', 'success');
            addLog('✓ ستظهر قائمة فرعية: اعتيادي/عارضة', 'success');
            updateStatus('✓ اختبر الإجازة السنوية - اختر "سنوي" لرؤية الخيارات الفرعية', 'success');
        }

        function testOfficialHolidays() {
            addLog('✓ جرب اختيار "عطلات رسمية" من قائمة نوع الإجازة', 'success');
            addLog('✓ ستظهر قائمة بالعطلات الرسمية المحددة مسبقاً', 'success');
            addLog('✓ مع خيار "إضافة عطلة جديدة" في النهاية', 'success');
            updateStatus('✓ اختبر العطلات الرسمية - اختر "عطلات رسمية" لرؤية القائمة', 'success');
        }

        function testCustomHoliday() {
            addLog('✓ اختر "عطلات رسمية" ثم "إضافة عطلة جديدة"', 'success');
            addLog('✓ ستظهر حقل إدخال مع زر "إضافة"', 'success');
            addLog('✓ أدخل اسم العطلة واضغط "إضافة" لحفظها', 'success');
            updateStatus('✓ اختبر إضافة عطلة مخصصة - ستُحفظ في القائمة تلقائياً', 'success');
        }

        function testDataPersistence() {
            addLog('بدء اختبار حفظ بيانات الإجازات...', 'info');

            try {
                // اختبار localStorage للإجازات
                const vacationsData = localStorage.getItem('oscoVacationsData');
                if (vacationsData) {
                    const parsed = JSON.parse(vacationsData);
                    addLog(`✓ بيانات الإجازات محفوظة: ${parsed.length} سجل`, 'success');
                } else {
                    addLog('⚠️ لا توجد بيانات إجازات في localStorage', 'warning');
                }

                // اختبار localStorage للعطلات المخصصة
                const customHolidays = localStorage.getItem('oscoCustomOfficialHolidays');
                if (customHolidays) {
                    const parsed = JSON.parse(customHolidays);
                    addLog(`✓ العطلات المخصصة محفوظة: ${parsed.length} عطلة`, 'success');
                } else {
                    addLog('⚠️ لا توجد عطلات مخصصة في localStorage', 'warning');
                }

                updateStatus('تم اختبار حفظ البيانات بنجاح', 'success');

            } catch (error) {
                addLog('✗ خطأ في اختبار حفظ البيانات: ' + error.message, 'error');
                updateStatus('خطأ في اختبار حفظ البيانات', 'error');
            }
        }

        function testVacationCalculations() {
            addLog('✓ في قسم "القيام بالإجازة" اختر تاريخ البداية والنهاية', 'success');
            addLog('✓ سيتم حساب عدد الأيام تلقائياً', 'success');
            addLog('✓ الحساب يشمل يوم البداية والنهاية', 'success');
            updateStatus('✓ اختبر حساب أيام الإجازة التلقائي', 'success');
        }

        function runFullVacationTest() {
            addLog('بدء تشغيل الاختبار الشامل لنظام الإجازات...', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => checkVacationSystem(), 1000);
            setTimeout(() => loadExistingData(), 1500);
            setTimeout(() => openVacationsWindow(), 2000);
            setTimeout(() => testAnnualVacation(), 3000);
            setTimeout(() => testOfficialHolidays(), 4000);
            setTimeout(() => testCustomHoliday(), 5000);
            setTimeout(() => testDataPersistence(), 6000);
            setTimeout(() => testVacationCalculations(), 7000);

            setTimeout(() => {
                addLog('انتهى الاختبار الشامل لنظام الإجازات بنجاح! 🎉', 'success');
                updateStatus('انتهى الاختبار الشامل بنجاح! جرب النظام الآن 🎉', 'success');
            }, 8000);
        }

        function clearVacationData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات الإجازات؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 555);

                // مسح بيانات الإجازات
                if (typeof vacationsData !== 'undefined') {
                    vacationsData = [];
                    localStorage.removeItem('oscoVacationsData');
                }

                // مسح العطلات المخصصة
                if (typeof customOfficialHolidays !== 'undefined') {
                    customOfficialHolidays = [];
                    localStorage.removeItem('oscoCustomOfficialHolidays');
                }

                // مسح السجل
                document.getElementById('logContainer').innerHTML = '';

                addLog('تم مسح جميع بيانات الإجازات', 'warning');
                updateStatus('تم مسح جميع بيانات الإجازات', 'warning');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار نظام الإجازات - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
