<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة الحوافز والمكافآت المحدثة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .comparison-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .comparison-section h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .comparison-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .comparison-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-card h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .comparison-card.advances {
            border-color: #28a745;
        }

        .comparison-card.incentives {
            border-color: #17a2b8;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            justify-content: center;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .test-button.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list .icon {
            color: #28a745;
            font-size: 16px;
        }

        .results-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e9ecef;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-result {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 14px;
            border-right: 4px solid;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border-color: #28a745;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #17a2b8;
        }

        .demo-employee {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .demo-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }

        .demo-info {
            flex: 1;
        }

        .demo-info h4 {
            margin: 0 0 5px 0;
            color: #495057;
        }

        .demo-info p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-balance-scale"></i> مقارنة نوافذ الحوافز والسلف</h1>
            <p>اختبار التشابه بين نافذة الحوافز والمكافآت ونافذة السلف والخصومات</p>
        </div>

        <div class="content">
            <!-- مقارنة الميزات -->
            <div class="comparison-section">
                <h2><i class="fas fa-list-check"></i> مقارنة الميزات</h2>

                <div class="comparison-grid">
                    <div class="comparison-card advances">
                        <h3><i class="fas fa-hand-holding-usd"></i> نافذة السلف والخصومات</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> تصميم بسيط ونظيف</li>
                            <li><i class="fas fa-check icon"></i> نموذج إضافة مدمج</li>
                            <li><i class="fas fa-check icon"></i> جدول عرض السجلات</li>
                            <li><i class="fas fa-check icon"></i> اختيار الشهر والسنة</li>
                            <li><i class="fas fa-check icon"></i> حذف السجلات</li>
                            <li><i class="fas fa-check icon"></i> حفظ تلقائي</li>
                        </ul>
                        <button class="test-button success" onclick="testAdvancesWindow()">
                            <i class="fas fa-play"></i> اختبار نافذة السلف
                        </button>
                    </div>

                    <div class="comparison-card incentives">
                        <h3><i class="fas fa-gift"></i> نافذة الحوافز والمكافآت</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> تصميم مطابق للسلف</li>
                            <li><i class="fas fa-check icon"></i> نموذج إضافة مدمج</li>
                            <li><i class="fas fa-check icon"></i> جدول عرض السجلات</li>
                            <li><i class="fas fa-check icon"></i> اختيار الشهر والسنة</li>
                            <li><i class="fas fa-check icon"></i> حذف السجلات</li>
                            <li><i class="fas fa-check icon"></i> حفظ تلقائي</li>
                        </ul>
                        <button class="test-button info" onclick="testIncentivesWindow()">
                            <i class="fas fa-play"></i> اختبار نافذة الحوافز
                        </button>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="test-button warning" onclick="compareBothWindows()">
                        <i class="fas fa-balance-scale"></i> مقارنة النافذتين جنباً إلى جنب
                    </button>
                </div>
            </div>

            <!-- موظف تجريبي -->
            <div class="comparison-section">
                <h2><i class="fas fa-user-tie"></i> موظف تجريبي للاختبار</h2>

                <div class="demo-employee">
                    <div class="demo-photo">أح</div>
                    <div class="demo-info">
                        <h4>أحمد محمد علي</h4>
                        <p><strong>الكود:</strong> TEST001 | <strong>المنصب:</strong> مطور برمجيات | <strong>النوع:</strong> شهري</p>
                    </div>
                    <div>
                        <button class="test-button success" onclick="createTestEmployee()">
                            <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                        </button>
                    </div>
                </div>
            </div>

            <!-- اختبارات سريعة -->
            <div class="comparison-section">
                <h2><i class="fas fa-rocket"></i> اختبارات سريعة</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="test-button" onclick="testDataConsistency()">
                        <i class="fas fa-database"></i> اختبار تناسق البيانات
                    </button>
                    <button class="test-button" onclick="testUIConsistency()">
                        <i class="fas fa-palette"></i> اختبار تناسق التصميم
                    </button>
                    <button class="test-button" onclick="testFunctionality()">
                        <i class="fas fa-cogs"></i> اختبار الوظائف
                    </button>
                    <button class="test-button" onclick="openOriginalApp()">
                        <i class="fas fa-external-link-alt"></i> فتح التطبيق الأصلي
                    </button>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">🚀 جاهز لاختبار التشابه بين نوافذ الحوافز والسلف...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 9999,
                name: 'أحمد محمد علي',
                position: 'مطور برمجيات',
                employeeCode: 'TEST001',
                nationalId: '12345678901234',
                phone: '01234567890',
                employmentType: 'monthly',
                basicSalary: 8000,
                photo: 'https://randomuser.me/api/portraits/men/45.jpg'
            };

            // إضافة الموظف إلى القائمة
            const existingIndex = employees.findIndex(emp => emp.id === 9999);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي الموجود', 'info');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء موظف تجريبي جديد', 'success');
            }

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult(`✅ تم حفظ الموظف: ${testEmployee.name}`, 'success');
        }

        // دالة لاختبار نافذة السلف والخصومات
        function testAdvancesWindow() {
            addResult('🔍 اختبار نافذة السلف والخصومات...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewAdvancesDeductions(9999);
                addResult('✅ تم فتح نافذة السلف والخصومات بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة السلف: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار نافذة الحوافز والمكافآت
        function testIncentivesWindow() {
            addResult('🔍 اختبار نافذة الحوافز والمكافآت...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(9999);
                addResult('✅ تم فتح نافذة الحوافز والمكافآت بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة الحوافز: ${error.message}`, 'error');
            }
        }

        // دالة لمقارنة النافذتين جنباً إلى جنب
        function compareBothWindows() {
            addResult('🔄 مقارنة النافذتين جنباً إلى جنب...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                // فتح نافذة السلف أولاً
                viewAdvancesDeductions(9999);
                addResult('✅ تم فتح نافذة السلف والخصومات', 'success');

                // انتظار قليل ثم فتح نافذة الحوافز
                setTimeout(() => {
                    try {
                        viewIncentivesRewards(9999);
                        addResult('✅ تم فتح نافذة الحوافز والمكافآت', 'success');
                        addResult('🎯 يمكنك الآن مقارنة النافذتين والتنقل بينهما', 'info');
                    } catch (error) {
                        addResult(`❌ خطأ في فتح نافذة الحوافز: ${error.message}`, 'error');
                    }
                }, 500);

            } catch (error) {
                addResult(`❌ خطأ في فتح نافذة السلف: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار تناسق البيانات
        function testDataConsistency() {
            addResult('🔍 اختبار تناسق البيانات...', 'info');

            // فحص المتغيرات العامة
            const checks = [
                { name: 'employees', variable: employees },
                { name: 'advancesDeductionsData', variable: advancesDeductionsData },
                { name: 'incentivesRewardsData', variable: incentivesRewardsData }
            ];

            checks.forEach(check => {
                if (typeof check.variable !== 'undefined') {
                    addResult(`✅ متغير ${check.name}: موجود (${Array.isArray(check.variable) ? check.variable.length + ' عنصر' : 'متاح'})`, 'success');
                } else {
                    addResult(`❌ متغير ${check.name}: مفقود`, 'error');
                }
            });

            // فحص الدوال الأساسية
            const functions = [
                'viewAdvancesDeductions',
                'viewIncentivesRewards',
                'saveAdvanceDeduction',
                'saveIncentiveReward',
                'deleteAdvanceDeduction',
                'deleteIncentiveReward'
            ];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة ${funcName}: متاحة`, 'success');
                } else {
                    addResult(`❌ دالة ${funcName}: غير متاحة`, 'error');
                }
            });

            addResult('✅ انتهى فحص تناسق البيانات', 'success');
        }

        // دالة لاختبار تناسق التصميم
        function testUIConsistency() {
            addResult('🎨 اختبار تناسق التصميم...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            // فتح النوافذ للفحص البصري
            try {
                viewAdvancesDeductions(9999);
                addResult('✅ نافذة السلف: تم فتحها للفحص البصري', 'success');

                setTimeout(() => {
                    viewIncentivesRewards(9999);
                    addResult('✅ نافذة الحوافز: تم فتحها للفحص البصري', 'success');
                    addResult('👀 قارن بين النافذتين من ناحية:', 'info');
                    addResult('• تطابق الألوان والخطوط', 'info');
                    addResult('• تشابه تخطيط العناصر', 'info');
                    addResult('• تناسق أحجام الأزرار والحقول', 'info');
                    addResult('• تطابق رسائل التأكيد والأخطاء', 'info');
                }, 1000);

            } catch (error) {
                addResult(`❌ خطأ في فحص التصميم: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار الوظائف
        function testFunctionality() {
            addResult('⚙️ اختبار الوظائف...', 'info');

            // التحقق من وجود الموظف التجريبي
            const employee = employees.find(emp => emp.id === 9999);
            if (!employee) {
                addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            // اختبار إنشاء بيانات تجريبية
            try {
                // إنشاء سلفة تجريبية
                const testAdvance = {
                    id: Date.now() + 1,
                    employeeId: 9999,
                    type: 'advance',
                    month: 11,
                    year: 2024,
                    amount: 1000,
                    description: 'سلفة اختبار',
                    date: new Date().toISOString()
                };

                advancesDeductionsData.push(testAdvance);
                saveAdvancesDeductionsToLocalStorage();
                addResult('✅ تم إنشاء سلفة تجريبية', 'success');

                // إنشاء حافز تجريبي
                const testIncentive = {
                    id: Date.now() + 2,
                    employeeId: 9999,
                    type: 'incentive',
                    month: 11,
                    year: 2024,
                    amount: 1500,
                    description: 'حافز اختبار',
                    date: new Date().toISOString()
                };

                incentivesRewardsData.push(testIncentive);
                saveIncentivesRewardsToLocalStorage();
                addResult('✅ تم إنشاء حافز تجريبي', 'success');

                addResult('🎯 يمكنك الآن فتح النوافذ لرؤية البيانات التجريبية', 'info');

            } catch (error) {
                addResult(`❌ خطأ في اختبار الوظائف: ${error.message}`, 'error');
            }
        }

        // دالة لفتح التطبيق الأصلي
        function openOriginalApp() {
            addResult('🌐 فتح التطبيق الأصلي...', 'info');
            window.open('index.html', '_blank');
            addResult('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 مرحباً بك في نظام مقارنة نوافذ الحوافز والسلف', 'info');
            addResult('📋 يمكنك الآن اختبار التشابه بين النافذتين', 'info');

            // فحص سريع للنظام
            setTimeout(() => {
                addResult('🔍 فحص سريع للنظام...', 'info');

                // فحص المتغيرات العامة
                if (typeof employees !== 'undefined') {
                    addResult('✅ متغير employees: موجود', 'success');
                } else {
                    addResult('❌ متغير employees: مفقود', 'error');
                }

                if (typeof advancesDeductionsData !== 'undefined') {
                    addResult('✅ متغير advancesDeductionsData: موجود', 'success');
                } else {
                    addResult('❌ متغير advancesDeductionsData: مفقود', 'error');
                }

                if (typeof incentivesRewardsData !== 'undefined') {
                    addResult('✅ متغير incentivesRewardsData: موجود', 'success');
                } else {
                    addResult('❌ متغير incentivesRewardsData: مفقود', 'error');
                }

                // فحص الدوال الأساسية
                const criticalFunctions = [
                    'viewAdvancesDeductions',
                    'viewIncentivesRewards'
                ];

                criticalFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ دالة ${funcName}: متاحة`, 'success');
                    } else {
                        addResult(`❌ دالة ${funcName}: غير متاحة`, 'error');
                    }
                });

                addResult('✅ انتهى الفحص السريع للنظام', 'success');
                addResult('🎯 النظام جاهز للمقارنة!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
