<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص السجلات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            color: white;
        }

        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .status-success { border-left: 5px solid #28a745; }
        .status-warning { border-left: 5px solid #ffc107; }
        .status-error { border-left: 5px solid #dc3545; }
        .status-info { border-left: 5px solid #17a2b8; }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .summary-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .summary-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص سجلات أيام الحضور</h1>
            <p>التحقق من وجود البيانات في النظام</p>
        </div>

        <div style="text-align: center; margin-bottom: 20px;">
            <button class="btn" onclick="checkRecords()">🔍 فحص السجلات</button>
            <button class="btn" onclick="createSampleData()">➕ إنشاء بيانات تجريبية</button>
            <button class="btn" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
            <button class="btn" onclick="openDaysWindow()">📋 فتح نافذة الأيام</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="app.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status-card status-${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-EG')}:</strong> ${message}`;
            results.appendChild(div);
        }

        function checkRecords() {
            addResult('🔍 بدء فحص السجلات...', 'info');

            // فحص المتغيرات الأساسية
            if (typeof employees === 'undefined') {
                addResult('❌ متغير employees غير موجود', 'error');
                return;
            }

            if (typeof employeeDaysData === 'undefined') {
                addResult('❌ متغير employeeDaysData غير موجود', 'error');
                return;
            }

            // عرض إحصائيات
            const employeesCount = employees.length;
            const recordsCount = employeeDaysData.length;

            addResult(`📊 عدد الموظفين: ${employeesCount}`, employeesCount > 0 ? 'success' : 'warning');
            addResult(`📋 عدد سجلات الأيام: ${recordsCount}`, recordsCount > 0 ? 'success' : 'warning');

            // فحص localStorage
            try {
                const savedEmployees = localStorage.getItem('oscoEmployees');
                const savedDays = localStorage.getItem('oscoEmployeeDaysData');

                addResult(`💾 بيانات الموظفين في localStorage: ${savedEmployees ? 'موجودة' : 'غير موجودة'}`,
                         savedEmployees ? 'success' : 'warning');
                addResult(`💾 بيانات الأيام في localStorage: ${savedDays ? 'موجودة' : 'غير موجودة'}`,
                         savedDays ? 'success' : 'warning');

                if (savedDays) {
                    const parsedDays = JSON.parse(savedDays);
                    addResult(`📊 عدد السجلات المحفوظة: ${parsedDays.length}`, 'info');
                }
            } catch (error) {
                addResult(`❌ خطأ في فحص localStorage: ${error.message}`, 'error');
            }

            // عرض ملخص فقط
            if (recordsCount > 0) {
                showRecordsSummary();
                addResult(`📋 يوجد ${recordsCount} سجل في النظام`, 'success');
            } else {
                addResult('⚠️ لا توجد سجلات أيام حضور', 'warning');
            }
        }

        function showRecordsSummary() {
            const totalDays = employeeDaysData.reduce((sum, record) => sum + (record.actualDays || 0), 0);
            const totalHours = employeeDaysData.reduce((sum, record) => sum + (record.overtimeHours || 0), 0);
            const averageDays = employeeDaysData.length > 0 ? (totalDays / employeeDaysData.length).toFixed(1) : 0;

            const summaryHTML = `
                <div class="summary">
                    <div class="summary-item">
                        <div class="summary-number">${employeeDaysData.length}</div>
                        <div class="summary-label">إجمالي السجلات</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">${totalDays}</div>
                        <div class="summary-label">إجمالي الأيام</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">${totalHours.toFixed(1)}</div>
                        <div class="summary-label">إجمالي الساعات</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">${averageDays}</div>
                        <div class="summary-label">متوسط الأيام</div>
                    </div>
                </div>
            `;

            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.innerHTML = summaryHTML;
            results.appendChild(div);
        }

        function createSampleData() {
            addResult('🔧 إنشاء بيانات تجريبية...', 'info');

            try {
                // إنشاء موظف تجريبي إذا لم يكن موجوداً
                if (!employees.find(emp => emp.id === 1001)) {
                    employees.push({
                        id: 1001,
                        name: 'أحمد محمد علي',
                        position: 'مهندس برمجيات',
                        employeeCode: 'ENG001',
                        employmentType: 'monthly',
                        basicSalary: 6000
                    });
                    addResult('✅ تم إنشاء موظف تجريبي', 'success');
                }

                // إنشاء سجلات أيام تجريبية
                const sampleRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 1001,
                        month: 12,
                        year: 2024,
                        projectName: 'مشروع تطوير النظام',
                        actualDays: 22,
                        overtimeHours: 8,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 1001,
                        month: 11,
                        year: 2024,
                        projectName: 'مشروع الصيانة',
                        actualDays: 25,
                        overtimeHours: 6,
                        createdAt: new Date().toISOString()
                    }
                ];

                sampleRecords.forEach(record => {
                    if (!employeeDaysData.find(r => r.id === record.id)) {
                        employeeDaysData.push(record);
                    }
                });

                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveDaysDataToLocalStorage();

                addResult(`✅ تم إنشاء ${sampleRecords.length} سجل تجريبي`, 'success');

            } catch (error) {
                addResult(`❌ خطأ في إنشاء البيانات: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (!confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                return;
            }

            try {
                // مسح البيانات من الذاكرة
                employeeDaysData.length = 0;

                // مسح البيانات من localStorage
                localStorage.removeItem('oscoEmployeeDaysData');

                addResult('✅ تم مسح جميع سجلات الأيام', 'success');

                // مسح النتائج المعروضة
                document.getElementById('results').innerHTML = '';

            } catch (error) {
                addResult(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function openDaysWindow() {
            if (employees.length === 0) {
                addResult('❌ لا توجد موظفين - قم بإنشاء بيانات تجريبية أولاً', 'error');
                return;
            }

            const firstEmployee = employees[0];
            addResult(`🔧 فتح نافذة الأيام للموظف: ${firstEmployee.name}`, 'info');

            try {
                openDaysCalculator(firstEmployee.id);
                addResult('✅ تم فتح نافذة الأيام بنجاح', 'success');
            } catch (error) {
                addResult(`❌ خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        // فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 تم تحميل صفحة فحص السجلات', 'info');
                checkRecords();
            }, 1000);
        });
    </script>
</body>
</html>
