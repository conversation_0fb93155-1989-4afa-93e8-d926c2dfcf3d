<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام النافذة الواحدة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .header p {
            color: #666;
            margin: 0;
            font-size: 16px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            margin-top: 20px;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #34495e;
        }

        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #007bff;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 اختبار نظام النافذة الواحدة</h1>
            <p>التأكد من عمل النظام بنافذة واحدة فقط بعد إزالة النافذة القديمة</p>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز للاختبار
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="testsRun">0</div>
                <div class="stat-label">اختبارات منفذة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsSuccess">0</div>
                <div class="stat-label">نجحت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testsFailed">0</div>
                <div class="stat-label">فشلت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="windowsFound">0</div>
                <div class="stat-label">نوافذ موجودة</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 اختبارات النظام</h3>
            <button class="btn" onclick="checkSystemStatus()">فحص حالة النظام</button>
            <button class="btn" onclick="createTestData()">إنشاء بيانات تجريبية</button>
            <button class="btn success" onclick="testSingleWindow()">اختبار النافذة الواحدة</button>
            <button class="btn warning" onclick="testWindowConflicts()">فحص تداخل النوافذ</button>
            <button class="btn danger" onclick="clearAllData()">مسح البيانات</button>
        </div>

        <div class="test-section">
            <h3>🚀 اختبارات متقدمة</h3>
            <button class="btn" onclick="testMonthlyEmployee()">اختبار موظف شهري</button>
            <button class="btn" onclick="testDailyEmployee()">اختبار موظف يومي</button>
            <button class="btn success" onclick="testFieldsToggle()">اختبار تبديل الحقول</button>
            <button class="btn warning" onclick="runFullTest()">تشغيل جميع الاختبارات</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry info">[SINGLE_WINDOW_TEST] نظام اختبار النافذة الواحدة جاهز...</div>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>

    <script>
        let testsRun = 0;
        let testsSuccess = 0;
        let testsFailed = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('ar-EG');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStats() {
            document.getElementById('testsRun').textContent = testsRun;
            document.getElementById('testsSuccess').textContent = testsSuccess;
            document.getElementById('testsFailed').textContent = testsFailed;
        }

        function runTest(testName, testFunction) {
            testsRun++;
            addLog(`🔧 بدء اختبار: ${testName}`, 'info');

            try {
                const result = testFunction();
                if (result !== false) {
                    testsSuccess++;
                    addLog(`✅ نجح اختبار: ${testName}`, 'success');
                } else {
                    testsFailed++;
                    addLog(`❌ فشل اختبار: ${testName}`, 'error');
                }
            } catch (error) {
                testsFailed++;
                addLog(`❌ خطأ في اختبار ${testName}: ${error.message}`, 'error');
            }

            updateStats();
        }

        function checkSystemStatus() {
            addLog('🔍 فحص حالة النظام...', 'info');

            // فحص وجود النوافذ في DOM
            const windowIds = ['daysModal', 'daysCalculationModal', 'enhancedDaysModal'];
            let foundWindows = 0;

            windowIds.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    foundWindows++;
                    addLog(`🔍 تم العثور على نافذة: ${id}`, 'warning');
                } else {
                    addLog(`✅ النافذة غير موجودة: ${id}`, 'success');
                }
            });

            document.getElementById('windowsFound').textContent = foundWindows;

            // فحص الدوال المطلوبة
            const requiredFunctions = [
                'openDaysCalculator',
                'openDaysCalculationModal',
                'createDaysCalculatorModal',
                'enableFieldsBasedOnEmployeeType'
            ];

            let functionsFound = 0;
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    functionsFound++;
                    addLog(`✅ دالة موجودة: ${funcName}`, 'success');
                } else {
                    addLog(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });

            if (foundWindows === 0) {
                updateStatus('✅ النظام نظيف - لا توجد نوافذ متداخلة', 'success');
            } else {
                updateStatus(`⚠️ تم العثور على ${foundWindows} نافذة في DOM`, 'warning');
            }
        }

        function createTestData() {
            addLog('📋 إنشاء بيانات تجريبية...', 'info');

            try {
                // إنشاء موظف شهري
                const monthlyEmployee = {
                    id: 2001,
                    name: "موظف شهري - اختبار النافذة الواحدة",
                    position: "مطور برمجيات",
                    employeeCode: "MONTHLY_2001",
                    employmentType: "monthly",
                    salary: { basic: 8000, total: 8000 }
                };

                // إنشاء موظف يومي
                const dailyEmployee = {
                    id: 2002,
                    name: "موظف يومي - اختبار النافذة الواحدة",
                    position: "فني",
                    employeeCode: "DAILY_2002",
                    employmentType: "daily",
                    salary: { dailyWage: 200, total: 0 }
                };

                // إضافة الموظفين
                if (typeof employees !== 'undefined') {
                    // إزالة الموظفين السابقين إذا كانوا موجودين
                    employees = employees.filter(emp => emp.id !== 2001 && emp.id !== 2002);
                    employees.push(monthlyEmployee, dailyEmployee);

                    addLog('✅ تم إنشاء موظف شهري: ' + monthlyEmployee.name, 'success');
                    addLog('✅ تم إنشاء موظف يومي: ' + dailyEmployee.name, 'success');
                    updateStatus('تم إنشاء البيانات التجريبية بنجاح', 'success');
                } else {
                    addLog('❌ متغير employees غير موجود', 'error');
                    updateStatus('خطأ: متغير employees غير موجود', 'error');
                }

            } catch (error) {
                addLog('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
                updateStatus('خطأ في إنشاء البيانات التجريبية', 'error');
            }
        }

        function testSingleWindow() {
            runTest('اختبار النافذة الواحدة', () => {
                if (!employees.find(emp => emp.id === 2001)) {
                    addLog('⚠️ يرجى إنشاء البيانات التجريبية أولاً', 'warning');
                    return false;
                }

                // محاولة فتح النافذة
                if (typeof openDaysCalculator === 'function') {
                    openDaysCalculator(2001);

                    // التحقق من وجود النافذة الجديدة فقط
                    setTimeout(() => {
                        const newWindow = document.getElementById('daysModal');
                        const oldWindow = document.getElementById('daysCalculationModal');

                        if (newWindow && !oldWindow) {
                            addLog('✅ النافذة الجديدة موجودة والقديمة غير موجودة', 'success');
                            updateStatus('✅ نظام النافذة الواحدة يعمل بشكل صحيح', 'success');
                        } else if (newWindow && oldWindow) {
                            addLog('⚠️ كلا النافذتين موجودتان - تداخل محتمل', 'warning');
                            updateStatus('⚠️ تداخل في النوافذ', 'warning');
                        } else {
                            addLog('❌ لم يتم فتح أي نافذة', 'error');
                            updateStatus('❌ فشل في فتح النافذة', 'error');
                        }
                    }, 500);

                    return true;
                } else {
                    addLog('❌ دالة openDaysCalculator غير موجودة', 'error');
                    return false;
                }
            });
        }

        function testWindowConflicts() {
            addLog('🔍 فحص تداخل النوافذ...', 'info');

            const windowIds = ['daysModal', 'daysCalculationModal', 'enhancedDaysModal'];
            const openWindows = [];

            windowIds.forEach(id => {
                const element = document.getElementById(id);
                if (element && element.style.display !== 'none') {
                    openWindows.push(id);
                }
            });

            if (openWindows.length === 0) {
                addLog('✅ لا توجد نوافذ مفتوحة', 'success');
                updateStatus('✅ لا يوجد تداخل في النوافذ', 'success');
            } else if (openWindows.length === 1) {
                addLog(`✅ نافذة واحدة مفتوحة فقط: ${openWindows[0]}`, 'success');
                updateStatus('✅ نافذة واحدة مفتوحة - لا يوجد تداخل', 'success');
            } else {
                addLog(`⚠️ عدة نوافذ مفتوحة: ${openWindows.join(', ')}`, 'warning');
                updateStatus(`⚠️ تداخل في النوافذ: ${openWindows.length} نافذة مفتوحة`, 'warning');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات التجريبية؟')) {
                // مسح الموظفين التجريبيين
                if (typeof employees !== 'undefined') {
                    employees = employees.filter(emp => emp.id !== 2001 && emp.id !== 2002);
                }

                // إغلاق أي نوافذ مفتوحة
                const windowIds = ['daysModal', 'daysCalculationModal', 'enhancedDaysModal'];
                windowIds.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.display = 'none';
                    }
                });

                // إعادة تعيين الإحصائيات
                testsRun = 0;
                testsSuccess = 0;
                testsFailed = 0;
                updateStats();

                // مسح السجل
                document.getElementById('logContainer').innerHTML =
                    '<div class="log-entry info">[SINGLE_WINDOW_TEST] تم مسح البيانات - النظام جاهز للاختبار...</div>';

                updateStatus('تم مسح جميع البيانات التجريبية', 'success');
            }
        }
    </script>
</body>
</html>
