<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار السلفة البسيطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .test-button.primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .test-button.primary:hover {
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .test-button.danger:hover {
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .status {
            background: #343a40;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success {
            background: #28a745;
        }

        .status.error {
            background: #dc3545;
        }

        .status.warning {
            background: #ffc107;
            color: #333;
        }

        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            color: #0c5460;
            margin-bottom: 10px;
        }

        .info-box p {
            color: #0c5460;
            margin: 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-hand-holding-usd"></i> اختبار السلفة البسيطة</h1>
            <p>اختبار دالة simpleAddAdvance الجديدة</p>
        </div>

        <div class="status" id="status">
            جاهز لاختبار السلفة البسيطة...
        </div>

        <div class="content">
            <div class="info-box">
                <h4>💡 كيفية الاختبار:</h4>
                <p>
                    1. انقر على "إنشاء موظف تجريبي"<br>
                    2. انقر على "اختبار السلفة البسيطة"<br>
                    3. أدخل مبلغ السلفة عند الطلب<br>
                    4. أدخل وصف السلفة (اختياري)<br>
                    5. تحقق من النتائج
                </p>
            </div>

            <button class="test-button" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i> 1. إنشاء موظف تجريبي
            </button>

            <button class="test-button primary" onclick="testSimpleAdvance()">
                <i class="fas fa-hand-holding-usd"></i> 2. اختبار السلفة البسيطة
            </button>

            <button class="test-button" onclick="checkSavedData()">
                <i class="fas fa-database"></i> 3. فحص البيانات المحفوظة
            </button>

            <button class="test-button danger" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let color = '#333';
            if (type === 'success') color = '#28a745';
            else if (type === 'error') color = '#dc3545';
            else if (type === 'warning') color = '#ffc107';
            else if (type === 'info') color = '#17a2b8';
            
            resultItem.style.cssText = `
                color: ${color};
                margin-bottom: 8px;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            `;
            resultItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // إنشاء موظف تجريبي
        function createTestEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            addResult('👤 إنشاء موظف تجريبي للسلفة البسيطة', 'info');
            
            try {
                const testEmployee = {
                    id: 33333,
                    name: "موظف السلفة البسيطة",
                    position: "مختبر السلف",
                    employeeCode: "SIMPLE_ADV",
                    nationalId: "12345678901234",
                    phone: "01000000000",
                    photo: "https://via.placeholder.com/150/667eea/ffffff?text=SIMPLE",
                    employmentType: "monthly",
                    salary: { basic: 4000, total: 4000 },
                    dateAdded: new Date().toISOString()
                };
                
                // إزالة الموظف إذا كان موجود
                const existingIndex = employees.findIndex(emp => emp.id === 33333);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                    addResult('🗑️ تم إزالة الموظف الموجود', 'warning');
                }
                
                employees.push(testEmployee);
                
                if (typeof saveEmployeesToLocalStorage === 'function') {
                    saveEmployeesToLocalStorage();
                }
                
                addResult('✅ تم إنشاء الموظف التجريبي بنجاح', 'success');
                updateStatus('✅ الموظف التجريبي جاهز', 'success');
                
                // التأكد من إنشاء مصفوفة السلف
                if (!window.advancesDeductionsData) {
                    window.advancesDeductionsData = [];
                    addResult('📋 تم إنشاء مصفوفة السلف والخصومات', 'info');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في إنشاء الموظف: ${error.message}`, 'error');
                updateStatus('❌ فشل في إنشاء الموظف', 'error');
            }
        }

        // اختبار السلفة البسيطة
        function testSimpleAdvance() {
            updateStatus('جاري اختبار السلفة البسيطة...', 'warning');
            addResult('🚀 اختبار دالة السلفة البسيطة', 'info');
            
            try {
                // التحقق من وجود الموظف
                const employee = employees.find(emp => emp.id === 33333);
                if (!employee) {
                    addResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    updateStatus('❌ الموظف غير موجود', 'error');
                    return;
                }
                
                // التحقق من وجود الدالة
                if (typeof simpleAddAdvance !== 'function') {
                    addResult('❌ دالة simpleAddAdvance غير موجودة', 'error');
                    updateStatus('❌ الدالة مفقودة', 'error');
                    return;
                }
                
                addResult('✅ الدالة موجودة، سيتم فتح نوافذ الإدخال...', 'success');
                addResult('💡 أدخل مبلغ السلفة في النافذة التي ستظهر', 'info');
                
                // استدعاء الدالة
                const beforeCount = advancesDeductionsData ? advancesDeductionsData.length : 0;
                addResult(`📊 عدد السجلات قبل الإضافة: ${beforeCount}`, 'info');
                
                const success = simpleAddAdvance(33333);
                
                setTimeout(() => {
                    const afterCount = advancesDeductionsData ? advancesDeductionsData.length : 0;
                    addResult(`📊 عدد السجلات بعد الإضافة: ${afterCount}`, 'info');
                    
                    if (success && afterCount > beforeCount) {
                        addResult('🎉 تم اختبار السلفة البسيطة بنجاح!', 'success');
                        updateStatus('✅ السلفة البسيطة تعمل بشكل مثالي!', 'success');
                    } else if (success === false) {
                        addResult('⚠️ تم إلغاء العملية من قبل المستخدم', 'warning');
                        updateStatus('⚠️ تم إلغاء العملية', 'warning');
                    } else {
                        addResult('❌ فشل في إضافة السلفة', 'error');
                        updateStatus('❌ فشل الاختبار', 'error');
                    }
                }, 1000);
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار السلفة: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاختبار', 'error');
            }
        }

        // فحص البيانات المحفوظة
        function checkSavedData() {
            updateStatus('جاري فحص البيانات...', 'warning');
            addResult('🔍 فحص البيانات المحفوظة', 'info');
            
            try {
                // فحص localStorage
                const savedData = localStorage.getItem('oscoAdvancesDeductions');
                if (savedData) {
                    const parsed = JSON.parse(savedData);
                    addResult(`📊 إجمالي السجلات: ${parsed.length}`, 'info');
                    
                    // فحص سجلات الموظف التجريبي
                    const employeeRecords = parsed.filter(r => r.employeeId === 33333);
                    addResult(`👤 سجلات الموظف التجريبي: ${employeeRecords.length}`, 'info');
                    
                    if (employeeRecords.length > 0) {
                        addResult('📋 تفاصيل السجلات:', 'info');
                        employeeRecords.forEach((record, index) => {
                            const typeText = record.type === 'advance' ? 'سلفة' : 'خصم';
                            const date = new Date(record.date).toLocaleDateString();
                            addResult(`${index + 1}. ${typeText}: ${record.amount} ج.م - ${record.description || 'بدون وصف'} (${date})`, 'success');
                        });
                        updateStatus('✅ البيانات محفوظة بنجاح!', 'success');
                    } else {
                        addResult('⚠️ لا توجد سجلات للموظف التجريبي', 'warning');
                        updateStatus('⚠️ لا توجد سجلات', 'warning');
                    }
                } else {
                    addResult('❌ لا توجد بيانات في localStorage', 'error');
                    updateStatus('❌ لا توجد بيانات محفوظة', 'error');
                }
                
                // فحص المتغير في الذاكرة
                if (typeof advancesDeductionsData !== 'undefined') {
                    addResult(`💾 البيانات في الذاكرة: ${advancesDeductionsData.length} سجل`, 'info');
                } else {
                    addResult('❌ متغير البيانات غير موجود في الذاكرة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
                updateStatus('❌ خطأ في فحص البيانات', 'error');
            }
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // اختبار تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في اختبار السلفة البسيطة', 'info');
                addResult('💡 هذا الاختبار يستخدم دالة simpleAddAdvance الجديدة', 'info');
                
                // فحص النظام
                if (typeof employees !== 'undefined') {
                    addResult(`✅ النظام محمل - ${employees.length} موظف`, 'success');
                } else {
                    addResult('❌ النظام غير محمل', 'error');
                    updateStatus('❌ النظام غير محمل', 'error');
                    return;
                }
                
                // فحص الدالة
                if (typeof simpleAddAdvance === 'function') {
                    addResult('✅ دالة simpleAddAdvance متاحة', 'success');
                    updateStatus('✅ النظام جاهز للاختبار', 'success');
                } else {
                    addResult('❌ دالة simpleAddAdvance غير متاحة', 'error');
                    updateStatus('❌ الدالة مفقودة', 'error');
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
