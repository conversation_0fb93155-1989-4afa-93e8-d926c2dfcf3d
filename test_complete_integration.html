<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التكامل الشامل - المشروعات وحساب الأيام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .feature-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .feature-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .status {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .projects-btn {
            background-color: #FF9800;
        }
        .projects-btn:hover {
            background-color: #F57C00;
        }
        .add-btn {
            background-color: #28a745;
        }
        .add-btn:hover {
            background-color: #218838;
        }
        .test-btn {
            background-color: #6c757d;
        }
        .test-btn:hover {
            background-color: #5a6268;
        }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }
        .test-step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>اختبار التكامل الشامل - نظام المشروعات وحساب الأيام</h1>
    
    <div class="test-container">
        <h2>خطة الاختبار الشاملة</h2>
        <p>هذا الاختبار يتحقق من التكامل بين نظام المشروعات ونافذة حساب الأيام</p>
        
        <div class="feature-section">
            <h3>1. اختبار زر المشروعات</h3>
            <div class="test-step">
                <span class="step-number">1</span>
                <strong>اختبار وجود زر المشروعات في الصفحة الأصلية</strong>
                <br>
                <button onclick="testProjectsButton()">اختبار زر المشروعات</button>
                <div id="projects-button-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>2. اختبار نافذة المشروعات</h3>
            <div class="test-step">
                <span class="step-number">2</span>
                <strong>اختبار فتح نافذة إدارة المشروعات</strong>
                <br>
                <button class="projects-btn" onclick="testProjectsModal()">فتح نافذة المشروعات</button>
                <div id="projects-modal-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>3. اختبار قائمة المشروعات المنسدلة</h3>
            <div class="test-step">
                <span class="step-number">3</span>
                <strong>اختبار قائمة المشروعات في نافذة حساب الأيام</strong>
                <br>
                <button onclick="testProjectsDropdown()">اختبار القائمة المنسدلة</button>
                <div id="dropdown-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>4. اختبار التكامل الكامل</h3>
            <div class="test-step">
                <span class="step-number">4</span>
                <strong>اختبار السيناريو الكامل: إضافة مشروع → استخدامه في حساب الأيام</strong>
                <br>
                <button class="add-btn" onclick="runFullIntegrationTest()">تشغيل الاختبار الشامل</button>
                <div id="integration-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>5. اختبارات إضافية</h3>
            <div class="test-step">
                <button onclick="testAllFunctions()">اختبار جميع الدوال</button>
                <button onclick="testDataPersistence()">اختبار حفظ البيانات</button>
                <button class="test-btn" onclick="openOriginalPage()">فتح الصفحة الأصلية</button>
                <button onclick="clearAllTests()">مسح نتائج الاختبارات</button>
            </div>
        </div>
    </div>
    
    <div id="results">
        <h3>📋 سجل نتائج الاختبارات:</h3>
        <p>جاهز لبدء الاختبارات الشاملة...</p>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            const timestamp = new Date().toLocaleTimeString();
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            p.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testProjectsButton() {
            addResult('🔍 بدء اختبار زر المشروعات...');
            
            try {
                // التحقق من وجود دالة فتح المشروعات
                if (typeof openProjectsModal === 'function') {
                    addResult('✅ دالة openProjectsModal موجودة', 'success');
                    updateStatus('projects-button-status', true, 'زر المشروعات يعمل بشكل صحيح');
                } else {
                    addResult('❌ دالة openProjectsModal غير موجودة', 'error');
                    updateStatus('projects-button-status', false, 'دالة openProjectsModal غير موجودة');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار زر المشروعات: ' + error.message, 'error');
                updateStatus('projects-button-status', false, 'خطأ في الاختبار');
            }
        }

        function testProjectsModal() {
            addResult('🔍 بدء اختبار نافذة المشروعات...');
            
            try {
                if (typeof openProjectsModal === 'function') {
                    addResult('✅ محاولة فتح نافذة المشروعات...', 'info');
                    // لا نفتح النافذة فعلياً في الاختبار
                    updateStatus('projects-modal-status', true, 'نافذة المشروعات جاهزة للفتح');
                    addResult('✅ نافذة المشروعات متاحة ويمكن فتحها', 'success');
                } else {
                    updateStatus('projects-modal-status', false, 'نافذة المشروعات غير متاحة');
                    addResult('❌ نافذة المشروعات غير متاحة', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في اختبار نافذة المشروعات: ' + error.message, 'error');
                updateStatus('projects-modal-status', false, 'خطأ في الاختبار');
            }
        }

        function testProjectsDropdown() {
            addResult('🔍 بدء اختبار قائمة المشروعات المنسدلة...');
            
            try {
                // التحقق من وجود دالة ملء القائمة المنسدلة
                if (typeof populateProjectsDropdown === 'function') {
                    addResult('✅ دالة populateProjectsDropdown موجودة', 'success');
                    updateStatus('dropdown-status', true, 'قائمة المشروعات المنسدلة جاهزة');
                } else {
                    addResult('❌ دالة populateProjectsDropdown غير موجودة', 'error');
                    updateStatus('dropdown-status', false, 'دالة القائمة المنسدلة غير موجودة');
                }
                
                // التحقق من وجود متغير المشروعات
                if (typeof projects !== 'undefined') {
                    addResult('✅ متغير projects موجود', 'success');
                    addResult(`📊 عدد المشروعات المتاحة: ${projects.length}`, 'info');
                } else {
                    addResult('❌ متغير projects غير موجود', 'error');
                }
                
            } catch (error) {
                addResult('❌ خطأ في اختبار القائمة المنسدلة: ' + error.message, 'error');
                updateStatus('dropdown-status', false, 'خطأ في الاختبار');
            }
        }

        function runFullIntegrationTest() {
            addResult('🚀 بدء الاختبار الشامل للتكامل...');
            updateStatus('integration-status', true, 'جاري تشغيل الاختبار الشامل...');
            
            let testsPassed = 0;
            let totalTests = 0;
            
            // اختبار 1: وجود دوال المشروعات
            totalTests++;
            if (typeof openProjectsModal === 'function') {
                addResult('✅ اختبار 1/5: دالة فتح المشروعات موجودة', 'success');
                testsPassed++;
            } else {
                addResult('❌ اختبار 1/5: دالة فتح المشروعات مفقودة', 'error');
            }
            
            // اختبار 2: وجود دوال حساب الأيام
            totalTests++;
            if (typeof openDaysCalculator === 'function') {
                addResult('✅ اختبار 2/5: دالة حساب الأيام موجودة', 'success');
                testsPassed++;
            } else {
                addResult('❌ اختبار 2/5: دالة حساب الأيام مفقودة', 'error');
            }
            
            // اختبار 3: وجود دالة القائمة المنسدلة
            totalTests++;
            if (typeof populateProjectsDropdown === 'function') {
                addResult('✅ اختبار 3/5: دالة القائمة المنسدلة موجودة', 'success');
                testsPassed++;
            } else {
                addResult('❌ اختبار 3/5: دالة القائمة المنسدلة مفقودة', 'error');
            }
            
            // اختبار 4: وجود بيانات المشروعات
            totalTests++;
            if (typeof projects !== 'undefined' && Array.isArray(projects)) {
                addResult('✅ اختبار 4/5: بيانات المشروعات متاحة', 'success');
                testsPassed++;
            } else {
                addResult('❌ اختبار 4/5: بيانات المشروعات غير متاحة', 'error');
            }
            
            // اختبار 5: وجود دالة التحديث التلقائي
            totalTests++;
            if (typeof updateProjectsDropdownIfOpen === 'function') {
                addResult('✅ اختبار 5/5: دالة التحديث التلقائي موجودة', 'success');
                testsPassed++;
            } else {
                addResult('❌ اختبار 5/5: دالة التحديث التلقائي مفقودة', 'error');
            }
            
            // النتيجة النهائية
            const successRate = (testsPassed / totalTests) * 100;
            if (successRate === 100) {
                addResult(`🎉 نجح الاختبار الشامل! (${testsPassed}/${totalTests}) - ${successRate}%`, 'success');
                updateStatus('integration-status', true, `نجح التكامل بنسبة ${successRate}%`);
            } else if (successRate >= 80) {
                addResult(`⚠️ نجح الاختبار جزئياً (${testsPassed}/${totalTests}) - ${successRate}%`, 'warning');
                updateStatus('integration-status', false, `نجح التكامل جزئياً - ${successRate}%`);
            } else {
                addResult(`❌ فشل الاختبار الشامل (${testsPassed}/${totalTests}) - ${successRate}%`, 'error');
                updateStatus('integration-status', false, `فشل التكامل - ${successRate}%`);
            }
        }

        function testAllFunctions() {
            addResult('🔍 بدء اختبار جميع الدوال...');
            
            const requiredFunctions = [
                'openProjectsModal',
                'closeProjectsModal',
                'saveProject',
                'editProject',
                'deleteProject',
                'populateProjectsList',
                'createProjectCard',
                'getProjectStatusText',
                'saveProjectsToLocalStorage',
                'loadProjectsFromLocalStorage',
                'exportProjectsToExcel',
                'printProjects',
                'openDaysCalculator',
                'closeDaysModal',
                'populateProjectsDropdown',
                'updateProjectsDropdownIfOpen'
            ];
            
            let functionsFound = 0;
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            const percentage = (functionsFound / requiredFunctions.length) * 100;
            addResult(`📊 النتيجة: ${functionsFound}/${requiredFunctions.length} دالة موجودة (${percentage.toFixed(1)}%)`, 'info');
        }

        function testDataPersistence() {
            addResult('🔍 بدء اختبار حفظ البيانات...');
            
            try {
                // اختبار localStorage للمشروعات
                const savedProjects = localStorage.getItem('oscoProjects');
                if (savedProjects) {
                    const projectsData = JSON.parse(savedProjects);
                    addResult(`✅ بيانات المشروعات محفوظة: ${projectsData.length} مشروع`, 'success');
                } else {
                    addResult('⚠️ لا توجد بيانات مشروعات محفوظة', 'warning');
                }
                
                // اختبار localStorage للموظفين
                const savedEmployees = localStorage.getItem('employees');
                if (savedEmployees) {
                    const employeesData = JSON.parse(savedEmployees);
                    addResult(`✅ بيانات الموظفين محفوظة: ${employeesData.length} موظف`, 'success');
                } else {
                    addResult('⚠️ لا توجد بيانات موظفين محفوظة', 'warning');
                }
                
            } catch (error) {
                addResult('❌ خطأ في اختبار حفظ البيانات: ' + error.message, 'error');
            }
        }

        function clearAllTests() {
            document.getElementById('results').innerHTML = '<h3>📋 سجل نتائج الاختبارات:</h3><p>تم مسح جميع النتائج. جاهز لبدء اختبارات جديدة...</p>';
            
            // إعادة تعيين حالة جميع الاختبارات
            const statusElements = ['projects-button-status', 'projects-modal-status', 'dropdown-status', 'integration-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'في انتظار الاختبار';
                }
            });
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة الاختبار الشامل للتكامل');
            addResult('📋 جاهز لبدء اختبار التكامل بين نظام المشروعات وحساب الأيام');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testProjectsButton();
                setTimeout(() => testProjectsDropdown(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
