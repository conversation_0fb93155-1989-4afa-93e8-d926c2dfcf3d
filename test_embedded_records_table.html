<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الجدول المدمج مع التعديل والحذف</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .btn.success {
            background: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn.success:hover {
            background: #1e7e34;
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .btn.warning:hover {
            background: #e0a800;
            box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
        }

        .btn.danger {
            background: #dc3545;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .btn.danger:hover {
            background: #c82333;
            box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .instructions {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }

        .instructions h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #6c757d;
        }

        .feature-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }

        .feature-box h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .feature-box ul {
            margin-right: 20px;
        }

        .feature-box li {
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 14px;
        }

        .demo-steps {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }

        .demo-steps h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .demo-steps ol {
            margin-right: 20px;
        }

        .demo-steps li {
            margin-bottom: 8px;
            color: #856404;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 اختبار الجدول المدمج مع التعديل والحذف</h1>
            <p>اختبار شامل للجدول المدمج في نافذة حساب الأيام مع إمكانيات التعديل والحذف المباشر</p>
        </div>

        <div class="content">
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h4>📋 تعليمات الاستخدام:</h4>
                <ul>
                    <li>الجدول مدمج داخل نافذة حساب الأيام (لا حاجة لنافذة منفصلة)</li>
                    <li>يظهر الجدول تلقائياً عند فتح النافذة</li>
                    <li>يمكن التعديل المباشر بالنقر على زر التعديل (✏️)</li>
                    <li>يمكن الحذف المباشر بالنقر على زر الحذف (🗑️)</li>
                    <li>الجدول مصغر ومضغوط لتوفير المساحة</li>
                    <li>يتم تحديث الجدول تلقائياً بعد كل عملية حفظ</li>
                </ul>
            </div>

            <!-- مميزات الجدول المدمج -->
            <div class="feature-box">
                <h4>🎯 مميزات الجدول المدمج:</h4>
                <ul>
                    <li>مدمج في نفس النافذة (لا حاجة لنوافذ إضافية)</li>
                    <li>حجم مصغر ومضغوط (خط 10-11px)</li>
                    <li>تعديل مباشر للحقول القابلة للتعديل</li>
                    <li>حذف فوري مع تأكيد</li>
                    <li>تحديث تلقائي بعد العمليات</li>
                    <li>شريط تمرير للسجلات الكثيرة</li>
                    <li>ألوان مميزة للتفاعل</li>
                    <li>أزرار صغيرة وأنيقة</li>
                </ul>
            </div>

            <!-- خطوات التجربة -->
            <div class="demo-steps">
                <h4>🚀 خطوات التجربة:</h4>
                <ol>
                    <li>أنشئ موظف تجريبي ومشروعات</li>
                    <li>افتح نافذة حساب الأيام</li>
                    <li>أضف عدة سجلات بأيام مختلفة</li>
                    <li>لاحظ ظهور الجدول في أسفل النافذة</li>
                    <li>جرب تعديل الأيام مباشرة من الجدول</li>
                    <li>جرب حذف سجل من الجدول</li>
                    <li>لاحظ التحديث التلقائي</li>
                </ol>
            </div>

            <!-- قسم الإعداد -->
            <div class="test-section">
                <h3>🔧 إعداد البيانات التجريبية</h3>
                <div class="grid">
                    <button class="btn success" onclick="createTestEmployee()">
                        إنشاء موظف تجريبي
                    </button>
                    <button class="btn" onclick="createTestProjects()">
                        إنشاء مشروعات تجريبية
                    </button>
                    <button class="btn warning" onclick="checkEmbeddedTable()">
                        فحص الجدول المدمج
                    </button>
                </div>
            </div>

            <!-- قسم اختبار الجدول -->
            <div class="test-section">
                <h3>📊 اختبار الجدول المدمج</h3>
                <div class="grid">
                    <button class="btn" onclick="openWindowWithTable()">
                        فتح النافذة مع الجدول
                    </button>
                    <button class="btn" onclick="addMultipleRecords()">
                        إضافة عدة سجلات
                    </button>
                    <button class="btn warning" onclick="testInlineEdit()">
                        اختبار التعديل المباشر
                    </button>
                    <button class="btn danger" onclick="testRecordDeletion()">
                        اختبار الحذف
                    </button>
                </div>
            </div>

            <!-- قسم الاختبارات المتقدمة -->
            <div class="test-section">
                <h3>🚀 اختبارات متقدمة</h3>
                <div class="grid">
                    <button class="btn success" onclick="runFullTableTest()">
                        تشغيل اختبار شامل
                    </button>
                    <button class="btn" onclick="testTableScroll()">
                        اختبار تمرير الجدول
                    </button>
                    <button class="btn warning" onclick="testTableRefresh()">
                        اختبار تحديث الجدول
                    </button>
                    <button class="btn danger" onclick="clearAllTestData()">
                        مسح البيانات التجريبية
                    </button>
                </div>
            </div>

            <!-- حالة النظام -->
            <div id="status" class="status info">
                <strong>الحالة:</strong> جاهز لاختبار الجدول المدمج مع التعديل والحذف...
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        // دوال الاختبار
        function createTestEmployee() {
            const testEmployee = {
                id: 555,
                name: 'خالد محمد (اختبار الجدول)',
                position: 'مهندس مدني',
                employmentType: 'monthly',
                nationalId: '12345678901234',
                phone: '01234567890',
                basicSalary: 7000,
                isActive: true
            };

            if (!employees.find(emp => emp.id === 555)) {
                employees.push(testEmployee);
                updateStatus('تم إنشاء الموظف التجريبي بنجاح', 'success');
            } else {
                updateStatus('الموظف التجريبي موجود بالفعل', 'info');
            }
        }

        function createTestProjects() {
            const testProjects = [
                { id: 601, name: 'مشروع اختبار الجدول 1', status: 'active' },
                { id: 602, name: 'مشروع اختبار الجدول 2', status: 'active' },
                { id: 603, name: 'مشروع اختبار الجدول 3', status: 'active' }
            ];

            testProjects.forEach(project => {
                if (!projects.find(p => p.id === project.id)) {
                    projects.push(project);
                }
            });

            updateStatus('تم إنشاء مشروعات اختبار الجدول بنجاح', 'success');
        }

        function checkEmbeddedTable() {
            const checks = [
                { name: 'دالة loadEmbeddedDaysRecords', test: () => typeof loadEmbeddedDaysRecords === 'function' },
                { name: 'دالة refreshDaysRecordsTable', test: () => typeof refreshDaysRecordsTable === 'function' },
                { name: 'دالة editRecord', test: () => typeof editRecord === 'function' },
                { name: 'دالة saveRecord', test: () => typeof saveRecord === 'function' },
                { name: 'دالة deleteRecord', test: () => typeof deleteRecord === 'function' },
                { name: 'عنصر daysRecordsSection', test: () => document.getElementById('daysRecordsSection') !== null },
                { name: 'عنصر daysRecordsContainer', test: () => document.getElementById('daysRecordsContainer') !== null },
                { name: 'CSS الجدول المصغر', test: () => {
                    const styles = Array.from(document.styleSheets);
                    return styles.some(sheet => {
                        try {
                            const rules = Array.from(sheet.cssRules || sheet.rules || []);
                            return rules.some(rule => rule.selectorText && rule.selectorText.includes('days-records-table'));
                        } catch (e) {
                            return false;
                        }
                    });
                }}
            ];

            let passed = 0;
            let results = [];
            
            checks.forEach(check => {
                if (check.test()) {
                    results.push(`✓ ${check.name}`);
                    passed++;
                } else {
                    results.push(`✗ ${check.name}`);
                }
            });

            const status = passed === checks.length ? 'success' : 'warning';
            updateStatus(`فحص الجدول المدمج: ${passed}/${checks.length} عناصر موجودة`, status);
            
            console.log('نتائج فحص الجدول المدمج:');
            results.forEach(result => console.log(result));
        }

        function openWindowWithTable() {
            if (!employees.find(emp => emp.id === 555)) {
                updateStatus('يرجى إنشاء الموظف التجريبي أولاً', 'warning');
                return;
            }

            openDaysCalculationModal(555);
            updateStatus('تم فتح النافذة مع الجدول المدمج - لاحظ الجدول في الأسفل!', 'success');
        }

        function addMultipleRecords() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            // إضافة سجلات متعددة
            const records = [
                { month: 1, year: 2024, project: 1, calculated: 25, absence: 2, deductionType: 'days', deduction: 1 },
                { month: 2, year: 2024, project: 2, calculated: 28, absence: 1, deductionType: 'hours', deduction: 4 },
                { month: 3, year: 2024, project: 1, calculated: 26, absence: 0, deductionType: '', deduction: 0 }
            ];

            let recordIndex = 0;
            function addNextRecord() {
                if (recordIndex >= records.length) {
                    updateStatus(`تم إضافة ${records.length} سجلات - لاحظ الجدول المحدث!`, 'success');
                    return;
                }

                const record = records[recordIndex];
                
                // ملء البيانات
                document.getElementById('daysMonth').value = record.month;
                document.getElementById('daysYear').value = record.year;
                
                const projectSelect = document.getElementById('daysProject');
                if (projectSelect && projectSelect.options.length > record.project) {
                    projectSelect.selectedIndex = record.project;
                }
                
                document.getElementById('calculatedDays').value = record.calculated;
                document.getElementById('absenceDays').value = record.absence;
                document.getElementById('deductionType').value = record.deductionType;
                document.getElementById('deductionAmount').value = record.deduction;
                
                // تشغيل الحساب
                if (typeof calculateActualDays === 'function') {
                    calculateActualDays();
                }
                
                // حفظ السجل
                setTimeout(() => {
                    saveDaysCalculation(555);
                    recordIndex++;
                    setTimeout(addNextRecord, 1000);
                }, 500);
            }

            addNextRecord();
        }

        function testInlineEdit() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة وإضافة سجلات أولاً', 'warning');
                return;
            }

            const table = modal.querySelector('.days-records-table');
            if (!table) {
                updateStatus('لا توجد سجلات للتعديل - أضف سجلات أولاً', 'warning');
                return;
            }

            const editButtons = table.querySelectorAll('.edit-record-btn');
            if (editButtons.length > 0) {
                updateStatus('✓ جرب النقر على زر التعديل (✏️) في الجدول لتعديل السجل', 'success');
                
                // إضافة تأثير بصري للزر الأول
                editButtons[0].style.animation = 'pulse 2s infinite';
                setTimeout(() => {
                    editButtons[0].style.animation = '';
                }, 4000);
            } else {
                updateStatus('لا توجد أزرار تعديل - تأكد من وجود سجلات', 'warning');
            }
        }

        function testRecordDeletion() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة وإضافة سجلات أولاً', 'warning');
                return;
            }

            const table = modal.querySelector('.days-records-table');
            if (!table) {
                updateStatus('لا توجد سجلات للحذف - أضف سجلات أولاً', 'warning');
                return;
            }

            const deleteButtons = table.querySelectorAll('.delete-record-btn');
            if (deleteButtons.length > 0) {
                updateStatus('✓ جرب النقر على زر الحذف (🗑️) في الجدول لحذف السجل', 'success');
                
                // إضافة تأثير بصري للزر الأخير
                const lastButton = deleteButtons[deleteButtons.length - 1];
                lastButton.style.animation = 'pulse 2s infinite';
                setTimeout(() => {
                    lastButton.style.animation = '';
                }, 4000);
            } else {
                updateStatus('لا توجد أزرار حذف - تأكد من وجود سجلات', 'warning');
            }
        }

        function testTableScroll() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            const container = document.getElementById('daysRecordsContainer');
            if (container) {
                // فحص إذا كان التمرير متاح
                const hasScroll = container.scrollHeight > container.clientHeight;
                
                if (hasScroll) {
                    updateStatus('✓ شريط التمرير متاح في الجدول', 'success');
                    
                    // تحريك التمرير تلقائياً للتوضيح
                    container.scrollTop = container.scrollHeight / 2;
                    
                    setTimeout(() => {
                        container.scrollTop = 0;
                    }, 1000);
                } else {
                    updateStatus('⚠️ الجدول لا يحتاج للتمرير - أضف المزيد من السجلات', 'warning');
                }
            } else {
                updateStatus('خطأ: لم يتم العثور على حاوية الجدول', 'error');
            }
        }

        function testTableRefresh() {
            const modal = document.getElementById('daysCalculationModal');
            if (!modal || modal.style.display !== 'block') {
                updateStatus('يرجى فتح النافذة أولاً', 'warning');
                return;
            }

            if (typeof refreshDaysRecordsTable === 'function') {
                refreshDaysRecordsTable();
                updateStatus('✓ تم تحديث الجدول بنجاح', 'success');
            } else {
                updateStatus('خطأ: دالة تحديث الجدول غير موجودة', 'error');
            }
        }

        function runFullTableTest() {
            updateStatus('جاري تشغيل الاختبار الشامل للجدول المدمج...', 'warning');

            // تسلسل الاختبارات
            setTimeout(() => createTestEmployee(), 500);
            setTimeout(() => createTestProjects(), 1000);
            setTimeout(() => checkEmbeddedTable(), 1500);
            setTimeout(() => openWindowWithTable(), 2500);
            setTimeout(() => addMultipleRecords(), 3500);
            setTimeout(() => testTableScroll(), 8000);
            setTimeout(() => testInlineEdit(), 9000);
            setTimeout(() => testRecordDeletion(), 10000);
            setTimeout(() => testTableRefresh(), 11000);

            setTimeout(() => {
                updateStatus('انتهى الاختبار الشامل للجدول المدمج بنجاح! 🎉', 'success');
            }, 12000);
        }

        function clearAllTestData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات اختبار الجدول؟')) {
                // مسح الموظف التجريبي
                employees = employees.filter(emp => emp.id !== 555);
                
                // مسح المشروعات التجريبية
                projects = projects.filter(p => ![601, 602, 603].includes(p.id));
                
                // مسح سجلات الأيام
                if (typeof employeeDaysData !== 'undefined') {
                    employeeDaysData = employeeDaysData.filter(record => record.employeeId !== 555);
                }

                updateStatus('تم مسح جميع بيانات اختبار الجدول', 'warning');
            }
        }

        // إضافة CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
                100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
            }
        `;
        document.head.appendChild(style);

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('جاهز لاختبار الجدول المدمج - ابدأ بإنشاء البيانات التجريبية', 'info');
        });
    </script>
</body>
</html>
