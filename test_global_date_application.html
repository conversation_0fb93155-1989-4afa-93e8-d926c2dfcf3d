<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تطبيق الشهر والسنة على جميع الموظفين</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .button.primary { background: #007bff; }
        .button.primary:hover { background: #0056b3; }
        
        .button.warning { background: #ffc107; color: #212529; }
        .button.warning:hover { background: #e0a800; }
        
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        
        .button.info { background: #17a2b8; }
        .button.info:hover { background: #138496; }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .status.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .console {
            background: #212529;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .employee-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            min-width: 180px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .employee-card.active {
            border-color: #28a745;
            background: #f8fff8;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }
        
        .employee-card h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        
        .employee-card p {
            margin: 4px 0;
            color: #666;
            font-size: 13px;
        }
        
        .activate-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }
        
        .activate-btn.active {
            background: #dc3545;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
        
        .workflow-step {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        .workflow-step h5 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        
        .workflow-step p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 اختبار تطبيق الشهر والسنة على جميع الموظفين</h1>
            <p>نظام تطبيق الشهر والسنة على نوافذ إدخال أيام العمل لجميع الموظفين المنشطين</p>
            <div style="font-size: 14px; margin-top: 10px; opacity: 0.9;">
                ✨ الجديد: تطبيق الشهر والسنة تلقائياً عند فتح نافذة إدخال الأيام لأي موظف منشط
            </div>
        </div>

        <div class="test-section">
            <h4>🔧 إعداد البيانات التجريبية:</h4>
            <button class="button" onclick="createTestData()">
                <i class="fas fa-plus"></i>
                إنشاء بيانات تجريبية
            </button>
            <button class="button warning" onclick="clearTestData()">
                <i class="fas fa-trash"></i>
                مسح البيانات
            </button>
        </div>

        <div class="test-section">
            <h4>👥 الموظفين التجريبيين:</h4>
            <div id="testEmployees">
                <!-- سيتم ملؤها ديناميكياً -->
            </div>
        </div>

        <div class="test-section">
            <h4>🌐 اختبار النظام العام:</h4>
            <button class="button primary" onclick="testGlobalDateApplication()">
                <i class="fas fa-globe"></i>
                فتح نافذة التطبيق العام
            </button>
            <button class="button info" onclick="testEmployeeDaysWindow()">
                <i class="fas fa-calendar-alt"></i>
                اختبار نافذة إدخال الأيام
            </button>
            <button class="button" onclick="checkGlobalStatus()">
                <i class="fas fa-info-circle"></i>
                فحص حالة التطبيق العام
            </button>
            <button class="button danger" onclick="clearGlobalApplication()">
                <i class="fas fa-times-circle"></i>
                إلغاء التطبيق العام
            </button>
        </div>

        <div class="test-section">
            <h4>📋 سير العمل المتوقع:</h4>
            <div class="workflow-step">
                <h5>الخطوة 1: تطبيق الشهر والسنة</h5>
                <p>انقر على "فتح نافذة التطبيق العام" واختر الشهر والسنة المطلوبين، ثم انقر على "تطبيق على الكل"</p>
            </div>
            <div class="workflow-step">
                <h5>الخطوة 2: فتح نافذة إدخال الأيام</h5>
                <p>انقر على "اختبار نافذة إدخال الأيام" - يجب أن يتم تطبيق الشهر والسنة تلقائياً</p>
            </div>
            <div class="workflow-step">
                <h5>الخطوة 3: التحقق من النتيجة</h5>
                <p>تأكد من أن حقلي الشهر والسنة في نافذة إدخال الأيام يحتويان على القيم المطبقة</p>
            </div>
        </div>

        <div id="status" class="status info">
            <strong>الحالة:</strong> جاهز لبدء الاختبار...
        </div>

        <div class="info-box" id="activeInfo">
            <strong>الموظفين المنشطين:</strong> <span id="activeCount">0</span> |
            <strong>التطبيق العام:</strong> <span id="globalStatus">غير مطبق</span>
        </div>

        <div class="console" id="console">
            [GLOBAL_DATE_APPLICATION_TEST] نظام اختبار التطبيق العام جاهز...<br>
        </div>
    </div>

    <!-- تحميل ملف JavaScript الأساسي -->
    <script src="app.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>الحالة:</strong> ${message}`;
        }

        function addLog(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }

        function updateActiveCount() {
            const activeCountSpan = document.getElementById('activeCount');
            const globalStatusSpan = document.getElementById('globalStatus');
            
            if (activeCountSpan && typeof activeEmployees !== 'undefined') {
                activeCountSpan.textContent = activeEmployees.size;
            }
            
            if (globalStatusSpan && typeof window.globalAppliedDate !== 'undefined') {
                if (window.globalAppliedDate) {
                    globalStatusSpan.textContent = `${window.globalAppliedDate.monthName} ${window.globalAppliedDate.year}`;
                    globalStatusSpan.style.color = '#28a745';
                    globalStatusSpan.style.fontWeight = 'bold';
                } else {
                    globalStatusSpan.textContent = 'غير مطبق';
                    globalStatusSpan.style.color = '#6c757d';
                    globalStatusSpan.style.fontWeight = 'normal';
                }
            }
        }

        function createTestData() {
            addLog('إنشاء بيانات تجريبية...');
            
            try {
                // إنشاء موظفين تجريبيين
                const testEmployees = [
                    {
                        id: 3001,
                        name: 'علي محمد حسن',
                        position: 'مهندس برمجيات',
                        employeeCode: 'ENG001',
                        employmentType: 'monthly',
                        basicSalary: 6000
                    },
                    {
                        id: 3002,
                        name: 'فاطمة أحمد علي',
                        position: 'محاسبة مالية',
                        employeeCode: 'ACC001',
                        employmentType: 'monthly',
                        basicSalary: 5500
                    },
                    {
                        id: 3003,
                        name: 'محمد سالم الدين',
                        position: 'مدير مشاريع',
                        employeeCode: 'PM001',
                        employmentType: 'monthly',
                        basicSalary: 7000
                    }
                ];
                
                // إضافة الموظفين إلى القائمة الرئيسية
                testEmployees.forEach(emp => {
                    if (!employees.find(e => e.id === emp.id)) {
                        employees.push(emp);
                    }
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                
                // عرض الموظفين في الصفحة
                displayTestEmployees();
                
                addLog(`✓ تم إنشاء ${testEmployees.length} موظف تجريبي`);
                updateStatus('تم إنشاء البيانات التجريبية بنجاح!', 'success');
                
            } catch (error) {
                addLog('✗ خطأ في إنشاء البيانات: ' + error.message);
                updateStatus('خطأ في إنشاء البيانات التجريبية', 'error');
            }
        }

        function displayTestEmployees() {
            const container = document.getElementById('testEmployees');
            const testEmployees = employees.filter(emp => emp.id >= 3001 && emp.id <= 3003);
            
            if (testEmployees.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">لا توجد بيانات تجريبية - انقر على "إنشاء بيانات تجريبية"</p>';
                return;
            }
            
            container.innerHTML = testEmployees.map(emp => {
                const isActive = activeEmployees.has(emp.id);
                return `
                    <div class="employee-card ${isActive ? 'active' : ''}" id="emp-card-${emp.id}">
                        <h4>${emp.name}</h4>
                        <p><strong>الوظيفة:</strong> ${emp.position}</p>
                        <p><strong>الكود:</strong> ${emp.employeeCode}</p>
                        <button class="activate-btn ${isActive ? 'active' : ''}" onclick="toggleEmployeeActivation(${emp.id})">
                            ${isActive ? 'إلغاء التنشيط' : 'تنشيط'}
                        </button>
                    </div>
                `;
            }).join('');
            
            updateActiveCount();
        }

        function toggleEmployeeActivation(employeeId) {
            addLog(`تبديل حالة تنشيط الموظف ${employeeId}...`);
            
            if (activeEmployees.has(employeeId)) {
                activeEmployees.delete(employeeId);
                addLog(`✓ تم إلغاء تنشيط الموظف ${employeeId}`);
            } else {
                activeEmployees.add(employeeId);
                addLog(`✓ تم تنشيط الموظف ${employeeId}`);
            }
            
            // حفظ حالة التنشيط
            saveActiveEmployeesToLocalStorage();
            
            // تحديث العرض
            displayTestEmployees();
            updateActiveCount();
        }

        function clearTestData() {
            addLog('مسح البيانات التجريبية...');
            
            try {
                // إزالة الموظفين التجريبيين
                employees = employees.filter(emp => emp.id < 3001 || emp.id > 3003);
                
                // إزالة من قائمة المنشطين
                [3001, 3002, 3003].forEach(id => {
                    activeEmployees.delete(id);
                });
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                saveActiveEmployeesToLocalStorage();
                
                // تحديث العرض
                displayTestEmployees();
                updateActiveCount();
                
                addLog('✓ تم مسح البيانات التجريبية');
                updateStatus('تم مسح البيانات التجريبية', 'warning');
                
            } catch (error) {
                addLog('✗ خطأ في مسح البيانات: ' + error.message);
                updateStatus('خطأ في مسح البيانات', 'error');
            }
        }

        function testGlobalDateApplication() {
            addLog('اختبار فتح نافذة التطبيق العام...');
            
            try {
                if (typeof openGlobalDateApplicationModal === 'function') {
                    openGlobalDateApplicationModal();
                    addLog('✓ تم استدعاء دالة فتح النافذة العامة');
                    updateStatus('تم فتح نافذة التطبيق العام!', 'success');
                } else {
                    addLog('✗ دالة openGlobalDateApplicationModal غير موجودة');
                    updateStatus('خطأ: دالة فتح النافذة العامة غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في فتح النافذة العامة: ' + error.message);
                updateStatus('خطأ في فتح النافذة العامة: ' + error.message, 'error');
            }
        }

        function testEmployeeDaysWindow() {
            addLog('اختبار فتح نافذة إدخال أيام العمل...');
            
            const activeEmployeesList = Array.from(activeEmployees);
            if (activeEmployeesList.length === 0) {
                addLog('⚠️ لا يوجد موظفين منشطين');
                updateStatus('يرجى تنشيط موظف واحد على الأقل أولاً', 'warning');
                return;
            }
            
            try {
                const firstActiveEmployee = activeEmployeesList[0];
                addLog(`🔧 فتح نافذة إدخال الأيام للموظف ${firstActiveEmployee}...`);
                
                if (typeof createEnhancedDaysCalculator === 'function') {
                    createEnhancedDaysCalculator(firstActiveEmployee);
                    addLog('✓ تم فتح نافذة إدخال أيام العمل');
                    
                    // فحص التطبيق التلقائي بعد فترة قصيرة
                    setTimeout(() => {
                        checkAutoApplication();
                    }, 1000);
                    
                    updateStatus('تم فتح نافذة إدخال الأيام - فحص التطبيق التلقائي...', 'info');
                } else {
                    addLog('✗ دالة createEnhancedDaysCalculator غير موجودة');
                    updateStatus('خطأ: دالة فتح نافذة الأيام غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في فتح نافذة الأيام: ' + error.message);
                updateStatus('خطأ في فتح نافذة الأيام: ' + error.message, 'error');
            }
        }

        function checkAutoApplication() {
            addLog('🔍 فحص التطبيق التلقائي للشهر والسنة...');
            
            const modal = document.getElementById('enhancedDaysModal');
            if (!modal) {
                addLog('❌ نافذة إدخال الأيام غير مفتوحة');
                updateStatus('نافذة إدخال الأيام غير مفتوحة', 'error');
                return;
            }
            
            const monthSelect = modal.querySelector('#monthSelect');
            const yearSelect = modal.querySelector('#yearSelect');
            
            if (!monthSelect || !yearSelect) {
                addLog('❌ لم يتم العثور على حقول الشهر والسنة');
                updateStatus('لم يتم العثور على حقول الشهر والسنة', 'error');
                return;
            }
            
            const appliedMonth = monthSelect.value;
            const appliedYear = yearSelect.value;
            
            if (window.globalAppliedDate) {
                const expectedMonth = window.globalAppliedDate.month.toString();
                const expectedYear = window.globalAppliedDate.year.toString();
                
                if (appliedMonth === expectedMonth && appliedYear === expectedYear) {
                    addLog(`✅ تم تطبيق التاريخ تلقائياً: ${window.globalAppliedDate.monthName} ${window.globalAppliedDate.year}`);
                    updateStatus('✅ التطبيق التلقائي يعمل بشكل مثالي!', 'success');
                } else {
                    addLog(`❌ التطبيق التلقائي فشل - متوقع: ${window.globalAppliedDate.monthName} ${window.globalAppliedDate.year}, الفعلي: ${appliedMonth}/${appliedYear}`);
                    updateStatus('❌ التطبيق التلقائي لا يعمل بشكل صحيح', 'error');
                }
            } else {
                addLog('ℹ️ لا يوجد تطبيق عام للتاريخ');
                updateStatus('لا يوجد تطبيق عام للتاريخ - هذا طبيعي إذا لم تقم بتطبيق التاريخ بعد', 'info');
            }
        }

        function checkGlobalStatus() {
            addLog('🔍 فحص حالة التطبيق العام...');
            
            if (window.globalAppliedDate) {
                addLog(`✅ التطبيق العام نشط: ${window.globalAppliedDate.monthName} ${window.globalAppliedDate.year}`);
                addLog(`📅 تم التطبيق في: ${new Date(window.globalAppliedDate.appliedAt).toLocaleString()}`);
                addLog(`👥 عدد الموظفين المطبق عليهم: ${window.globalAppliedDate.appliedEmployees.length}`);
                updateStatus(`التطبيق العام نشط: ${window.globalAppliedDate.monthName} ${window.globalAppliedDate.year}`, 'success');
            } else {
                addLog('ℹ️ لا يوجد تطبيق عام للتاريخ');
                updateStatus('لا يوجد تطبيق عام للتاريخ', 'info');
            }
            
            updateActiveCount();
        }

        function clearGlobalApplication() {
            addLog('🗑️ إلغاء التطبيق العام...');
            
            try {
                if (typeof clearGlobalDateApplication === 'function') {
                    clearGlobalDateApplication();
                    addLog('✓ تم إلغاء التطبيق العام');
                    updateStatus('تم إلغاء التطبيق العام بنجاح', 'success');
                    updateActiveCount();
                } else {
                    addLog('✗ دالة clearGlobalDateApplication غير موجودة');
                    updateStatus('خطأ: دالة إلغاء التطبيق العام غير موجودة', 'error');
                }
            } catch (error) {
                addLog('✗ خطأ في إلغاء التطبيق العام: ' + error.message);
                updateStatus('خطأ في إلغاء التطبيق العام: ' + error.message, 'error');
            }
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('تم تحميل صفحة اختبار التطبيق العام');
            
            setTimeout(() => {
                if (typeof employees !== 'undefined') {
                    addLog('✅ البرنامج الأساسي محمل');
                    displayTestEmployees();
                    updateActiveCount();
                    updateStatus('جاهز لبدء الاختبار', 'success');
                } else {
                    addLog('❌ البرنامج الأساسي غير محمل');
                    updateStatus('❌ البرنامج الأساسي غير محمل', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
