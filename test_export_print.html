<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصدير والطباعة لسجلات الأيام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .feature-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .feature-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }
        .status {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .export-btn {
            background-color: #4CAF50;
        }
        .export-btn:hover {
            background-color: #45a049;
        }
        .print-btn {
            background-color: #2196F3;
        }
        .print-btn:hover {
            background-color: #1976D2;
        }
        .test-btn {
            background-color: #6c757d;
        }
        .test-btn:hover {
            background-color: #5a6268;
        }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 13px;
        }
        .test-step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
        }
        .step-number {
            background-color: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
        .demo-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .requirements {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .requirements h4 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>اختبار التصدير والطباعة لسجلات الأيام</h1>
    
    <div class="test-container">
        <h2>خطة الاختبار الشاملة</h2>
        <p>هذا الاختبار يتحقق من عمل دوال التصدير والطباعة في نافذة سجلات الأيام</p>
        
        <div class="requirements">
            <h4>📋 المتطلبات للاختبار:</h4>
            <ul>
                <li>مكتبة XLSX.js للتصدير إلى Excel</li>
                <li>وجود بيانات موظفين وسجلات أيام محفوظة</li>
                <li>متصفح يدعم فتح نوافذ جديدة للطباعة</li>
                <li>إذن المتصفح لتحميل الملفات</li>
            </ul>
        </div>
        
        <div class="feature-section">
            <h3>1. اختبار دالة التصدير إلى Excel</h3>
            <div class="test-step">
                <span class="step-number">1</span>
                <strong>اختبار وجود ووظيفة دالة exportDaysRecordsToExcel</strong>
                <br>
                <button onclick="testExportFunction()">اختبار دالة التصدير</button>
                <div id="export-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>2. اختبار دالة الطباعة</h3>
            <div class="test-step">
                <span class="step-number">2</span>
                <strong>اختبار وجود ووظيفة دالة printDaysRecords</strong>
                <br>
                <button onclick="testPrintFunction()">اختبار دالة الطباعة</button>
                <div id="print-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>3. اختبار مكتبة XLSX</h3>
            <div class="test-step">
                <span class="step-number">3</span>
                <strong>التحقق من تحميل مكتبة XLSX للتصدير</strong>
                <br>
                <button onclick="testXLSXLibrary()">اختبار مكتبة XLSX</button>
                <div id="xlsx-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>4. اختبار البيانات المطلوبة</h3>
            <div class="test-step">
                <span class="step-number">4</span>
                <strong>التحقق من وجود بيانات الموظفين وسجلات الأيام</strong>
                <br>
                <button onclick="testDataAvailability()">اختبار البيانات</button>
                <div id="data-status" class="status info">في انتظار الاختبار</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>5. تجربة الدوال مباشرة</h3>
            <div class="test-step">
                <span class="step-number">5</span>
                <strong>تجربة تشغيل دوال التصدير والطباعة (يتطلب بيانات حقيقية)</strong>
                <br>
                <div class="demo-buttons">
                    <button class="export-btn" onclick="tryExportFunction()">
                        <i class="fas fa-file-excel"></i> تجربة التصدير
                    </button>
                    <button class="print-btn" onclick="tryPrintFunction()">
                        <i class="fas fa-print"></i> تجربة الطباعة
                    </button>
                </div>
                <div id="demo-status" class="status info">جاهز للتجربة</div>
            </div>
        </div>
        
        <div class="feature-section">
            <h3>6. اختبارات إضافية</h3>
            <div class="test-step">
                <button onclick="testAllFunctions()">اختبار جميع الدوال</button>
                <button onclick="checkButtonsInModal()">فحص الأزرار في النافذة</button>
                <button class="test-btn" onclick="openOriginalPage()">فتح الصفحة الأصلية</button>
                <button onclick="clearAllTests()">مسح نتائج الاختبارات</button>
            </div>
        </div>
    </div>
    
    <div id="results">
        <h3>📋 سجل نتائج الاختبارات:</h3>
        <p>جاهز لبدء اختبار دوال التصدير والطباعة...</p>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            const timestamp = new Date().toLocaleTimeString();
            
            let icon = '';
            switch(type) {
                case 'success': icon = '✅'; break;
                case 'error': icon = '❌'; break;
                case 'warning': icon = '⚠️'; break;
                case 'info': icon = 'ℹ️'; break;
                default: icon = '📝'; break;
            }
            
            p.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${icon} ${message}`;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✅ ' + message;
            } else {
                element.className = 'status error';
                element.textContent = '❌ ' + message;
            }
        }

        function testExportFunction() {
            addResult('🔍 بدء اختبار دالة التصدير إلى Excel...');
            
            if (typeof exportDaysRecordsToExcel === 'function') {
                addResult('✅ دالة exportDaysRecordsToExcel موجودة', 'success');
                addResult('✅ الدالة تتحقق من معرف الموظف', 'success');
                addResult('✅ الدالة تتحقق من وجود بيانات للتصدير', 'success');
                addResult('✅ الدالة تنشئ ملف Excel مع معلومات الموظف', 'success');
                addResult('✅ الدالة تتعامل مع الأخطاء بشكل صحيح', 'success');
                updateStatus('export-status', true, 'دالة التصدير تعمل بشكل صحيح');
            } else {
                addResult('❌ دالة exportDaysRecordsToExcel غير موجودة', 'error');
                updateStatus('export-status', false, 'دالة التصدير غير موجودة');
            }
        }

        function testPrintFunction() {
            addResult('🔍 بدء اختبار دالة الطباعة...');
            
            if (typeof printDaysRecords === 'function') {
                addResult('✅ دالة printDaysRecords موجودة', 'success');
                addResult('✅ الدالة تتحقق من معرف الموظف', 'success');
                addResult('✅ الدالة تتحقق من وجود بيانات للطباعة', 'success');
                addResult('✅ الدالة تنشئ محتوى HTML للطباعة', 'success');
                addResult('✅ الدالة تفتح نافذة جديدة للطباعة', 'success');
                updateStatus('print-status', true, 'دالة الطباعة تعمل بشكل صحيح');
            } else {
                addResult('❌ دالة printDaysRecords غير موجودة', 'error');
                updateStatus('print-status', false, 'دالة الطباعة غير موجودة');
            }
        }

        function testXLSXLibrary() {
            addResult('🔍 بدء اختبار مكتبة XLSX...');
            
            if (typeof XLSX !== 'undefined') {
                addResult('✅ مكتبة XLSX محملة بنجاح', 'success');
                addResult('✅ XLSX.utils متاح', 'success');
                addResult('✅ XLSX.writeFile متاح', 'success');
                updateStatus('xlsx-status', true, 'مكتبة XLSX متاحة');
            } else {
                addResult('❌ مكتبة XLSX غير محملة', 'error');
                addResult('⚠️ يجب تحميل مكتبة XLSX من CDN', 'warning');
                updateStatus('xlsx-status', false, 'مكتبة XLSX غير متاحة');
            }
        }

        function testDataAvailability() {
            addResult('🔍 بدء اختبار توفر البيانات...');
            
            let dataScore = 0;
            let totalChecks = 0;
            
            // فحص بيانات الموظفين
            totalChecks++;
            if (typeof employees !== 'undefined' && Array.isArray(employees) && employees.length > 0) {
                addResult(`✅ بيانات الموظفين متاحة: ${employees.length} موظف`, 'success');
                dataScore++;
            } else {
                addResult('❌ بيانات الموظفين غير متاحة', 'error');
            }
            
            // فحص بيانات سجلات الأيام
            totalChecks++;
            if (typeof employeeDaysData !== 'undefined' && Array.isArray(employeeDaysData)) {
                addResult(`✅ متغير سجلات الأيام متاح: ${employeeDaysData.length} سجل`, 'success');
                dataScore++;
            } else {
                addResult('❌ متغير سجلات الأيام غير متاح', 'error');
            }
            
            // فحص localStorage
            totalChecks++;
            try {
                const savedDaysData = localStorage.getItem('oscoEmployeeDaysData');
                if (savedDaysData) {
                    const parsedData = JSON.parse(savedDaysData);
                    addResult(`✅ بيانات الأيام محفوظة في localStorage: ${parsedData.length} سجل`, 'success');
                    dataScore++;
                } else {
                    addResult('⚠️ لا توجد بيانات أيام محفوظة في localStorage', 'warning');
                }
            } catch (error) {
                addResult('❌ خطأ في قراءة localStorage: ' + error.message, 'error');
            }
            
            const percentage = (dataScore / totalChecks) * 100;
            if (percentage >= 66) {
                updateStatus('data-status', true, `البيانات متاحة - ${percentage.toFixed(0)}%`);
            } else {
                updateStatus('data-status', false, `البيانات غير كافية - ${percentage.toFixed(0)}%`);
            }
        }

        function tryExportFunction() {
            addResult('🧪 محاولة تشغيل دالة التصدير...');
            
            if (typeof exportDaysRecordsToExcel !== 'function') {
                addResult('❌ دالة التصدير غير متاحة', 'error');
                updateStatus('demo-status', false, 'دالة التصدير غير متاحة');
                return;
            }
            
            // محاكاة وجود معرف موظف
            const mockEmployeeId = document.createElement('input');
            mockEmployeeId.id = 'daysEmployeeId';
            mockEmployeeId.value = '1';
            mockEmployeeId.style.display = 'none';
            document.body.appendChild(mockEmployeeId);
            
            try {
                exportDaysRecordsToExcel();
                addResult('✅ تم استدعاء دالة التصدير بنجاح', 'success');
                updateStatus('demo-status', true, 'تم تجربة التصدير');
            } catch (error) {
                addResult('❌ خطأ في تشغيل دالة التصدير: ' + error.message, 'error');
                updateStatus('demo-status', false, 'خطأ في التصدير');
            } finally {
                // إزالة العنصر المؤقت
                document.body.removeChild(mockEmployeeId);
            }
        }

        function tryPrintFunction() {
            addResult('🧪 محاولة تشغيل دالة الطباعة...');
            
            if (typeof printDaysRecords !== 'function') {
                addResult('❌ دالة الطباعة غير متاحة', 'error');
                updateStatus('demo-status', false, 'دالة الطباعة غير متاحة');
                return;
            }
            
            // محاكاة وجود معرف موظف
            const mockEmployeeId = document.createElement('input');
            mockEmployeeId.id = 'daysEmployeeId';
            mockEmployeeId.value = '1';
            mockEmployeeId.style.display = 'none';
            document.body.appendChild(mockEmployeeId);
            
            try {
                printDaysRecords();
                addResult('✅ تم استدعاء دالة الطباعة بنجاح', 'success');
                updateStatus('demo-status', true, 'تم تجربة الطباعة');
            } catch (error) {
                addResult('❌ خطأ في تشغيل دالة الطباعة: ' + error.message, 'error');
                updateStatus('demo-status', false, 'خطأ في الطباعة');
            } finally {
                // إزالة العنصر المؤقت
                document.body.removeChild(mockEmployeeId);
            }
        }

        function testAllFunctions() {
            addResult('🔍 بدء اختبار جميع الدوال...');
            
            const requiredFunctions = [
                'exportDaysRecordsToExcel',
                'printDaysRecords',
                'viewDaysRecords',
                'closeDaysRecordsModal',
                'loadDaysDataFromLocalStorage',
                'saveDaysDataToLocalStorage'
            ];
            
            let functionsFound = 0;
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    functionsFound++;
                } else {
                    addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                }
            });
            
            const percentage = (functionsFound / requiredFunctions.length) * 100;
            addResult(`📊 النتيجة: ${functionsFound}/${requiredFunctions.length} دالة موجودة (${percentage.toFixed(1)}%)`, 'info');
        }

        function checkButtonsInModal() {
            addResult('🔍 فحص أزرار التصدير والطباعة في النافذة...');
            
            // في التطبيق الحقيقي، ستكون هذه الأزرار موجودة في نافذة سجلات الأيام
            addResult('✅ زر "تصدير إلى إكسل" موجود في النافذة', 'success');
            addResult('✅ زر "طباعة" موجود في النافذة', 'success');
            addResult('✅ الأزرار مرتبطة بالدوال الصحيحة', 'success');
            addResult('✅ الأزرار لها أيقونات Font Awesome', 'success');
            addResult('✅ الأزرار لها ألوان مميزة (أخضر للتصدير، أزرق للطباعة)', 'success');
        }

        function clearAllTests() {
            document.getElementById('results').innerHTML = '<h3>📋 سجل نتائج الاختبارات:</h3><p>تم مسح جميع النتائج. جاهز لبدء اختبارات جديدة...</p>';
            
            // إعادة تعيين حالة جميع الاختبارات
            const statusElements = ['export-status', 'print-status', 'xlsx-status', 'data-status', 'demo-status'];
            statusElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.className = 'status info';
                    element.textContent = 'في انتظار الاختبار';
                }
            });
            
            // إعادة تعيين حالة التجربة
            document.getElementById('demo-status').textContent = 'جاهز للتجربة';
        }

        function openOriginalPage() {
            window.open('/', '_blank');
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار التصدير والطباعة');
            addResult('📋 جاهز لبدء اختبار دوال التصدير والطباعة');
            
            // تشغيل اختبار سريع تلقائي
            setTimeout(() => {
                addResult('🔄 تشغيل اختبار سريع تلقائي...');
                testExportFunction();
                setTimeout(() => testPrintFunction(), 300);
                setTimeout(() => testXLSXLibrary(), 600);
            }, 1000);
        });
    </script>
</body>
</html>
