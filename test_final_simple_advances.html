<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 النافذة البسيطة النهائية - جاهزة للاستخدام</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        .header h1 {
            color: #4CAF50;
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .success-alert {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            text-align: center;
            font-weight: bold;
            font-size: 1.3em;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        .specs-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .spec-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px solid #4CAF50;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .spec-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4CAF50, #45a049);
        }
        .spec-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .spec-icon {
            font-size: 2.5em;
            color: #4CAF50;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .spec-title {
            color: #2E7D32;
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .spec-value {
            color: #1B5E20;
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 8px;
        }
        .spec-desc {
            color: #4E342E;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .action-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
        }
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }
        .action-btn.primary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
        }
        .action-btn.primary:hover {
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
        }
        .features-list {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            border-left: 5px solid #4CAF50;
        }
        .features-list h3 {
            color: #2E7D32;
            margin: 0 0 20px 0;
            font-size: 1.4em;
        }
        .features-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .features-list li {
            padding: 8px 0;
            color: #424242;
            font-size: 1.1em;
            position: relative;
            padding-right: 25px;
        }
        .features-list li::before {
            content: '✅';
            position: absolute;
            right: 0;
            top: 8px;
        }
        .result {
            background: #d4edda;
            border: 2px solid #4CAF50;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
            display: none;
            font-size: 1.1em;
        }
        .result.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .final-note {
            background: linear-gradient(45deg, #E8F5E8, #C8E6C9);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .final-note h3 {
            color: #2E7D32;
            margin: 0 0 15px 0;
            font-size: 1.5em;
        }
        .final-note p {
            color: #1B5E20;
            margin: 0;
            font-size: 1.2em;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> النافذة البسيطة جاهزة!</h1>
            <p style="font-size: 1.3em; color: #666;">تم إزالة النافذة القديمة وتفعيل النافذة الجديدة البسيطة</p>
        </div>

        <div class="success-alert">
            <i class="fas fa-check-circle"></i>
            تم بنجاح! النافذة البسيطة 450px جاهزة للاستخدام في النظام
        </div>

        <div class="specs-container">
            <div class="spec-card">
                <div class="spec-icon">📏</div>
                <div class="spec-title">الحجم</div>
                <div class="spec-value">450px عرض</div>
                <div class="spec-desc">حجم صغير ومناسب للاستخدام اليومي</div>
            </div>
            
            <div class="spec-card">
                <div class="spec-icon">🎨</div>
                <div class="spec-title">التصميم</div>
                <div class="spec-value">بسيط ونظيف</div>
                <div class="spec-desc">مثل نافذة أيام الحضور تماماً</div>
            </div>
            
            <div class="spec-card">
                <div class="spec-icon">⚡</div>
                <div class="spec-title">الأداء</div>
                <div class="spec-value">سريع وخفيف</div>
                <div class="spec-desc">تحميل فوري وتفاعل سلس</div>
            </div>
            
            <div class="spec-card">
                <div class="spec-icon">📱</div>
                <div class="spec-title">الاستجابة</div>
                <div class="spec-value">متجاوب</div>
                <div class="spec-desc">يعمل على جميع أحجام الشاشات</div>
            </div>
        </div>

        <div class="features-list">
            <h3><i class="fas fa-star"></i> الميزات المطبقة:</h3>
            <ul>
                <li>حجم صغير 450px بدلاً من 800px</li>
                <li>تصميم مدمج مع حقول متجاورة</li>
                <li>جدول مختصر بخط صغير</li>
                <li>أزرار بأيقونات لتوفير المساحة</li>
                <li>معلومات موظف مختصرة في سطر واحد</li>
                <li>إغلاق بالنقر خارج النافذة</li>
                <li>تأثيرات بصرية سلسة</li>
                <li>ألوان متناسقة مع النظام</li>
            </ul>
        </div>

        <div class="action-buttons">
            <button class="action-btn" onclick="createTestEmployee()">
                <i class="fas fa-user-plus"></i>
                إنشاء موظف تجريبي
            </button>
            
            <button class="action-btn primary" onclick="testNewWindow()">
                <i class="fas fa-play"></i>
                تشغيل النافذة الجديدة
            </button>
        </div>

        <div id="result" class="result"></div>

        <div class="final-note">
            <h3><i class="fas fa-trophy"></i> مبروك!</h3>
            <p>
                تم إنشاء نافذة السلف البسيطة بنجاح وهي الآن جاهزة للاستخدام في النظام الأساسي.
                <br><br>
                يمكنك الآن استخدام النافذة الجديدة من خلال النقر على زر "السلف والخصومات" في كروت الموظفين.
            </p>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>
    
    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'exclamation-triangle'}"></i> ${message}`;
        }

        function createTestEmployee() {
            try {
                // إنشاء موظف تجريبي
                const testEmployee = {
                    id: 77777,
                    name: 'فاطمة محمد - الاختبار النهائي',
                    position: 'مهندسة',
                    employeeCode: 'FINAL77777',
                    employmentType: 'monthly',
                    basicSalary: 7000
                };
                
                // إزالة الموظف إذا كان موجوداً
                const existingIndex = employees.findIndex(emp => emp.id === 77777);
                if (existingIndex !== -1) {
                    employees.splice(existingIndex, 1);
                }
                
                employees.push(testEmployee);
                
                // إنشاء بعض سجلات السلف التجريبية
                const testAdvances = [
                    {
                        id: Date.now() + 1,
                        employeeId: 77777,
                        type: 'advance',
                        month: 12,
                        year: 2024,
                        amount: 1200,
                        description: 'سلفة نهاية السنة',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 77777,
                        type: 'deduction',
                        month: 11,
                        year: 2024,
                        amount: 200,
                        description: 'خصم تأمينات',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 77777,
                        type: 'advance',
                        month: 10,
                        year: 2024,
                        amount: 800,
                        description: 'سلفة أكتوبر',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // إزالة السجلات السابقة
                if (typeof advancesDeductionsData !== 'undefined') {
                    advancesDeductionsData = advancesDeductionsData.filter(record => record.employeeId !== 77777);
                    testAdvances.forEach(record => {
                        advancesDeductionsData.push(record);
                    });
                } else {
                    window.advancesDeductionsData = testAdvances;
                }
                
                // حفظ البيانات
                saveEmployeesToLocalStorage();
                if (typeof saveAdvancesDeductionsToLocalStorage === 'function') {
                    saveAdvancesDeductionsToLocalStorage();
                }
                
                showResult(`✅ تم إنشاء الموظفة "${testEmployee.name}" مع ${testAdvances.length} سجلات سلف/خصم`, 'success');
                
            } catch (error) {
                showResult('❌ خطأ في إنشاء البيانات: ' + error.message, 'error');
            }
        }

        function testNewWindow() {
            try {
                if (!employees.find(emp => emp.id === 77777)) {
                    showResult('❌ يرجى إنشاء الموظف التجريبي أولاً', 'error');
                    return;
                }
                
                // فتح النافذة الجديدة البسيطة
                viewAdvancesDeductions(77777);
                
                showResult('🎉 تم تشغيل النافذة البسيطة الجديدة! لاحظ الحجم الصغير والتصميم المدمج', 'success');
                
                // فحص النافذة بعد فترة قصيرة
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        const modalContent = modal.querySelector('.modal-content');
                        if (modalContent) {
                            showResult(`✅ النافذة البسيطة تعمل بنجاح! العرض: 450px - التصميم: مدمج وبسيط`, 'success');
                        }
                    }
                }, 1500);
                
            } catch (error) {
                showResult('❌ خطأ في تشغيل النافذة: ' + error.message, 'error');
            }
        }

        // فحص النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof employees !== 'undefined' && typeof viewAdvancesDeductions === 'function') {
                    showResult('✅ النظام جاهز! النافذة البسيطة مفعلة ومتاحة للاستخدام', 'success');
                } else {
                    showResult('❌ خطأ: النظام غير جاهز. تأكد من تحميل ملف app.js', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
