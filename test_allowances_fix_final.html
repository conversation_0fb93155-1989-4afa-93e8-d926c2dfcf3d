<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح مشكلة البدلات النهائي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #fd7e14, #e55a00);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #fd7e14;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }

        .test-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .test-button.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .result-item {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-item.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-item.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result-item.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .result-item.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
            font-size: 16px;
        }

        .instructions {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
        }

        .instructions ol {
            margin: 10px 0;
            padding-right: 20px;
        }

        .instructions li {
            margin: 8px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> اختبار إصلاح مشكلة البدلات</h1>
            <p>حل نهائي لمشكلة عدم ظهور البدلات في نافذة حساب المرتبات</p>
        </div>

        <div class="content">
            <div class="instructions">
                <h4><i class="fas fa-info-circle"></i> تعليمات الاستخدام:</h4>
                <ol>
                    <li>انقر على "إصلاح البدلات لجميع الموظفين" لإضافة البدلات اليومية</li>
                    <li>انقر على "اختبار نافذة حساب الراتب" لفتح نافذة حساب المرتبات</li>
                    <li>تحقق من ظهور البدلات اليومية في النافذة</li>
                    <li>إذا لم تظهر البدلات، انقر على "إصلاح فوري" لحل المشكلة</li>
                </ol>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-wrench"></i> إصلاح البدلات</h3>
                <button class="test-button" onclick="fixAllowancesForAllEmployees()">
                    <i class="fas fa-magic"></i> إصلاح البدلات لجميع الموظفين
                </button>
                <button class="test-button secondary" onclick="addDailyAllowancesToAllEmployees()">
                    <i class="fas fa-plus-circle"></i> إضافة البدلات اليومية
                </button>
                <button class="test-button warning" onclick="quickFixAllowances()">
                    <i class="fas fa-bolt"></i> إصلاح فوري
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-calculator"></i> اختبار نافذة حساب الراتب</h3>
                <button class="test-button" onclick="testSalaryCalculatorWithAllowances()">
                    <i class="fas fa-test-tube"></i> اختبار نافذة حساب الراتب
                </button>
                <button class="test-button secondary" onclick="testAndFixAllowances()">
                    <i class="fas fa-cogs"></i> اختبار وإصلاح شامل
                </button>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-info"></i> معلومات النظام</h3>
                <button class="test-button secondary" onclick="checkSystemStatus()">
                    <i class="fas fa-heartbeat"></i> فحص حالة النظام
                </button>
                <button class="test-button" onclick="showEmployeesAllowances()">
                    <i class="fas fa-users"></i> عرض بدلات الموظفين
                </button>
                <button class="test-button danger" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
            </div>

            <div id="status" class="status" style="background: #f8f9fa; color: #6c757d;">
                جاهز للاختبار
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        // دوال الاختبار المحلية
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${type}`;
            
            let icon = '';
            switch(type) {
                case 'success': icon = '<i class="fas fa-check-circle"></i>'; break;
                case 'error': icon = '<i class="fas fa-times-circle"></i>'; break;
                case 'warning': icon = '<i class="fas fa-exclamation-triangle"></i>'; break;
                default: icon = '<i class="fas fa-info-circle"></i>';
            }
            
            resultItem.innerHTML = `${icon} ${message}`;
            results.appendChild(resultItem);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            
            switch(type) {
                case 'success':
                    status.style.background = '#d4edda';
                    status.style.color = '#155724';
                    break;
                case 'error':
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                    break;
                case 'warning':
                    status.style.background = '#fff3cd';
                    status.style.color = '#856404';
                    break;
                default:
                    status.style.background = '#d1ecf1';
                    status.style.color = '#0c5460';
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        function testSalaryCalculatorWithAllowances() {
            addResult('🧪 بدء اختبار نافذة حساب الراتب...', 'info');
            updateStatus('جاري اختبار نافذة حساب الراتب...', 'warning');
            
            try {
                if (!employees || employees.length === 0) {
                    addResult('❌ لا يوجد موظفين في النظام', 'error');
                    updateStatus('❌ لا يوجد موظفين', 'error');
                    return;
                }
                
                const firstEmployee = employees[0];
                addResult(`🎯 اختبار مع الموظف: ${firstEmployee.name}`, 'info');
                
                // فتح نافذة حساب الراتب
                openSalaryCalculator(firstEmployee.id);
                
                addResult('✅ تم فتح نافذة حساب الراتب بنجاح', 'success');
                addResult('🔍 تحقق من ظهور البدلات اليومية في النافذة', 'info');
                updateStatus('✅ تم فتح النافذة - تحقق من البدلات', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار النافذة: ${error.message}`, 'error');
                updateStatus('❌ فشل الاختبار', 'error');
            }
        }

        function checkSystemStatus() {
            addResult('🔍 فحص حالة النظام...', 'info');
            updateStatus('جاري فحص النظام...', 'warning');
            
            try {
                // فحص الموظفين
                if (typeof employees !== 'undefined' && employees.length > 0) {
                    addResult(`✅ عدد الموظفين: ${employees.length}`, 'success');
                } else {
                    addResult('❌ لا يوجد موظفين في النظام', 'error');
                }
                
                // فحص الدوال المطلوبة
                const requiredFunctions = [
                    'openSalaryCalculator',
                    'ensureEmployeeHasDailyAllowances',
                    'addDailyAllowancesToAllEmployees',
                    'fixAllowancesForAllEmployees'
                ];
                
                requiredFunctions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ دالة موجودة: ${funcName}`, 'success');
                    } else {
                        addResult(`❌ دالة مفقودة: ${funcName}`, 'error');
                    }
                });
                
                updateStatus('✅ تم فحص النظام', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في فحص النظام: ${error.message}`, 'error');
                updateStatus('❌ فشل فحص النظام', 'error');
            }
        }

        function showEmployeesAllowances() {
            addResult('📋 عرض بدلات الموظفين...', 'info');
            updateStatus('جاري عرض البدلات...', 'warning');
            
            try {
                if (!employees || employees.length === 0) {
                    addResult('❌ لا يوجد موظفين في النظام', 'error');
                    return;
                }
                
                employees.forEach((employee, index) => {
                    if (index < 5) { // عرض أول 5 موظفين فقط
                        const hasAllowances = employee.salary && employee.salary.dailyAllowances;
                        const expatDaily = hasAllowances ? employee.salary.dailyAllowances.expatDaily : 0;
                        const transportDaily = hasAllowances ? employee.salary.dailyAllowances.transportDaily : 0;
                        
                        if (hasAllowances) {
                            addResult(`✅ ${employee.name}: بدل اغتراب ${expatDaily} ج.م، بدل انتقالات ${transportDaily} ج.م`, 'success');
                        } else {
                            addResult(`❌ ${employee.name}: لا يوجد بدلات يومية`, 'error');
                        }
                    }
                });
                
                if (employees.length > 5) {
                    addResult(`ℹ️ تم عرض أول 5 موظفين من أصل ${employees.length}`, 'info');
                }
                
                updateStatus('✅ تم عرض البدلات', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في عرض البدلات: ${error.message}`, 'error');
                updateStatus('❌ فشل عرض البدلات', 'error');
            }
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 تم تحميل صفحة الاختبار', 'success');
                addResult('💡 يمكنك الآن بدء الاختبارات', 'info');
                checkSystemStatus();
            }, 1000);
        });
    </script>
</body>
</html>
