<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الزر العائم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* أنماط الزر الدائري العائم */
        .floating-date-btn {
            position: fixed !important;
            bottom: 30px !important;
            right: 30px !important;
            width: 60px !important;
            height: 60px !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            user-select: none !important;
            z-index: 9999 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3) !important;
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            border: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            overflow: visible !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .floating-date-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
            background: linear-gradient(135deg, #0056b3, #004085);
        }

        .floating-date-btn:active {
            transform: scale(0.95);
        }

        /* حالة الإلغاء (أحمر) */
        .floating-date-btn.cancel-mode {
            background: linear-gradient(135deg, #dc3545, #c82333);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
        }

        .floating-date-btn.cancel-mode:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        }

        /* أيقونة الزر */
        .fab-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .fab-icon i {
            font-size: 24px;
            color: white;
            transition: all 0.3s ease;
        }

        .floating-date-btn:hover .fab-icon {
            transform: rotate(10deg);
        }

        .floating-date-btn.transitioning .fab-icon {
            transform: rotate(180deg) scale(0.8);
        }

        /* Tooltip */
        .fab-tooltip {
            position: absolute;
            right: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
            backdrop-filter: blur(10px);
        }

        .fab-tooltip::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 6px solid transparent;
            border-left-color: rgba(0, 0, 0, 0.8);
        }

        .floating-date-btn:hover .fab-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(-5px);
        }

        /* تأثيرات الحركة */
        @keyframes pulse {
            0% { box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3); }
            50% { box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6); }
            100% { box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3); }
        }

        .floating-date-btn.pulse {
            animation: pulse 2s infinite;
        }

        /* تجاوب للشاشات الصغيرة */
        @media (max-width: 768px) {
            .floating-date-btn {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
            }
            
            .fab-icon i {
                font-size: 20px;
            }
            
            .fab-tooltip {
                right: 60px;
                font-size: 11px;
                padding: 6px 10px;
            }
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار الزر العائم</h1>
        
        <div id="status" class="status info">جاري التحميل...</div>
        
        <div style="margin-top: 30px;">
            <h3>اختبارات:</h3>
            <button onclick="testButtonExists()" style="margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                فحص وجود الزر
            </button>
            <button onclick="testButtonFunction()" style="margin: 5px; padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                اختبار الدالة
            </button>
            <button onclick="toggleMode()" style="margin: 5px; padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 5px; cursor: pointer;">
                تبديل الوضع
            </button>
            <button onclick="createButton()" style="margin: 5px; padding: 10px 15px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">
                إنشاء الزر
            </button>
        </div>
        
        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <!-- زر دائري عائم لإدارة التواريخ -->
    <div class="floating-date-btn" id="floatingDateBtn" onclick="testClick()">
        <div class="fab-icon">
            <i class="fas fa-calendar-check" id="fabIcon"></i>
        </div>
        <div class="fab-tooltip" id="fabTooltip">تطبيق التاريخ على الكل</div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        function testClick() {
            console.log('تم النقر على الزر العائم!');
            addResult('✅ تم النقر على الزر العائم!', 'success');
        }

        function testButtonExists() {
            const button = document.getElementById('floatingDateBtn');
            if (button) {
                addResult('✅ الزر العائم موجود ويعمل', 'success');
                console.log('Button element:', button);
                console.log('Button styles:', window.getComputedStyle(button));
            } else {
                addResult('❌ الزر العائم غير موجود', 'error');
            }
        }

        function testButtonFunction() {
            const button = document.getElementById('floatingDateBtn');
            if (button) {
                button.click();
                addResult('✅ تم تشغيل الدالة برمجياً', 'success');
            } else {
                addResult('❌ لا يمكن اختبار الدالة - الزر غير موجود', 'error');
            }
        }

        function toggleMode() {
            const button = document.getElementById('floatingDateBtn');
            const icon = document.getElementById('fabIcon');
            const tooltip = document.getElementById('fabTooltip');
            
            if (button.classList.contains('cancel-mode')) {
                button.className = 'floating-date-btn';
                icon.className = 'fas fa-calendar-check';
                tooltip.textContent = 'تطبيق التاريخ على الكل';
                addResult('✅ تم التبديل إلى وضع التطبيق', 'success');
            } else {
                button.className = 'floating-date-btn cancel-mode pulse';
                icon.className = 'fas fa-calendar-times';
                tooltip.textContent = 'إلغاء التطبيق';
                addResult('✅ تم التبديل إلى وضع الإلغاء', 'success');
            }
        }

        function createButton() {
            // إزالة الزر الموجود
            const existingBtn = document.getElementById('floatingDateBtn');
            if (existingBtn) {
                existingBtn.remove();
            }

            // إنشاء زر جديد
            const button = document.createElement('div');
            button.id = 'floatingDateBtn';
            button.className = 'floating-date-btn';
            button.onclick = testClick;
            
            button.innerHTML = `
                <div class="fab-icon">
                    <i class="fas fa-calendar-check" id="fabIcon"></i>
                </div>
                <div class="fab-tooltip" id="fabTooltip">تطبيق التاريخ على الكل</div>
            `;
            
            document.body.appendChild(button);
            addResult('✅ تم إنشاء الزر العائم برمجياً', 'success');
        }

        // تشغيل اختبار أولي
        window.addEventListener('load', function() {
            showStatus('✅ تم تحميل الصفحة بنجاح', 'success');
            testButtonExists();
        });
    </script>
</body>
</html>
