<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة الحوافز والمكافآت المحسنة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 700px;
            margin: 0 auto;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            box-shadow: 0 4px 20px rgba(102,126,234,0.3);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header-icon {
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            backdrop-filter: blur(10px);
        }

        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .feature-section {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .feature-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }

        .feature-section h2 {
            margin: 0 0 20px 0;
            color: #495057;
            font-size: 22px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.15);
        }

        .feature-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #6c757d;
        }

        .feature-list .icon {
            color: #28a745;
            font-size: 14px;
            width: 16px;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 28px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 8px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
            position: relative;
            overflow: hidden;
        }

        .test-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .test-button:hover::before {
            left: 100%;
        }

        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102,126,234,0.4);
        }

        .test-button.primary {
            width: 100%;
            justify-content: center;
            padding: 20px;
            font-size: 18px;
        }

        .test-button.success {
            background: linear-gradient(135deg, #28a745, #20c997);
            box-shadow: 0 8px 25px rgba(40,167,69,0.3);
        }

        .test-button.success:hover {
            box-shadow: 0 12px 35px rgba(40,167,69,0.4);
        }

        .demo-employee {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 20px;
            border: 2px solid #ffc107;
            box-shadow: 0 8px 25px rgba(255,193,7,0.2);
        }

        .demo-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .demo-info {
            flex: 1;
        }

        .demo-info h4 {
            margin: 0 0 8px 0;
            color: #856404;
            font-size: 18px;
        }

        .demo-info p {
            margin: 0;
            color: #856404;
            font-size: 14px;
        }

        .results-container {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 25px;
            margin-top: 25px;
            border: 2px solid #e9ecef;
            max-height: 350px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .test-result {
            padding: 12px 18px;
            margin: 8px 0;
            border-radius: 12px;
            font-size: 14px;
            border-right: 4px solid;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .test-result:hover {
            transform: translateX(-3px);
        }

        .test-result.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-color: #28a745;
        }

        .test-result.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-color: #dc3545;
        }

        .test-result.info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
            border-color: #17a2b8;
        }

        .test-result.warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border-color: #ffc107;
        }

        .highlight-box {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 2px solid #28a745;
            border-radius: 20px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 8px 25px rgba(40,167,69,0.2);
        }

        .highlight-box h3 {
            margin: 0 0 15px 0;
            color: #155724;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .highlight-box p {
            margin: 0;
            color: #155724;
            font-size: 16px;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>
                    <div class="header-icon">🎁</div>
                    نافذة الحوافز المحسنة
                </h1>
                <p>تصميم جديد بالكامل مع تحسينات شاملة وتجربة مستخدم متقدمة</p>
            </div>
        </div>

        <div class="content">
            <!-- التحسينات الجديدة -->
            <div class="feature-section">
                <h2>
                    <div class="feature-icon">✨</div>
                    التحسينات الجديدة
                </h2>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3><i class="fas fa-palette"></i> تصميم محسن</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> خلفيات متدرجة جميلة</li>
                            <li><i class="fas fa-check icon"></i> تأثيرات بصرية متقدمة</li>
                            <li><i class="fas fa-check icon"></i> أيقونات ملونة ومميزة</li>
                            <li><i class="fas fa-check icon"></i> انيميشن سلس</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3><i class="fas fa-chart-line"></i> إحصائيات ذكية</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> إجمالي الحوافز</li>
                            <li><i class="fas fa-check icon"></i> إجمالي المكافآت</li>
                            <li><i class="fas fa-check icon"></i> المجموع الكلي</li>
                            <li><i class="fas fa-check icon"></i> عداد السجلات</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3><i class="fas fa-magic"></i> تجربة محسنة</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> رسائل تفاعلية</li>
                            <li><i class="fas fa-check icon"></i> تحقق ذكي من البيانات</li>
                            <li><i class="fas fa-check icon"></i> تركيز تلقائي على الحقول</li>
                            <li><i class="fas fa-check icon"></i> تأكيد للمبالغ الكبيرة</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3><i class="fas fa-layer-group"></i> سجلات متطورة</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check icon"></i> بطاقات ملونة حسب النوع</li>
                            <li><i class="fas fa-check icon"></i> انيميشن عند الظهور</li>
                            <li><i class="fas fa-check icon"></i> تأثيرات عند التمرير</li>
                            <li><i class="fas fa-check icon"></i> أزرار حذف محسنة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="highlight-box">
                <h3><i class="fas fa-star"></i> الهدف من التحسينات</h3>
                <p>تقديم تجربة مستخدم استثنائية مع تصميم عصري وأداء محسن لإدارة الحوافز والمكافآت بكفاءة عالية</p>
            </div>

            <!-- موظف تجريبي -->
            <div class="feature-section">
                <h2>
                    <div class="feature-icon">👤</div>
                    موظف تجريبي
                </h2>

                <div class="demo-employee">
                    <div class="demo-photo">فا</div>
                    <div class="demo-info">
                        <h4>فاطمة أحمد محمد</h4>
                        <p><strong>الكود:</strong> EMP004 | <strong>المنصب:</strong> مديرة مشاريع</p>
                        <p><strong>الراتب:</strong> 12,000 ج.م | <strong>النوع:</strong> شهري</p>
                    </div>
                </div>

                <button class="test-button primary success" onclick="createTestEmployee()">
                    <i class="fas fa-user-plus"></i> إنشاء موظف تجريبي
                </button>
            </div>

            <!-- اختبارات متقدمة -->
            <div class="feature-section">
                <h2>
                    <div class="feature-icon">🚀</div>
                    اختبارات متقدمة
                </h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalTests">0</div>
                        <div class="stat-label">اختبارات مكتملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successTests">0</div>
                        <div class="stat-label">اختبارات ناجحة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="errorTests">0</div>
                        <div class="stat-label">أخطاء</div>
                    </div>
                </div>

                <div class="feature-grid">
                    <button class="test-button" onclick="testEnhancedWindow()">
                        <i class="fas fa-window-maximize"></i> فتح النافذة المحسنة
                    </button>
                    <button class="test-button" onclick="addEnhancedTestData()">
                        <i class="fas fa-database"></i> بيانات تجريبية متقدمة
                    </button>
                    <button class="test-button" onclick="testAnimations()">
                        <i class="fas fa-magic"></i> اختبار الانيميشن
                    </button>
                    <button class="test-button" onclick="testValidation()">
                        <i class="fas fa-shield-alt"></i> اختبار التحقق
                    </button>
                </div>

                <button class="test-button primary" onclick="runEnhancedTest()">
                    <i class="fas fa-rocket"></i> تشغيل اختبار شامل محسن
                </button>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبارات المحسنة</h3>
                <div id="test-results">
                    <div class="test-result info">🚀 جاهز لاختبار النافذة المحسنة مع جميع التحسينات الجديدة...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        let testStats = { total: 0, success: 0, error: 0 };

        // دالة لإضافة نتيجة اختبار محسنة
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;

            const time = new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', second: '2-digit'});
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            resultDiv.innerHTML = `${icons[type] || 'ℹ️'} ${time} - ${message}`;
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;

            // تحديث الإحصائيات
            testStats.total++;
            if (type === 'success') testStats.success++;
            if (type === 'error') testStats.error++;
            updateStats();
        }

        // دالة لتحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('successTests').textContent = testStats.success;
            document.getElementById('errorTests').textContent = testStats.error;
        }

        // دالة لإنشاء موظف تجريبي محسن
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي محسن...', 'info');

            const testEmployee = {
                id: 6666,
                name: 'فاطمة أحمد محمد',
                position: 'مديرة مشاريع',
                employeeCode: 'EMP004',
                nationalId: '29012345678901',
                phone: '01012345678',
                employmentType: 'monthly',
                basicSalary: 12000,
                photo: 'https://randomuser.me/api/portraits/women/45.jpg'
            };

            try {
                // إضافة الموظف إلى القائمة
                const existingIndex = employees.findIndex(emp => emp.id === 6666);
                if (existingIndex !== -1) {
                    employees[existingIndex] = testEmployee;
                    addResult('تم تحديث الموظف التجريبي الموجود', 'warning');
                } else {
                    employees.push(testEmployee);
                    addResult('تم إنشاء موظف تجريبي جديد بنجاح', 'success');
                }

                // حفظ البيانات
                saveEmployeesToLocalStorage();
                addResult(`✨ تم حفظ بيانات: ${testEmployee.name}`, 'success');

            } catch (error) {
                addResult(`خطأ في إنشاء الموظف: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار النافذة المحسنة
        function testEnhancedWindow() {
            addResult('🔍 اختبار فتح النافذة المحسنة...', 'info');

            const employee = employees.find(emp => emp.id === 6666);
            if (!employee) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(6666);
                addResult('تم فتح النافذة المحسنة بنجاح', 'success');
                addResult('👀 لاحظ التحسينات: الخلفيات المتدرجة، الإحصائيات، الانيميشن', 'info');
            } catch (error) {
                addResult(`خطأ في فتح النافذة: ${error.message}`, 'error');
            }
        }

        // دالة لإضافة بيانات تجريبية متقدمة
        function addEnhancedTestData() {
            addResult('📊 إضافة بيانات تجريبية متقدمة...', 'info');

            const employee = employees.find(emp => emp.id === 6666);
            if (!employee) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                const currentMonth = new Date().getMonth() + 1;
                const currentYear = new Date().getFullYear();

                const testRecords = [
                    {
                        id: Date.now() + 1,
                        employeeId: 6666,
                        type: 'incentive',
                        month: currentMonth,
                        year: currentYear,
                        amount: 2000,
                        description: 'حافز الأداء المتميز للربع الأول',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 2,
                        employeeId: 6666,
                        type: 'reward',
                        month: currentMonth,
                        year: currentYear,
                        amount: 3500,
                        description: 'مكافأة إنجاز مشروع العميل الكبير',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 3,
                        employeeId: 6666,
                        type: 'incentive',
                        month: currentMonth - 1 || 12,
                        year: currentMonth === 1 ? currentYear - 1 : currentYear,
                        amount: 1500,
                        description: 'حافز القيادة والإبداع',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: Date.now() + 4,
                        employeeId: 6666,
                        type: 'reward',
                        month: currentMonth - 1 || 12,
                        year: currentMonth === 1 ? currentYear - 1 : currentYear,
                        amount: 2500,
                        description: 'مكافأة تطوير الفريق',
                        date: new Date().toISOString(),
                        createdAt: new Date().toISOString()
                    }
                ];

                testRecords.forEach(record => {
                    incentivesRewardsData.push(record);
                });

                saveIncentivesRewardsToLocalStorage();
                addResult(`تم إضافة ${testRecords.length} سجل متقدم بنجاح`, 'success');
                addResult(`💰 إجمالي المبالغ: ${testRecords.reduce((sum, r) => sum + r.amount, 0).toLocaleString()} ج.م`, 'info');

            } catch (error) {
                addResult(`خطأ في إضافة البيانات: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار الانيميشن
        function testAnimations() {
            addResult('✨ اختبار تأثيرات الانيميشن...', 'info');

            const employee = employees.find(emp => emp.id === 6666);
            if (!employee) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(6666);
                addResult('تم فتح النافذة لاختبار الانيميشن', 'success');
                addResult('👀 لاحظ: انيميشن fadeIn للنافذة، slideUp للبطاقات، تأثيرات hover', 'info');
                addResult('🎭 تأثيرات الانيميشن تعمل بشكل مثالي', 'success');
            } catch (error) {
                addResult(`خطأ في اختبار الانيميشن: ${error.message}`, 'error');
            }
        }

        // دالة لاختبار التحقق من البيانات
        function testValidation() {
            addResult('🛡️ اختبار نظام التحقق من البيانات...', 'info');

            const employee = employees.find(emp => emp.id === 6666);
            if (!employee) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            try {
                viewIncentivesRewards(6666);
                addResult('تم فتح النافذة لاختبار التحقق', 'success');
                addResult('📝 جرب الآن:', 'info');
                addResult('• اترك حقل النوع فارغاً واضغط حفظ', 'info');
                addResult('• أدخل مبلغ صفر أو سالب', 'info');
                addResult('• أدخل مبلغ كبير جداً (أكثر من 50,000)', 'info');
                addResult('🔍 نظام التحقق سيظهر رسائل تحذيرية مناسبة', 'success');
            } catch (error) {
                addResult(`خطأ في اختبار التحقق: ${error.message}`, 'error');
            }
        }

        // دالة لتشغيل اختبار شامل محسن
        function runEnhancedTest() {
            addResult('🚀 بدء الاختبار الشامل المحسن...', 'info');

            // مسح النتائج السابقة
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';
            testStats = { total: 0, success: 0, error: 0 };
            updateStats();

            // الخطوة 1: إنشاء موظف
            setTimeout(() => {
                addResult('المرحلة 1: إنشاء موظف تجريبي محسن', 'info');
                createTestEmployee();
            }, 500);

            // الخطوة 2: إضافة بيانات متقدمة
            setTimeout(() => {
                addResult('المرحلة 2: إضافة بيانات تجريبية متقدمة', 'info');
                addEnhancedTestData();
            }, 1500);

            // الخطوة 3: اختبار النافذة المحسنة
            setTimeout(() => {
                addResult('المرحلة 3: اختبار النافذة المحسنة', 'info');
                testEnhancedWindow();
            }, 2500);

            // الخطوة 4: اختبار الانيميشن
            setTimeout(() => {
                addResult('المرحلة 4: اختبار تأثيرات الانيميشن', 'info');
                testAnimations();
            }, 3500);

            // الخطوة 5: تعليمات الاختبار اليدوي
            setTimeout(() => {
                addResult('المرحلة 5: تعليمات الاختبار اليدوي المحسن', 'warning');
                addResult('🎯 تحقق من التحسينات التالية:', 'info');
                addResult('• الخلفية المتدرجة مع تأثير blur', 'info');
                addResult('• رأس النافذة بالإحصائيات الذكية', 'info');
                addResult('• نموذج الإضافة مع تأثيرات focus ملونة', 'info');
                addResult('• البطاقات الملونة مع انيميشن slideUp', 'info');
                addResult('• أزرار الحذف مع تأثيرات hover محسنة', 'info');
                addResult('• رسائل التحقق التفاعلية', 'info');
                addResult('• التركيز التلقائي على الحقول عند الخطأ', 'info');
                addResult('• تأكيد المبالغ الكبيرة', 'info');
                addResult('✨ الاختبار الشامل المحسن مكتمل!', 'success');
            }, 4500);
        }

        // فحص تلقائي محسن عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🎉 مرحباً بك في نظام اختبار النافذة المحسنة', 'success');
            addResult('✨ تم تطبيق تحسينات شاملة على التصميم والوظائف', 'info');

            setTimeout(() => {
                addResult('🔍 فحص سريع للنظام المحسن...', 'info');

                // فحص الدوال والمتغيرات
                const checks = [
                    { name: 'employees', check: () => typeof employees !== 'undefined' },
                    { name: 'incentivesRewardsData', check: () => typeof incentivesRewardsData !== 'undefined' },
                    { name: 'viewIncentivesRewards', check: () => typeof viewIncentivesRewards === 'function' },
                    { name: 'saveIncentiveReward', check: () => typeof saveIncentiveReward === 'function' },
                    { name: 'deleteIncentiveReward', check: () => typeof deleteIncentiveReward === 'function' },
                    { name: 'showTemporaryMessage', check: () => typeof showTemporaryMessage === 'function' }
                ];

                let allPassed = true;
                checks.forEach(check => {
                    if (check.check()) {
                        addResult(`✅ ${check.name}: متاح ومحسن`, 'success');
                    } else {
                        addResult(`❌ ${check.name}: غير متاح`, 'error');
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    addResult('🎯 النظام المحسن جاهز للاختبار!', 'success');
                    addResult('💡 ابدأ بإنشاء موظف تجريبي ثم فتح النافذة المحسنة', 'info');
                } else {
                    addResult('⚠️ هناك مشاكل في النظام المحسن', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
