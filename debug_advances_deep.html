<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص عميق - مشكلة السلف</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .test-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 8px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }

        .test-button.success { background: linear-gradient(135deg, #28a745, #20c997); }
        .test-button.danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .test-button.warning { background: linear-gradient(135deg, #ffc107, #fd7e14); }

        .results {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .status {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .status.success { background: #27ae60; }
        .status.error { background: #e74c3c; }
        .status.warning { background: #f39c12; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microscope"></i> تشخيص عميق لمشكلة السلف</h1>
            <p>فحص شامل ومفصل لمعرفة سبب عدم الحفظ</p>
        </div>

        <div class="status" id="status">جاهز للتشخيص العميق...</div>

        <div class="content">
            <div class="grid">
                <button class="test-button" onclick="step1_systemCheck()">
                    <i class="fas fa-search"></i> 1. فحص النظام
                </button>
                
                <button class="test-button" onclick="step2_createEmployee()">
                    <i class="fas fa-user-plus"></i> 2. إنشاء موظف
                </button>
                
                <button class="test-button" onclick="step3_openWindow()">
                    <i class="fas fa-window-maximize"></i> 3. فتح النافذة
                </button>
                
                <button class="test-button" onclick="step4_simulateSave()">
                    <i class="fas fa-save"></i> 4. محاكاة الحفظ
                </button>
                
                <button class="test-button success" onclick="step5_manualSave()">
                    <i class="fas fa-hand-paper"></i> 5. حفظ يدوي
                </button>
                
                <button class="test-button warning" onclick="step6_directSave()">
                    <i class="fas fa-bolt"></i> 6. حفظ مباشر
                </button>
                
                <button class="test-button danger" onclick="step7_forceSave()">
                    <i class="fas fa-hammer"></i> 7. حفظ بالقوة
                </button>
                
                <button class="test-button" onclick="clearResults()">
                    <i class="fas fa-trash"></i> مسح النتائج
                </button>
            </div>

            <div class="results" id="results"></div>
        </div>
    </div>

    <script src="app.js"></script>

    <script>
        function log(message, color = '#ecf0f1') {
            const results = document.getElementById('results');
            const time = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="color: ${color}; margin-bottom: 3px;">[${time}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // الخطوة 1: فحص النظام
        function step1_systemCheck() {
            updateStatus('جاري فحص النظام...', 'warning');
            log('🔍 === بدء فحص النظام ===', '#3498db');
            
            // فحص المتغيرات الأساسية
            log(`employees: ${typeof employees !== 'undefined' ? '✅ موجود (' + employees.length + ')' : '❌ مفقود'}`, 
                typeof employees !== 'undefined' ? '#27ae60' : '#e74c3c');
            
            log(`advancesDeductionsData: ${typeof advancesDeductionsData !== 'undefined' ? '✅ موجود (' + advancesDeductionsData.length + ')' : '❌ مفقود'}`, 
                typeof advancesDeductionsData !== 'undefined' ? '#27ae60' : '#e74c3c');
            
            // فحص الدوال
            const functions = ['viewAdvancesDeductions', 'saveAdvanceDeduction', 'saveAdvancesDeductionsToLocalStorage'];
            functions.forEach(func => {
                log(`${func}: ${typeof window[func] === 'function' ? '✅ موجودة' : '❌ مفقودة'}`, 
                    typeof window[func] === 'function' ? '#27ae60' : '#e74c3c');
            });
            
            // فحص localStorage
            try {
                const testKey = 'test_' + Date.now();
                localStorage.setItem(testKey, 'test');
                const retrieved = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);
                log(`localStorage: ${retrieved === 'test' ? '✅ يعمل' : '❌ لا يعمل'}`, 
                    retrieved === 'test' ? '#27ae60' : '#e74c3c');
            } catch (error) {
                log(`localStorage: ❌ خطأ - ${error.message}`, '#e74c3c');
            }
            
            updateStatus('تم فحص النظام', 'success');
        }

        // الخطوة 2: إنشاء موظف
        function step2_createEmployee() {
            updateStatus('جاري إنشاء موظف تجريبي...', 'warning');
            log('👤 === إنشاء موظف تجريبي ===', '#3498db');
            
            const testEmployee = {
                id: 99999,
                name: "موظف تشخيص السلف",
                position: "مختبر عميق",
                employeeCode: "DEEP_TEST",
                employmentType: "monthly",
                salary: { basic: 5000 }
            };
            
            // إزالة الموظف إذا كان موجود
            const existingIndex = employees.findIndex(emp => emp.id === 99999);
            if (existingIndex !== -1) {
                employees.splice(existingIndex, 1);
                log('🗑️ تم إزالة الموظف الموجود', '#f39c12');
            }
            
            employees.push(testEmployee);
            log('✅ تم إنشاء الموظف التجريبي', '#27ae60');
            
            // التأكد من إنشاء مصفوفة السلف
            if (typeof advancesDeductionsData === 'undefined') {
                window.advancesDeductionsData = [];
                log('📋 تم إنشاء مصفوفة السلف', '#f39c12');
            }
            
            updateStatus('تم إنشاء الموظف', 'success');
        }

        // الخطوة 3: فتح النافذة
        function step3_openWindow() {
            updateStatus('جاري فتح النافذة...', 'warning');
            log('🪟 === فتح نافذة السلف ===', '#3498db');
            
            try {
                viewAdvancesDeductions(99999);
                log('✅ تم استدعاء دالة فتح النافذة', '#27ae60');
                
                setTimeout(() => {
                    const modal = document.getElementById('advancesDeductionsModal');
                    if (modal) {
                        log('✅ النافذة مفتوحة ومرئية', '#27ae60');
                        updateStatus('النافذة مفتوحة', 'success');
                    } else {
                        log('❌ النافذة غير مرئية', '#e74c3c');
                        updateStatus('النافذة لم تفتح', 'error');
                    }
                }, 500);
                
            } catch (error) {
                log(`❌ خطأ في فتح النافذة: ${error.message}`, '#e74c3c');
                updateStatus('فشل فتح النافذة', 'error');
            }
        }

        // الخطوة 4: محاكاة الحفظ
        function step4_simulateSave() {
            updateStatus('جاري محاكاة الحفظ...', 'warning');
            log('🎭 === محاكاة عملية الحفظ ===', '#3498db');
            
            // إنشاء عناصر النموذج
            const formHTML = `
                <div id="tempForm" style="display: none;">
                    <select id="advanceDeductionType">
                        <option value="advance" selected>سلفة</option>
                    </select>
                    <input id="advanceDeductionAmount" value="2000">
                    <input id="advanceDeductionDescription" value="سلفة تشخيص عميق">
                    <input id="advanceDeductionMonth" value="${new Date().getMonth() + 1}">
                    <input id="advanceDeductionYear" value="${new Date().getFullYear()}">
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', formHTML);
            log('📝 تم إنشاء عناصر النموذج', '#27ae60');
            
            // محاولة الحفظ
            try {
                const beforeCount = advancesDeductionsData.length;
                log(`📊 عدد السجلات قبل الحفظ: ${beforeCount}`, '#3498db');
                
                saveAdvanceDeduction(99999);
                
                setTimeout(() => {
                    const afterCount = advancesDeductionsData.length;
                    log(`📊 عدد السجلات بعد الحفظ: ${afterCount}`, '#3498db');
                    
                    if (afterCount > beforeCount) {
                        log('✅ تم الحفظ في المصفوفة', '#27ae60');
                        updateStatus('تم الحفظ في المصفوفة', 'success');
                    } else {
                        log('❌ لم يتم الحفظ في المصفوفة', '#e74c3c');
                        updateStatus('فشل الحفظ في المصفوفة', 'error');
                    }
                    
                    // إزالة النموذج المؤقت
                    const tempForm = document.getElementById('tempForm');
                    if (tempForm) tempForm.remove();
                }, 1000);
                
            } catch (error) {
                log(`❌ خطأ في محاكاة الحفظ: ${error.message}`, '#e74c3c');
                updateStatus('فشل محاكاة الحفظ', 'error');
            }
        }

        // الخطوة 5: حفظ يدوي
        function step5_manualSave() {
            updateStatus('جاري الحفظ اليدوي...', 'warning');
            log('✋ === حفظ يدوي مباشر ===', '#3498db');
            
            try {
                const record = {
                    id: Date.now(),
                    employeeId: 99999,
                    type: 'advance',
                    month: 12,
                    year: 2024,
                    amount: 3000,
                    description: 'حفظ يدوي مباشر',
                    date: new Date().toISOString()
                };
                
                log('📋 السجل المراد حفظه:', '#3498db');
                log(JSON.stringify(record, null, 2), '#ecf0f1');
                
                const beforeCount = advancesDeductionsData.length;
                advancesDeductionsData.push(record);
                const afterCount = advancesDeductionsData.length;
                
                log(`📊 ${beforeCount} → ${afterCount}`, afterCount > beforeCount ? '#27ae60' : '#e74c3c');
                
                if (afterCount > beforeCount) {
                    log('✅ تم إضافة السجل للمصفوفة', '#27ae60');
                    
                    // حفظ في localStorage
                    saveAdvancesDeductionsToLocalStorage();
                    log('💾 تم استدعاء دالة الحفظ', '#27ae60');
                    
                    // التحقق من الحفظ
                    const saved = localStorage.getItem('oscoAdvancesDeductions');
                    if (saved) {
                        const parsed = JSON.parse(saved);
                        const found = parsed.find(r => r.id === record.id);
                        if (found) {
                            log('✅ تم التحقق من الحفظ في localStorage', '#27ae60');
                            updateStatus('تم الحفظ اليدوي بنجاح', 'success');
                        } else {
                            log('❌ السجل غير موجود في localStorage', '#e74c3c');
                            updateStatus('فشل الحفظ في localStorage', 'error');
                        }
                    } else {
                        log('❌ لا توجد بيانات في localStorage', '#e74c3c');
                        updateStatus('فشل الحفظ', 'error');
                    }
                } else {
                    log('❌ فشل إضافة السجل للمصفوفة', '#e74c3c');
                    updateStatus('فشل الحفظ اليدوي', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الحفظ اليدوي: ${error.message}`, '#e74c3c');
                updateStatus('خطأ في الحفظ اليدوي', 'error');
            }
        }

        // الخطوة 6: حفظ مباشر
        function step6_directSave() {
            updateStatus('جاري الحفظ المباشر...', 'warning');
            log('⚡ === حفظ مباشر في localStorage ===', '#3498db');
            
            try {
                const record = {
                    id: Date.now() + 1000,
                    employeeId: 99999,
                    type: 'deduction',
                    month: 12,
                    year: 2024,
                    amount: 500,
                    description: 'حفظ مباشر',
                    date: new Date().toISOString()
                };
                
                // الحصول على البيانات الحالية
                let currentData = [];
                const existing = localStorage.getItem('oscoAdvancesDeductions');
                if (existing) {
                    currentData = JSON.parse(existing);
                }
                
                log(`📊 البيانات الحالية: ${currentData.length} سجل`, '#3498db');
                
                // إضافة السجل الجديد
                currentData.push(record);
                
                // حفظ مباشر
                localStorage.setItem('oscoAdvancesDeductions', JSON.stringify(currentData));
                log('💾 تم الحفظ المباشر في localStorage', '#27ae60');
                
                // التحقق
                const verified = localStorage.getItem('oscoAdvancesDeductions');
                if (verified) {
                    const parsed = JSON.parse(verified);
                    const found = parsed.find(r => r.id === record.id);
                    if (found) {
                        log('✅ تم التحقق من الحفظ المباشر', '#27ae60');
                        
                        // تحديث المصفوفة في الذاكرة
                        advancesDeductionsData = parsed;
                        log('🔄 تم تحديث المصفوفة في الذاكرة', '#27ae60');
                        
                        updateStatus('تم الحفظ المباشر بنجاح', 'success');
                    } else {
                        log('❌ السجل غير موجود بعد الحفظ المباشر', '#e74c3c');
                        updateStatus('فشل الحفظ المباشر', 'error');
                    }
                } else {
                    log('❌ فشل الحفظ المباشر', '#e74c3c');
                    updateStatus('فشل الحفظ المباشر', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الحفظ المباشر: ${error.message}`, '#e74c3c');
                updateStatus('خطأ في الحفظ المباشر', 'error');
            }
        }

        // الخطوة 7: حفظ بالقوة
        function step7_forceSave() {
            updateStatus('جاري الحفظ بالقوة...', 'warning');
            log('🔨 === حفظ بالقوة (تجاوز كل شيء) ===', '#3498db');
            
            try {
                // إنشاء دالة حفظ بديلة
                window.forceSaveAdvance = function(employeeId, type, amount, description) {
                    log('🚀 بدء الحفظ بالقوة...', '#e67e22');
                    
                    const record = {
                        id: Date.now() + 2000,
                        employeeId: parseInt(employeeId),
                        type: type,
                        month: 12,
                        year: 2024,
                        amount: parseFloat(amount),
                        description: description,
                        date: new Date().toISOString()
                    };
                    
                    // التأكد من وجود المصفوفة
                    if (!window.advancesDeductionsData) {
                        window.advancesDeductionsData = [];
                        log('📋 تم إنشاء مصفوفة جديدة', '#f39c12');
                    }
                    
                    // إضافة السجل
                    window.advancesDeductionsData.push(record);
                    log('✅ تم إضافة السجل للمصفوفة', '#27ae60');
                    
                    // حفظ مباشر
                    try {
                        localStorage.setItem('oscoAdvancesDeductions', JSON.stringify(window.advancesDeductionsData));
                        log('💾 تم الحفظ بالقوة في localStorage', '#27ae60');
                        
                        // التحقق النهائي
                        const final = localStorage.getItem('oscoAdvancesDeductions');
                        if (final) {
                            const finalParsed = JSON.parse(final);
                            log(`🎉 النتيجة النهائية: ${finalParsed.length} سجل محفوظ`, '#27ae60');
                            return true;
                        }
                    } catch (saveError) {
                        log(`❌ خطأ في الحفظ بالقوة: ${saveError.message}`, '#e74c3c');
                        return false;
                    }
                    
                    return false;
                };
                
                // تنفيذ الحفظ بالقوة
                const success = forceSaveAdvance(99999, 'advance', 4000, 'حفظ بالقوة');
                
                if (success) {
                    updateStatus('تم الحفظ بالقوة بنجاح!', 'success');
                    log('🏆 تم الحفظ بالقوة بنجاح!', '#27ae60');
                } else {
                    updateStatus('فشل الحفظ بالقوة', 'error');
                    log('💥 فشل الحفظ بالقوة', '#e74c3c');
                }
                
            } catch (error) {
                log(`❌ خطأ في الحفظ بالقوة: ${error.message}`, '#e74c3c');
                updateStatus('خطأ في الحفظ بالقوة', 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('تم مسح النتائج', 'info');
        }

        // بدء تلقائي
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🚀 بدء التشخيص العميق...', '#3498db');
                step1_systemCheck();
            }, 1000);
        });
    </script>
</body>
</html>
