<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نافذة حساب المرتبات البسيطة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #ddd;
        }

        .header {
            background: #f8f9fa;
            color: #333;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }

        .header h1 {
            font-size: 20px;
            margin-bottom: 8px;
            color: #333;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-button {
            background: #333;
            color: white;
            border: 1px solid #333;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            margin: 4px;
            transition: background 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .test-button:hover {
            background: #555;
        }

        .test-button.secondary {
            background: #666;
            border-color: #666;
        }

        .test-button.secondary:hover {
            background: #777;
        }

        .test-button.warning {
            background: #888;
            border-color: #888;
        }

        .test-button.warning:hover {
            background: #999;
        }

        .test-button.info {
            background: #555;
            border-color: #555;
        }

        .test-button.info:hover {
            background: #666;
        }

        .results-container {
            background: white;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #ddd;
            max-height: 350px;
            overflow-y: auto;
        }

        .test-result {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 3px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-result.success {
            background: #f0f8f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .test-result.error {
            background: #f8f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .test-result.warning {
            background: #f8f8f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .test-result.info {
            background: #f0f8f8;
            color: #333;
            border: 1px solid #ddd;
        }

        .status {
            padding: 6px 10px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            margin: 4px 0;
        }

        .status.success {
            background: #f0f8f0;
            color: #333;
        }

        .status.error {
            background: #f8f0f0;
            color: #333;
        }

        .status.warning {
            background: #f8f8f0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>اختبار نافذة حساب المرتبات البسيطة</h1>
            <p>اختبار نافذة حساب المرتبات الجديدة مع عرض تفاصيل المشروعات</p>
        </div>

        <div class="content">
            <!-- قسم إنشاء البيانات التجريبية -->
            <div class="test-section">
                <h3>إعداد البيانات التجريبية</h3>
                <button class="test-button" onclick="createTestEmployee()">
                    إنشاء موظف تجريبي
                </button>
                <button class="test-button" onclick="addTestWorkDays()">
                    إضافة أيام عمل
                </button>
                <button class="test-button" onclick="addTestIncentives()">
                    إضافة حوافز
                </button>
                <button class="test-button" onclick="addTestAdvances()">
                    إضافة سلف وخصومات
                </button>
            </div>

            <!-- قسم اختبار النافذة -->
            <div class="test-section">
                <h3>اختبار نافذة حساب المرتبات</h3>
                <button class="test-button info" onclick="testSalaryCalculator()">
                    اختبار النافذة
                </button>
                <button class="test-button warning" onclick="testWithMultipleProjects()">
                    اختبار مع مشروعات متعددة
                </button>
                <button class="test-button secondary" onclick="clearResults()">
                    مسح النتائج
                </button>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="results-container">
                <h3>سجل نتائج الاختبارات</h3>
                <div id="test-results">
                    <div class="test-result info">جاهز لبدء اختبار نافذة حساب المرتبات...</div>
                </div>
            </div>

            <!-- حالة الاختبارات -->
            <div style="margin-top: 15px;">
                <div id="employee-status" class="status">في انتظار إنشاء الموظف التجريبي...</div>
                <div id="data-status" class="status">في انتظار إضافة البيانات...</div>
                <div id="calculator-status" class="status">في انتظار اختبار النافذة...</div>
            </div>
        </div>
    </div>

    <!-- تضمين ملف JavaScript الأصلي -->
    <script src="app.js"></script>

    <script>
        // متغير لحفظ معرف الموظف التجريبي
        let testEmployeeId = null;

        // دالة لإضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;

            const icon = type === 'success' ? '✓' :
                        type === 'error' ? '✗' :
                        type === 'warning' ? '!' : '•';

            resultDiv.innerHTML = `${icon} ${message}`;
            resultsContainer.appendChild(resultDiv);

            // التمرير إلى أسفل
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }

        // دالة لتحديث حالة الاختبار
        function updateStatus(statusId, success, message) {
            const statusElement = document.getElementById(statusId);
            if (statusElement) {
                statusElement.className = `status ${success ? 'success' : 'error'}`;
                statusElement.textContent = message;
            }
        }

        // دالة لإنشاء موظف تجريبي
        function createTestEmployee() {
            addResult('إنشاء موظف تجريبي...', 'info');

            const testEmployee = {
                id: 9999,
                name: 'أحمد محمد علي',
                position: 'مهندس مدني',
                employeeCode: 'ENG-001',
                employmentType: 'daily',
                dailyWage: 200,
                basicSalary: 0,
                photo: 'https://via.placeholder.com/100x100/007bff/ffffff?text=AM',
                salary: {
                    basic: 0,
                    allowances: {
                        total: 500
                    },
                    dailyAllowances: {
                        expatDaily: 50,
                        transportDaily: 30
                    }
                },
                isActive: true
            };

            // إضافة الموظف إلى المصفوفة
            const existingIndex = employees.findIndex(emp => emp.id === 9999);
            if (existingIndex !== -1) {
                employees[existingIndex] = testEmployee;
                addResult('تم تحديث الموظف التجريبي', 'success');
            } else {
                employees.push(testEmployee);
                addResult('تم إنشاء الموظف التجريبي بنجاح', 'success');
            }

            testEmployeeId = 9999;
            updateStatus('employee-status', true, 'تم إنشاء الموظف التجريبي');

            // حفظ البيانات
            saveEmployeesToLocalStorage();
            addResult('تم حفظ بيانات الموظف', 'success');
        }

        // دالة لإضافة أيام عمل تجريبية
        function addTestWorkDays() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة أيام عمل تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            // إضافة أيام عمل لمشروعات مختلفة
            const workDaysRecords = [
                {
                    id: Date.now() + 1,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    projectId: 'proj1',
                    projectName: 'مشروع البرج السكني',
                    scheduledDays: 15,
                    actualDays: 14,
                    overtimeHours: 8,
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    projectId: 'proj2',
                    projectName: 'مشروع المجمع التجاري',
                    scheduledDays: 12,
                    actualDays: 12,
                    overtimeHours: 4,
                    createdAt: new Date().toISOString()
                }
            ];

            // إضافة السجلات
            workDaysRecords.forEach(record => {
                const existingIndex = employeeDaysData.findIndex(r => 
                    r.employeeId === record.employeeId && 
                    r.projectId === record.projectId &&
                    r.month === record.month &&
                    r.year === record.year
                );

                if (existingIndex !== -1) {
                    employeeDaysData[existingIndex] = record;
                } else {
                    employeeDaysData.push(record);
                }
            });

            addResult('تم إضافة أيام العمل لمشروعين', 'success');
            addResult(`المشروع الأول: ${workDaysRecords[0].actualDays} يوم + ${workDaysRecords[0].overtimeHours} ساعة إضافية`, 'info');
            addResult(`المشروع الثاني: ${workDaysRecords[1].actualDays} يوم + ${workDaysRecords[1].overtimeHours} ساعة إضافية`, 'info');

            updateStatus('data-status', true, 'تم إضافة أيام العمل');
        }

        // دالة لإضافة حوافز تجريبية
        function addTestIncentives() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة حوافز ومكافآت تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const incentiveRecord = {
                id: Date.now(),
                employeeId: testEmployeeId,
                month: currentMonth,
                year: currentYear,
                category: 'incentive',
                type: 'production',
                amount: 800,
                status: 'active',
                description: 'حافز إنتاج شهري',
                createdAt: new Date().toISOString()
            };

            incentivesData.push(incentiveRecord);
            addResult('تم إضافة حافز إنتاج بقيمة 800 ج.م', 'success');
        }

        // دالة لإضافة سلف وخصومات تجريبية
        function addTestAdvances() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('إضافة سلف وخصومات تجريبية...', 'info');

            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const records = [
                {
                    id: Date.now() + 1,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    type: 'advance',
                    amount: 1000,
                    date: currentDate.toISOString().split('T')[0],
                    notes: 'سلفة شهرية',
                    createdAt: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    employeeId: testEmployeeId,
                    month: currentMonth,
                    year: currentYear,
                    type: 'deduction',
                    deductionSubtype: 'days',
                    amount: 200,
                    date: currentDate.toISOString().split('T')[0],
                    notes: 'خصم غياب',
                    createdAt: new Date().toISOString()
                }
            ];

            records.forEach(record => advancesDeductionsData.push(record));
            addResult('تم إضافة سلفة 1000 ج.م وخصم 200 ج.م', 'success');
        }

        // دالة لاختبار نافذة حساب المرتبات
        function testSalaryCalculator() {
            if (!testEmployeeId) {
                addResult('يرجى إنشاء الموظف التجريبي أولاً', 'error');
                return;
            }

            addResult('اختبار نافذة حساب المرتبات...', 'info');

            try {
                // فتح النافذة
                openSalaryCalculator(testEmployeeId);
                addResult('تم فتح نافذة حساب المرتبات بنجاح', 'success');

                // التحقق من وجود النافذة
                setTimeout(() => {
                    const modal = document.getElementById('salaryCalculatorModal');
                    if (modal) {
                        addResult('النافذة موجودة ومعروضة بشكل صحيح', 'success');
                        updateStatus('calculator-status', true, 'النافذة تعمل بشكل صحيح');
                    } else {
                        addResult('فشل في العثور على النافذة', 'error');
                        updateStatus('calculator-status', false, 'فشل في فتح النافذة');
                    }
                }, 500);

            } catch (error) {
                addResult(`خطأ في فتح النافذة: ${error.message}`, 'error');
                updateStatus('calculator-status', false, 'خطأ في النافذة');
            }
        }

        // دالة لاختبار مع مشروعات متعددة
        function testWithMultipleProjects() {
            addResult('إضافة مشروعات إضافية للاختبار...', 'info');

            // إضافة مشروع ثالث
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            const additionalRecord = {
                id: Date.now() + 3,
                employeeId: testEmployeeId,
                month: currentMonth,
                year: currentYear,
                projectId: 'proj3',
                projectName: 'مشروع الفيلا الخاصة',
                scheduledDays: 8,
                actualDays: 8,
                overtimeHours: 2,
                createdAt: new Date().toISOString()
            };

            employeeDaysData.push(additionalRecord);
            addResult('تم إضافة مشروع ثالث', 'success');

            // فتح النافذة مرة أخرى
            setTimeout(() => {
                testSalaryCalculator();
            }, 500);
        }

        // دالة لمسح النتائج
        function clearResults() {
            document.getElementById('test-results').innerHTML =
                '<div class="test-result info">جاهز لبدء اختبار نافذة حساب المرتبات...</div>';

            // إعادة تعيين حالة الاختبارات
            updateStatus('employee-status', false, 'في انتظار إنشاء الموظف التجريبي...');
            updateStatus('data-status', false, 'في انتظار إضافة البيانات...');
            updateStatus('calculator-status', false, 'في انتظار اختبار النافذة...');
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('مرحباً بك في نظام اختبار نافذة حساب المرتبات', 'info');
            addResult('يمكنك الآن إنشاء البيانات التجريبية واختبار النافذة', 'info');
        });
    </script>
</body>
</html>
